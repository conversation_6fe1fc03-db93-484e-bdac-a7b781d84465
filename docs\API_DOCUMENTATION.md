# BulletHell_RougeLike_RPG API Documentation

## Overview

This document provides comprehensive API documentation for the BulletHell_RougeLike_RPG game engine and systems.

## Table of Contents

1. [Core Systems](#core-systems)
2. [Game Entities](#game-entities)
3. [Utilities](#utilities)
4. [Configuration](#configuration)
5. [Type Definitions](#type-definitions)

## Core Systems

### Game Class

The main game orchestrator that manages all systems and the game loop.

```typescript
class Game {
  constructor(systems: GameSystems)
  async initialize(): Promise<void>
  start(): void
  stop(): void
  pause(): void
  resume(): void
  destroy(): void
}
```

**Key Features:**
- Fixed timestep game loop (60 FPS)
- Variable rendering for smooth visuals
- Automatic performance monitoring
- System lifecycle management

### CanvasRenderer

High-performance 2D rendering system with optimization features.

```typescript
class CanvasRenderer {
  constructor(canvas: HTMLCanvasElement, config: CanvasConfig)
  clear(color?: string): void
  drawRect(x: number, y: number, width: number, height: number, color: string): void
  drawCircle(x: number, y: number, radius: number, color: string): void
  drawText(text: string, x: number, y: number, font: string, color: string): void
}
```

**Features:**
- Hardware-accelerated rendering
- Automatic batching for performance
- Built-in shape and text rendering
- Transform stack management

### InputManager

Unified input handling for keyboard, mouse, and touch events.

```typescript
class InputManager {
  constructor(canvas: HTMLCanvasElement)
  isKeyDown(key: string): boolean
  isKeyPressed(key: string): boolean
  isMouseButtonDown(button: number): boolean
  getMousePosition(): Vector2
}
```

**Supported Input Types:**
- Keyboard (with key repeat handling)
- Mouse (with button states and position)
- Touch (with gesture recognition)
- Gamepad (with analog stick support)

### SceneManager

Manages game scenes and transitions between different game states.

```typescript
class SceneManager {
  addScene(id: string, scene: BaseScene): void
  switchTo(sceneId: string): void
  getCurrentScene(): BaseScene | null
  update(deltaTime: number): void
  render(renderer: CanvasRenderer, alpha: number): void
}
```

**Scene Types:**
- MenuScene: Main menu and UI screens
- GameScene: Active gameplay
- LoadingScene: Asset loading screens
- PauseScene: Game pause overlay

## Game Entities

### Player

The player character with stats, abilities, and progression.

```typescript
class Player extends BaseEntity {
  stats: PlayerStats
  abilities: PlayerAbilities
  position: Vector2
  
  update(deltaTime: number): void
  takeDamage(amount: number): void
  useAbility(abilityName: string): boolean
}
```

**Player Stats:**
- Health/MaxHealth
- Mana/MaxMana
- Damage, Speed, Defense
- Critical Chance/Multiplier

### Enemy

AI-controlled entities with various behavior patterns.

```typescript
class Enemy extends BaseEntity {
  config: EnemyConfig
  aiState: EnemyAIState
  
  update(deltaTime: number, player: Player, obstacles: Obstacle[]): void
  takeDamage(amount: number): void
  attack(target: Entity): void
}
```

**AI Behaviors:**
- Aggressive: Direct pursuit and attack
- Defensive: Cautious approach with retreat
- Patrol: Fixed path movement
- Ranged: Keep distance and shoot
- Swarm: Coordinate with other enemies

### Projectile

Bullets, missiles, and other projectiles with physics.

```typescript
class Projectile extends BaseEntity {
  velocity: Vector2
  damage: number
  owner: EntityType
  
  update(deltaTime: number): void
  onCollision(entity: Entity): void
}
```

**Projectile Types:**
- Bullet: Fast, straight-line projectiles
- Missile: Homing projectiles with guidance
- Laser: Instant hit-scan weapons
- Magic: Special effect projectiles

## Utilities

### MathUtils

Mathematical utility functions for game calculations.

```typescript
class MathUtils {
  static clamp(value: number, min: number, max: number): number
  static lerp(a: number, b: number, t: number): number
  static distance(x1: number, y1: number, x2: number, y2: number): number
  static normalizeAngle(angle: number): number
  static random(min?: number, max?: number): number
}
```

### RenderUtils

Rendering utility functions for common drawing operations.

```typescript
class RenderUtils {
  static drawHealthBar(renderer: CanvasRenderer, x: number, y: number, width: number, height: number, current: number, max: number): void
  static drawProgressBar(renderer: CanvasRenderer, x: number, y: number, width: number, height: number, progress: number): void
  static drawTextWithOutline(renderer: CanvasRenderer, text: string, x: number, y: number, textColor: string, outlineColor: string): void
}
```

### EntityUtils

Entity management and AI utility functions.

```typescript
class EntityUtils {
  static applyDamage(stats: EntityStats, damage: number, isCritical: boolean): DamageResult
  static calculateExperienceGain(baseExp: number, playerLevel: number, enemyLevel: number): number
  static updateMovement(movement: MovementState, input: Vector2, deltaTime: number): void
  static hasLineOfSight(from: Vector2, to: Vector2, obstacles: Obstacle[]): boolean
}
```

## Configuration

### GameConfig

Main game configuration interface.

```typescript
interface GameConfig {
  canvas: CanvasConfig
  debug: DebugConfig
  performance: PerformanceConfig
  audio: AudioConfig
  graphics: GraphicsConfig
}
```

### Performance Settings

```typescript
interface PerformanceConfig {
  maxEntities: number        // Maximum entities (default: 1000)
  maxParticles: number       // Maximum particles (default: 500)
  collisionBudget: number    // Max collision checks per frame (default: 500)
  renderBudget: number       // Max render time in ms (default: 16)
  enableProfiling: boolean   // Enable performance profiling (default: false)
}
```

## Type Definitions

### Common Types

```typescript
type EntityId = Brand<string, 'EntityId'>
type Timestamp = Brand<number, 'Timestamp'>
type Milliseconds = Brand<number, 'Milliseconds'>

interface Point2D {
  readonly x: number
  readonly y: number
}

interface Rectangle extends Point2D {
  readonly width: number
  readonly height: number
}
```

### Entity Types

```typescript
interface EntityStats {
  health: number
  maxHealth: number
  damage: number
  speed: number
  defense?: number
  criticalChance?: number
  criticalMultiplier?: number
}

interface EntityTransform {
  position: Point2D
  rotation: number
  scale: Point2D
}
```

## Error Handling

The engine provides comprehensive error handling with different severity levels:

```typescript
type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical'

interface GameError {
  id: string
  message: string
  severity: ErrorSeverity
  timestamp: Timestamp
  stack?: string
  context?: Record<string, unknown>
}
```

## Performance Optimization

### Collision Detection

The engine uses optimized spatial partitioning for collision detection:

- **Spatial Grid**: Divides world into cells for broad-phase detection
- **Performance Budget**: Limits collision checks per frame
- **Object Pooling**: Reuses objects to reduce garbage collection

### Rendering Optimization

- **Frustum Culling**: Only renders visible entities
- **Batching**: Groups similar draw calls
- **Level of Detail**: Reduces detail for distant objects

### Memory Management

- **Object Pooling**: Reuses frequently created/destroyed objects
- **Automatic Cleanup**: Removes inactive entities and particles
- **Memory Monitoring**: Tracks memory usage and warns of leaks

## Best Practices

### Entity Creation

```typescript
// Good: Use entity factories
const enemy = EnemyFactory.create({
  type: 'basic',
  position: new Vector2(100, 100),
  level: 5
});

// Avoid: Direct instantiation without proper setup
const enemy = new Enemy(); // Missing required configuration
```

### Performance Considerations

```typescript
// Good: Use object pooling for frequently created objects
const bullet = BulletPool.acquire();
bullet.initialize(position, velocity, damage);

// Good: Batch similar operations
renderer.beginBatch();
entities.forEach(entity => entity.render(renderer));
renderer.endBatch();

// Avoid: Creating objects in update loops
function update() {
  const temp = new Vector2(); // Creates garbage every frame
}
```

### Error Handling

```typescript
// Good: Use safe operations with error handling
const result = SafeOperations.safePropertyAccess(config, 'player.stats.health', 100);

// Good: Validate input before processing
const validation = InputValidator.validatePlayerInput(input);
if (validation.isValid) {
  processInput(validation.sanitizedValue);
}
```

## Migration Guide

### From Version 0.x to 1.0

1. **Update Configuration**: New configuration structure with validation
2. **Entity System**: Entities now use component-based architecture
3. **Type Safety**: Strict TypeScript types replace any types
4. **Performance**: New optimized collision and rendering systems

### Breaking Changes

- `Game.initialize()` is now async and returns a Promise
- Entity constructors require configuration objects
- Input handling uses new action-based system
- Scene transitions use string IDs instead of class references

## Contributing

When contributing to the codebase:

1. **Documentation**: Add JSDoc comments to all public APIs
2. **Type Safety**: Use strict TypeScript types, avoid `any`
3. **Testing**: Include unit tests with 70%+ coverage
4. **Performance**: Consider performance impact of changes
5. **Error Handling**: Use proper error handling and validation

For more information, see the [Contributing Guide](../CONTRIBUTING.md).
