# 🛠️ Developer Guide - Ultimate Bullet Hell RPG

This guide provides detailed information for developers who want to understand, modify, or extend the game.

## 📋 Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Core Systems](#core-systems)
3. [Adding New Features](#adding-new-features)
4. [Performance Guidelines](#performance-guidelines)
5. [Testing & Debugging](#testing--debugging)
6. [Best Practices](#best-practices)

## 🏗️ Architecture Overview

### Design Philosophy

The game follows these core principles:

- **Performance First**: 60 FPS is non-negotiable
- **Memory Efficiency**: Zero allocation during gameplay
- **Modularity**: Systems are loosely coupled and easily testable
- **Error Resilience**: Graceful degradation and recovery
- **Cross-Platform**: Works on desktop and mobile devices

### System Dependencies

```mermaid
graph TD
    A[UltimateGame] --> B[PerformanceMonitor]
    A --> C[AudioSystem]
    A --> D[TouchManager]
    A --> E[VirtualControls]
    A --> F[ResponsiveManager]
    A --> G[GameStateManager]
    A --> H[ObjectPoolManager]
    A --> I[CombatSystem]
    A --> J[CollisionSystem]
    A --> K[ProgressionSystem]
```

## 🔧 Core Systems

### Object Pooling System

**Location**: `src/engine/core/ObjectPool.ts`

**Purpose**: Eliminates garbage collection during gameplay

```typescript
// Creating a pool
poolManager.createPool('bullets', () => new Bullet(), 200);

// Getting from pool
const bullet = poolManager.getFromPool('bullets');

// Returning to pool
poolManager.returnToPool('bullets', bullet);
```

**Key Points**:
- Pre-allocates objects at startup
- Automatic growth when pool is exhausted
- Statistics tracking for optimization
- Generic implementation for any poolable object

### Performance Monitoring

**Location**: `src/engine/core/PerformanceMonitor.ts`

**Features**:
- Real-time FPS tracking
- Memory usage monitoring
- Profiler for code sections
- Performance alerts
- Export data for analysis

```typescript
// Profiling code sections
performanceMonitor.startProfile('update');
// ... code to profile
performanceMonitor.endProfile('update');

// Getting metrics
const metrics = performanceMonitor.getCurrentMetrics();
console.log(`FPS: ${metrics.fps}, Memory: ${metrics.memoryUsage}MB`);
```

### Audio System

**Location**: `src/game/systems/AudioSystem.ts`

**Features**:
- Procedural sound generation
- Spatial audio positioning
- Category-based volume control
- Web Audio API integration

```typescript
// Playing positioned sound
audioSystem.playWeaponSound('bow', playerPosition, isCritical);

// Setting listener position
audioSystem.setListenerPosition(playerPosition);
```

### Error Handling

**Location**: `src/utils/error/ErrorHandler.ts`

**Features**:
- Global error catching
- Recovery strategies
- User-friendly error messages
- Error reporting and analytics

```typescript
// Logging errors
errorHandler.error('Something went wrong', { context: 'combat' });

// Adding recovery strategy
errorHandler.addRecoveryStrategy({
  name: 'audio_recovery',
  canRecover: (error) => error.context === 'audio',
  recover: async (error) => { /* recovery logic */ }
});
```

## ➕ Adding New Features

### Adding a New Weapon

1. **Define weapon data** in `CombatSystem.ts`:
```typescript
const newWeapon = {
  name: 'Plasma Cannon',
  damage: 75,
  rate: 2,
  projectileSpeed: 800,
  special: 'plasma_burst'
};
```

2. **Implement special behavior** in bullet update logic:
```typescript
if (this.special === 'plasma_burst') {
  // Custom plasma behavior
}
```

3. **Add audio feedback**:
```typescript
audioSystem.weaponSounds.set('plasma_cannon', 'plasma_sound');
```

### Adding a New Enemy Type

1. **Create enemy class** extending `Enemy`:
```typescript
class PlasmaEnemy extends Enemy {
  protected updateAI(deltaTime: number, playerPosition: Vector2): void {
    // Custom AI behavior
    this.shootPlasmaPattern(playerPosition);
  }
  
  private shootPlasmaPattern(target: Vector2): void {
    // Implement unique bullet pattern
  }
}
```

2. **Register in enemy factory**:
```typescript
enemyFactory.register('plasma', () => new PlasmaEnemy());
```

3. **Add to spawn tables**:
```typescript
spawnTable.add('plasma', { weight: 10, minLevel: 5 });
```

### Adding a New Skill

1. **Define skill data**:
```typescript
const newSkill = {
  id: 'plasma_mastery',
  name: 'Plasma Mastery',
  description: 'Increases plasma weapon damage',
  tree: 'combat',
  maxLevel: 5,
  prerequisites: ['weapon_mastery'],
  cost: (level) => level * 3,
  effect: (level) => ({ plasmaDamage: level * 0.2 })
};
```

2. **Add to skill tree**:
```typescript
skillTree.addSkill('combat', newSkill);
```

3. **Implement effect application**:
```typescript
applySkillEffects(character, skills) {
  if (skills.has('plasma_mastery')) {
    character.plasmaDamage *= (1 + skills.get('plasma_mastery') * 0.2);
  }
}
```

## ⚡ Performance Guidelines

### Memory Management

**Do:**
- Use object pools for frequently created/destroyed objects
- Reuse Vector2 instances where possible
- Clear arrays with `array.length = 0` instead of creating new arrays
- Use `const` for objects that won't be reassigned

**Don't:**
- Create new objects in update loops
- Use `delete` operator on objects
- Create closures in hot code paths
- Allocate large arrays during gameplay

### Rendering Optimization

**Techniques:**
- Frustum culling for off-screen objects
- Batch similar draw calls
- Use canvas transforms efficiently
- Minimize state changes

```typescript
// Good: Batch similar operations
renderer.setFillStyle('#ff0000');
for (const enemy of enemies) {
  enemy.render(renderer);
}

// Bad: Frequent state changes
for (const enemy of enemies) {
  renderer.setFillStyle(enemy.color);
  enemy.render(renderer);
}
```

### Update Loop Optimization

**Best Practices:**
- Use spatial partitioning for collision detection
- Implement early exits in expensive operations
- Cache expensive calculations
- Use fixed timestep for physics

```typescript
// Spatial partitioning example
const nearbyEnemies = spatialGrid.getObjectsInRadius(player.position, 100);
for (const enemy of nearbyEnemies) {
  // Only check collision with nearby enemies
}
```

## 🧪 Testing & Debugging

### Performance Testing

1. **Enable performance monitoring**:
```typescript
const config = { debug: { logPerformance: true } };
```

2. **Monitor key metrics**:
- FPS should stay above 55
- Frame time should be under 16.67ms
- Memory usage should be stable
- Object count should be reasonable

3. **Use browser dev tools**:
- Performance tab for profiling
- Memory tab for leak detection
- Console for error monitoring

### Debug Features

**Performance Display** (F3):
- Real-time FPS and frame time
- Memory usage tracking
- Object count monitoring
- Profiler data visualization

**Error Console**:
- Detailed error messages
- Stack traces for debugging
- Recovery attempt logs
- Performance alerts

### Common Issues

**Low FPS**:
- Check object pool sizes
- Monitor garbage collection
- Profile expensive operations
- Verify spatial partitioning

**Memory Leaks**:
- Ensure objects are returned to pools
- Check for circular references
- Monitor event listener cleanup
- Verify array clearing

**Audio Issues**:
- Check Web Audio API support
- Verify user interaction for audio context
- Monitor audio buffer loading
- Check spatial audio calculations

## 📝 Best Practices

### Code Organization

**File Structure**:
- One class per file
- Group related functionality
- Use clear, descriptive names
- Maintain consistent indentation

**Naming Conventions**:
- Classes: PascalCase (`PlayerController`)
- Methods: camelCase (`updatePosition`)
- Constants: UPPER_SNAKE_CASE (`MAX_HEALTH`)
- Private members: prefix with underscore (`_internalState`)

### Error Handling

**Always handle errors gracefully**:
```typescript
try {
  riskyOperation();
} catch (error) {
  errorHandler.error('Operation failed', error, 'context');
  // Provide fallback behavior
  fallbackOperation();
}
```

**Use safe operations**:
```typescript
// Instead of direct array access
const item = SafeOperations.safeArrayAccess(array, index, defaultValue);

// Instead of direct property access
const value = SafeOperations.safePropertyAccess(obj, 'nested.property', defaultValue);
```

### Performance Monitoring

**Profile critical sections**:
```typescript
performanceMonitor.startProfile('collision_detection');
// Collision detection code
performanceMonitor.endProfile('collision_detection');
```

**Monitor resource usage**:
```typescript
performanceMonitor.recordObjectCount(totalObjects);
performanceMonitor.recordDrawCalls(drawCallCount);
```

### Documentation

**Use JSDoc for public APIs**:
```typescript
/**
 * Creates a new bullet with the specified parameters
 * @param position - Starting position of the bullet
 * @param velocity - Velocity vector for movement
 * @param damage - Damage value for collision
 * @returns The created bullet instance
 */
createBullet(position: Vector2, velocity: Vector2, damage: number): Bullet {
  // Implementation
}
```

**Comment complex algorithms**:
```typescript
// Use minimum spanning tree to connect rooms optimally
const mst = this.generateMinimumSpanningTree(rooms);

// Generate L-shaped corridors between connected rooms
for (const connection of mst) {
  this.createLShapedCorridor(connection.from, connection.to);
}
```

## 🔄 Continuous Integration

### Code Quality Checks

1. **Type checking**: Ensure all TypeScript compiles without errors
2. **Performance testing**: Verify 60 FPS in all demos
3. **Memory testing**: Check for memory leaks during extended play
4. **Cross-platform testing**: Test on desktop and mobile devices

### Release Process

1. **Test all demos** thoroughly
2. **Update documentation** with new features
3. **Check performance metrics** meet standards
4. **Verify error handling** works correctly
5. **Test on multiple browsers** and devices

---

This guide should help you understand and extend the game effectively. For specific questions, refer to the code documentation or create an issue in the repository.
