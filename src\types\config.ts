/**
 * Strict type definitions for configuration objects
 */

import { Milliseconds, Brand } from './common';

// Configuration validation
export type ConfigValue = string | number | boolean | ConfigObject | ConfigArray;
export interface ConfigObject {
  readonly [key: string]: ConfigValue;
}
export interface ConfigArray extends ReadonlyArray<ConfigValue> {}

// Canvas configuration
export interface CanvasConfig {
  readonly width: number;
  readonly height: number;
  readonly targetFPS: number;
  readonly pixelRatio: number;
  readonly antialias: boolean;
  readonly alpha: boolean;
  readonly preserveDrawingBuffer: boolean;
}

// Debug configuration
export interface DebugConfig {
  readonly enabled: boolean;
  readonly showFPS: boolean;
  readonly showColliders: boolean;
  readonly showSpatialGrid: boolean;
  readonly logPerformance: boolean;
  readonly enableConsole: boolean;
  readonly verboseLogging: boolean;
}

// Performance configuration
export interface PerformanceConfig {
  readonly maxEntities: number;
  readonly maxParticles: number;
  readonly collisionBudget: number;
  readonly renderBudget: Milliseconds;
  readonly updateBudget: Milliseconds;
  readonly enableProfiling: boolean;
  readonly autoOptimize: boolean;
}

// Audio configuration
export interface AudioConfig {
  readonly enabled: boolean;
  readonly masterVolume: number; // 0-1
  readonly musicVolume: number; // 0-1
  readonly sfxVolume: number; // 0-1
  readonly maxConcurrentSounds: number;
  readonly audioContext: 'auto' | 'web' | 'webaudio';
}

// Graphics configuration
export type ShadowQuality = 'off' | 'low' | 'medium' | 'high';
export type TextureQuality = 'low' | 'medium' | 'high';

export interface GraphicsConfig {
  readonly particleCount: number;
  readonly shadowQuality: ShadowQuality;
  readonly textureQuality: TextureQuality;
  readonly antiAliasing: boolean;
  readonly vsync: boolean;
  readonly enableBloom: boolean;
  readonly enableMotionBlur: boolean;
}

// Input configuration
export interface InputConfig {
  readonly keyboardEnabled: boolean;
  readonly mouseEnabled: boolean;
  readonly touchEnabled: boolean;
  readonly gamepadEnabled: boolean;
  readonly mouseSensitivity: number;
  readonly keyRepeatDelay: Milliseconds;
  readonly keyRepeatRate: Milliseconds;
}

// Game configuration
export interface GameConfig {
  readonly canvas: CanvasConfig;
  readonly debug: DebugConfig;
  readonly performance: PerformanceConfig;
  readonly audio: AudioConfig;
  readonly graphics: GraphicsConfig;
  readonly input: InputConfig;
}

// Player configuration
export interface PlayerStatsConfig {
  readonly health: number;
  readonly maxHealth: number;
  readonly mana: number;
  readonly maxMana: number;
  readonly damage: number;
  readonly speed: number;
  readonly defense: number;
  readonly criticalChance: number; // 0-1
  readonly criticalMultiplier: number;
}

export interface PlayerControlsConfig {
  readonly moveLeft: string;
  readonly moveRight: string;
  readonly moveUp: string;
  readonly moveDown: string;
  readonly shoot: string;
  readonly special: string;
  readonly interact: string;
  readonly pause: string;
  readonly menu: string;
  readonly inventory: string;
}

export interface PlayerGameplayConfig {
  readonly invulnerabilityTime: Milliseconds;
  readonly respawnTime: Milliseconds;
  readonly maxLives: number;
  readonly startingLives: number;
  readonly autoRespawn: boolean;
}

export interface PlayerConfig {
  readonly stats: PlayerStatsConfig;
  readonly controls: PlayerControlsConfig;
  readonly gameplay: PlayerGameplayConfig;
}

// Enemy configuration
export type EnemyAIType = 'aggressive' | 'defensive' | 'patrol' | 'ranged' | 'swarm' | 'boss';

export interface EnemyStatsConfig {
  readonly health: number;
  readonly damage: number;
  readonly speed: number;
  readonly experienceValue: number;
  readonly defense: number;
  readonly attackSpeed: number;
}

export interface EnemyAIConfig {
  readonly type: EnemyAIType;
  readonly detectionRange: number;
  readonly attackRange: number;
  readonly attackCooldown: Milliseconds;
  readonly aggroTime: Milliseconds;
  readonly fleeHealthThreshold: number; // 0-1
  readonly wanderRadius: number;
  readonly patrolSpeed: number;
}

export interface EnemySpawningConfig {
  readonly weight: number; // Relative spawn weight
  readonly minLevel: number;
  readonly maxLevel: number;
  readonly maxConcurrent: number;
  readonly spawnCooldown: Milliseconds;
}

export interface EnemyTypeConfig {
  readonly stats: EnemyStatsConfig;
  readonly ai: EnemyAIConfig;
  readonly spawning: EnemySpawningConfig;
}

export interface EnemyConfig {
  readonly [enemyType: string]: EnemyTypeConfig;
}

// Weapon configuration
export type WeaponType = 'pistol' | 'rifle' | 'shotgun' | 'laser' | 'missile' | 'magic';
export type ProjectileType = 'bullet' | 'laser' | 'missile' | 'magic' | 'explosive';

export interface WeaponStatsConfig {
  readonly damage: number;
  readonly fireRate: number; // shots per second
  readonly range: number;
  readonly accuracy: number; // 0-1
  readonly reloadTime: Milliseconds;
  readonly magazineSize: number;
  readonly projectileSpeed: number;
}

export interface WeaponConfig {
  readonly type: WeaponType;
  readonly projectileType: ProjectileType;
  readonly stats: WeaponStatsConfig;
  readonly unlockLevel: number;
  readonly cost: number;
}

// Progression configuration
export interface ProgressionConfig {
  readonly baseExperience: number;
  readonly experienceMultiplier: number;
  readonly maxLevel: number;
  readonly skillPointsPerLevel: number;
  readonly statIncreasePerLevel: number;
}

// Loot configuration
export type ItemRarity = 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';

export interface LootTableEntry {
  readonly itemType: string;
  readonly rarity: ItemRarity;
  readonly weight: number;
  readonly minQuantity: number;
  readonly maxQuantity: number;
}

export interface LootConfig {
  readonly dropChance: number; // 0-1
  readonly rarityWeights: Record<ItemRarity, number>;
  readonly lootTable: readonly LootTableEntry[];
}

// Scene configuration
export interface SceneConfig {
  readonly preloadAssets: readonly string[];
  readonly backgroundColor: string;
  readonly ambientLight: number; // 0-1
  readonly enableParticles: boolean;
  readonly enablePostProcessing: boolean;
}

// Complete game configuration
export interface CompleteGameConfig {
  readonly game: GameConfig;
  readonly player: PlayerConfig;
  readonly enemies: EnemyConfig;
  readonly weapons: readonly WeaponConfig[];
  readonly progression: ProgressionConfig;
  readonly loot: LootConfig;
  readonly scenes: Record<string, SceneConfig>;
}

// Configuration validation types
export interface ConfigValidationRule<T> {
  readonly field: keyof T;
  readonly required: boolean;
  readonly type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  readonly min?: number;
  readonly max?: number;
  readonly pattern?: RegExp;
  readonly allowedValues?: readonly unknown[];
}

export interface ConfigValidationSchema<T> {
  readonly rules: readonly ConfigValidationRule<T>[];
}

export interface ConfigValidationResult {
  readonly isValid: boolean;
  readonly errors: readonly string[];
  readonly warnings: readonly string[];
}

// Configuration factory types
export interface ConfigFactory<T> {
  create(overrides?: Partial<T>): T;
  validate(config: T): ConfigValidationResult;
  merge(base: T, overrides: Partial<T>): T;
}

// Environment-specific configuration
export type Environment = 'development' | 'testing' | 'staging' | 'production';

export interface EnvironmentConfig {
  readonly environment: Environment;
  readonly apiUrl: string;
  readonly enableAnalytics: boolean;
  readonly enableErrorReporting: boolean;
  readonly logLevel: 'debug' | 'info' | 'warn' | 'error';
}

// Type guards for configuration
export function isValidCanvasConfig(config: unknown): config is CanvasConfig {
  return (
    typeof config === 'object' &&
    config !== null &&
    'width' in config &&
    'height' in config &&
    'targetFPS' in config &&
    typeof (config as any).width === 'number' &&
    typeof (config as any).height === 'number' &&
    typeof (config as any).targetFPS === 'number'
  );
}

export function isValidGameConfig(config: unknown): config is GameConfig {
  return (
    typeof config === 'object' &&
    config !== null &&
    'canvas' in config &&
    'debug' in config &&
    'performance' in config &&
    isValidCanvasConfig((config as any).canvas)
  );
}

// Configuration constants
export const DEFAULT_CANVAS_CONFIG: CanvasConfig = {
  width: 1024,
  height: 768,
  targetFPS: 60,
  pixelRatio: 1,
  antialias: true,
  alpha: false,
  preserveDrawingBuffer: false
} as const;

export const DEFAULT_DEBUG_CONFIG: DebugConfig = {
  enabled: false,
  showFPS: false,
  showColliders: false,
  showSpatialGrid: false,
  logPerformance: false,
  enableConsole: false,
  verboseLogging: false
} as const;

export const DEFAULT_PERFORMANCE_CONFIG: PerformanceConfig = {
  maxEntities: 1000,
  maxParticles: 500,
  collisionBudget: 500,
  renderBudget: 16 as Milliseconds,
  updateBudget: 16 as Milliseconds,
  enableProfiling: false,
  autoOptimize: true
} as const;
