import { describe, it, expect, beforeEach } from 'vitest';
import { SpatialGrid, OptimizedCollisionManager, SpatialObject } from '@/engine/physics/SpatialGrid';

describe('SpatialGrid', () => {
  let spatialGrid: SpatialGrid;

  beforeEach(() => {
    spatialGrid = new SpatialGrid(64); // 64px cell size
  });

  describe('initialization', () => {
    it('should initialize with correct cell size', () => {
      expect(spatialGrid).toBeDefined();
    });

    it('should start empty', () => {
      const stats = spatialGrid.getStats();
      expect(stats.totalCells).toBe(0);
      expect(stats.totalObjects).toBe(0);
    });
  });

  describe('insert and remove', () => {
    it('should insert object into correct cells', () => {
      const obj: SpatialObject = {
        position: { x: 32, y: 32 },
        collisionRadius: 16,
        id: 'test1'
      };

      spatialGrid.insert(obj);

      const stats = spatialGrid.getStats();
      expect(stats.totalObjects).toBe(1);
      expect(stats.totalCells).toBeGreaterThan(0);
    });

    it('should insert large object into multiple cells', () => {
      const obj: SpatialObject = {
        position: { x: 64, y: 64 },
        collisionRadius: 50, // Large radius spanning multiple cells
        id: 'large'
      };

      spatialGrid.insert(obj);

      const stats = spatialGrid.getStats();
      expect(stats.totalObjects).toBe(1);
      expect(stats.totalCells).toBeGreaterThan(1); // Should span multiple cells
    });

    it('should remove object from all cells', () => {
      const obj: SpatialObject = {
        position: { x: 32, y: 32 },
        collisionRadius: 16,
        id: 'test1'
      };

      spatialGrid.insert(obj);
      expect(spatialGrid.getStats().totalObjects).toBe(1);

      spatialGrid.remove(obj);
      expect(spatialGrid.getStats().totalObjects).toBe(0);
    });

    it('should handle removing non-existent object', () => {
      const obj: SpatialObject = {
        position: { x: 32, y: 32 },
        collisionRadius: 16,
        id: 'nonexistent'
      };

      // Should not throw error
      spatialGrid.remove(obj);
      expect(spatialGrid.getStats().totalObjects).toBe(0);
    });
  });

  describe('update', () => {
    it('should update object position in grid', () => {
      const obj: SpatialObject = {
        position: { x: 32, y: 32 },
        collisionRadius: 16,
        id: 'moving'
      };

      spatialGrid.insert(obj);
      
      // Move object to different cell
      obj.position.x = 128;
      obj.position.y = 128;
      
      spatialGrid.update(obj);

      // Object should still be in grid but in different cells
      const stats = spatialGrid.getStats();
      expect(stats.totalObjects).toBe(1);
    });
  });

  describe('getNearbyObjects', () => {
    it('should return nearby objects', () => {
      const obj1: SpatialObject = {
        position: { x: 32, y: 32 },
        collisionRadius: 16,
        id: 'obj1'
      };

      const obj2: SpatialObject = {
        position: { x: 48, y: 48 },
        collisionRadius: 16,
        id: 'obj2'
      };

      const obj3: SpatialObject = {
        position: { x: 200, y: 200 },
        collisionRadius: 16,
        id: 'obj3'
      };

      spatialGrid.insert(obj1);
      spatialGrid.insert(obj2);
      spatialGrid.insert(obj3);

      const nearby = spatialGrid.getNearbyObjects(obj1);

      expect(nearby).toContain(obj2); // Should be nearby
      expect(nearby).not.toContain(obj3); // Should be far away
      expect(nearby).not.toContain(obj1); // Should not include self
    });

    it('should return empty array when no nearby objects', () => {
      const obj: SpatialObject = {
        position: { x: 32, y: 32 },
        collisionRadius: 16,
        id: 'lonely'
      };

      spatialGrid.insert(obj);

      const nearby = spatialGrid.getNearbyObjects(obj);
      expect(nearby).toHaveLength(0);
    });
  });

  describe('getObjectsInRadius', () => {
    it('should return objects within radius', () => {
      const obj1: SpatialObject = {
        position: { x: 32, y: 32 },
        collisionRadius: 16,
        id: 'obj1'
      };

      const obj2: SpatialObject = {
        position: { x: 48, y: 48 },
        collisionRadius: 16,
        id: 'obj2'
      };

      spatialGrid.insert(obj1);
      spatialGrid.insert(obj2);

      const objects = spatialGrid.getObjectsInRadius({ x: 40, y: 40 }, 32);

      expect(objects).toContain(obj1);
      expect(objects).toContain(obj2);
    });
  });

  describe('clear', () => {
    it('should clear all objects and cells', () => {
      const obj: SpatialObject = {
        position: { x: 32, y: 32 },
        collisionRadius: 16,
        id: 'test'
      };

      spatialGrid.insert(obj);
      expect(spatialGrid.getStats().totalObjects).toBe(1);

      spatialGrid.clear();
      
      const stats = spatialGrid.getStats();
      expect(stats.totalObjects).toBe(0);
      expect(stats.totalCells).toBe(0);
    });
  });

  describe('getCellBounds', () => {
    it('should return correct cell bounds', () => {
      const bounds = spatialGrid.getCellBounds('1,1');

      expect(bounds.x).toBe(64);
      expect(bounds.y).toBe(64);
      expect(bounds.width).toBe(64);
      expect(bounds.height).toBe(64);
    });
  });
});

describe('OptimizedCollisionManager', () => {
  let collisionManager: OptimizedCollisionManager;

  beforeEach(() => {
    collisionManager = new OptimizedCollisionManager(64);
  });

  describe('checkCollisions', () => {
    it('should detect collisions between groups', () => {
      const group1: SpatialObject[] = [
        { position: { x: 32, y: 32 }, collisionRadius: 16, id: 'a1' }
      ];

      const group2: SpatialObject[] = [
        { position: { x: 40, y: 40 }, collisionRadius: 16, id: 'b1' },
        { position: { x: 200, y: 200 }, collisionRadius: 16, id: 'b2' }
      ];

      const collisions: Array<[SpatialObject, SpatialObject]> = [];
      
      collisionManager.checkCollisions(
        group1,
        group2,
        (obj1, obj2) => {
          collisions.push([obj1, obj2]);
        }
      );

      expect(collisions).toHaveLength(1);
      expect(collisions[0][0].id).toBe('a1');
      expect(collisions[0][1].id).toBe('b1');
    });

    it('should not detect collisions between distant objects', () => {
      const group1: SpatialObject[] = [
        { position: { x: 32, y: 32 }, collisionRadius: 16, id: 'a1' }
      ];

      const group2: SpatialObject[] = [
        { position: { x: 200, y: 200 }, collisionRadius: 16, id: 'b1' }
      ];

      const collisions: Array<[SpatialObject, SpatialObject]> = [];
      
      collisionManager.checkCollisions(
        group1,
        group2,
        (obj1, obj2) => {
          collisions.push([obj1, obj2]);
        }
      );

      expect(collisions).toHaveLength(0);
    });

    it('should track collision check count', () => {
      const group1: SpatialObject[] = [
        { position: { x: 32, y: 32 }, collisionRadius: 16, id: 'a1' },
        { position: { x: 48, y: 48 }, collisionRadius: 16, id: 'a2' }
      ];

      const group2: SpatialObject[] = [
        { position: { x: 40, y: 40 }, collisionRadius: 16, id: 'b1' }
      ];

      collisionManager.resetCollisionChecks();
      
      collisionManager.checkCollisions(
        group1,
        group2,
        () => {}
      );

      const checks = collisionManager.getCollisionChecks();
      expect(checks).toBeGreaterThan(0);
    });

    it('should handle empty groups', () => {
      const group1: SpatialObject[] = [];
      const group2: SpatialObject[] = [
        { position: { x: 32, y: 32 }, collisionRadius: 16, id: 'b1' }
      ];

      const collisions: Array<[SpatialObject, SpatialObject]> = [];
      
      collisionManager.checkCollisions(
        group1,
        group2,
        (obj1, obj2) => {
          collisions.push([obj1, obj2]);
        }
      );

      expect(collisions).toHaveLength(0);
    });
  });

  describe('clear', () => {
    it('should clear spatial grid and reset counters', () => {
      collisionManager.clear();
      
      const stats = collisionManager.getStats();
      expect(stats.totalObjects).toBe(0);
      expect(stats.collisionChecks).toBe(0);
    });
  });

  describe('resetCollisionChecks', () => {
    it('should reset collision check counter', () => {
      // Perform some collision checks
      const group1: SpatialObject[] = [
        { position: { x: 32, y: 32 }, collisionRadius: 16, id: 'a1' }
      ];
      const group2: SpatialObject[] = [
        { position: { x: 40, y: 40 }, collisionRadius: 16, id: 'b1' }
      ];

      collisionManager.checkCollisions(group1, group2, () => {});
      expect(collisionManager.getCollisionChecks()).toBeGreaterThan(0);

      collisionManager.resetCollisionChecks();
      expect(collisionManager.getCollisionChecks()).toBe(0);
    });
  });

  describe('getStats', () => {
    it('should return comprehensive statistics', () => {
      const stats = collisionManager.getStats();

      expect(stats).toHaveProperty('totalCells');
      expect(stats).toHaveProperty('totalObjects');
      expect(stats).toHaveProperty('averageObjectsPerCell');
      expect(stats).toHaveProperty('maxObjectsInCell');
      expect(stats).toHaveProperty('collisionChecks');
    });
  });
});
