/**
 * Common mathematical utility functions to reduce code duplication
 */

import { Vector2 } from '@/utils/math/Vector2';

export class MathUtils {
  // Constants
  static readonly PI = Math.PI;
  static readonly TWO_PI = Math.PI * 2;
  static readonly HALF_PI = Math.PI * 0.5;
  static readonly DEG_TO_RAD = Math.PI / 180;
  static readonly RAD_TO_DEG = 180 / Math.PI;
  static readonly EPSILON = 1e-6;

  /**
   * Clamp a value between min and max
   */
  static clamp(value: number, min: number, max: number): number {
    return Math.max(min, Math.min(max, value));
  }

  /**
   * Linear interpolation between two values
   */
  static lerp(a: number, b: number, t: number): number {
    return a + (b - a) * this.clamp(t, 0, 1);
  }

  /**
   * Smooth step interpolation (ease in/out)
   */
  static smoothStep(a: number, b: number, t: number): number {
    t = this.clamp(t, 0, 1);
    t = t * t * (3 - 2 * t);
    return this.lerp(a, b, t);
  }

  /**
   * Map a value from one range to another
   */
  static map(value: number, fromMin: number, fromMax: number, toMin: number, toMax: number): number {
    const t = (value - fromMin) / (fromMax - fromMin);
    return this.lerp(toMin, toMax, t);
  }

  /**
   * Check if two numbers are approximately equal
   */
  static approximately(a: number, b: number, epsilon: number = this.EPSILON): boolean {
    return Math.abs(a - b) < epsilon;
  }

  /**
   * Calculate distance between two points
   */
  static distance(x1: number, y1: number, x2: number, y2: number): number {
    const dx = x2 - x1;
    const dy = y2 - y1;
    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * Calculate squared distance (faster when you don't need exact distance)
   */
  static distanceSquared(x1: number, y1: number, x2: number, y2: number): number {
    const dx = x2 - x1;
    const dy = y2 - y1;
    return dx * dx + dy * dy;
  }

  /**
   * Normalize an angle to be between 0 and 2π
   */
  static normalizeAngle(angle: number): number {
    while (angle < 0) angle += this.TWO_PI;
    while (angle >= this.TWO_PI) angle -= this.TWO_PI;
    return angle;
  }

  /**
   * Get the shortest angular distance between two angles
   */
  static angleDifference(a: number, b: number): number {
    const diff = this.normalizeAngle(b - a);
    return diff > Math.PI ? diff - this.TWO_PI : diff;
  }

  /**
   * Convert degrees to radians
   */
  static toRadians(degrees: number): number {
    return degrees * this.DEG_TO_RAD;
  }

  /**
   * Convert radians to degrees
   */
  static toDegrees(radians: number): number {
    return radians * this.RAD_TO_DEG;
  }

  /**
   * Generate random number between min and max
   */
  static random(min: number = 0, max: number = 1): number {
    return min + Math.random() * (max - min);
  }

  /**
   * Generate random integer between min and max (inclusive)
   */
  static randomInt(min: number, max: number): number {
    return Math.floor(this.random(min, max + 1));
  }

  /**
   * Check if a point is inside a circle
   */
  static pointInCircle(px: number, py: number, cx: number, cy: number, radius: number): boolean {
    return this.distanceSquared(px, py, cx, cy) <= radius * radius;
  }

  /**
   * Check if a point is inside a rectangle
   */
  static pointInRect(px: number, py: number, rx: number, ry: number, width: number, height: number): boolean {
    return px >= rx && px <= rx + width && py >= ry && py <= ry + height;
  }

  /**
   * Check if two circles overlap
   */
  static circlesOverlap(x1: number, y1: number, r1: number, x2: number, y2: number, r2: number): boolean {
    const minDistance = r1 + r2;
    return this.distanceSquared(x1, y1, x2, y2) <= minDistance * minDistance;
  }

  /**
   * Check if two rectangles overlap
   */
  static rectsOverlap(
    x1: number, y1: number, w1: number, h1: number,
    x2: number, y2: number, w2: number, h2: number
  ): boolean {
    return x1 < x2 + w2 && x1 + w1 > x2 && y1 < y2 + h2 && y1 + h1 > y2;
  }

  /**
   * Get the closest point on a line segment to a given point
   */
  static closestPointOnLine(
    px: number, py: number,
    x1: number, y1: number,
    x2: number, y2: number
  ): { x: number; y: number } {
    const dx = x2 - x1;
    const dy = y2 - y1;
    const length = Math.sqrt(dx * dx + dy * dy);
    
    if (length === 0) {
      return { x: x1, y: y1 };
    }
    
    const t = this.clamp(((px - x1) * dx + (py - y1) * dy) / (length * length), 0, 1);
    
    return {
      x: x1 + t * dx,
      y: y1 + t * dy
    };
  }

  /**
   * Calculate area of a triangle
   */
  static triangleArea(x1: number, y1: number, x2: number, y2: number, x3: number, y3: number): number {
    return Math.abs((x1 * (y2 - y3) + x2 * (y3 - y1) + x3 * (y1 - y2)) / 2);
  }

  /**
   * Check if a point is inside a triangle
   */
  static pointInTriangle(
    px: number, py: number,
    x1: number, y1: number,
    x2: number, y2: number,
    x3: number, y3: number
  ): boolean {
    const totalArea = this.triangleArea(x1, y1, x2, y2, x3, y3);
    const area1 = this.triangleArea(px, py, x2, y2, x3, y3);
    const area2 = this.triangleArea(x1, y1, px, py, x3, y3);
    const area3 = this.triangleArea(x1, y1, x2, y2, px, py);
    
    return this.approximately(totalArea, area1 + area2 + area3);
  }

  /**
   * Ease functions for animations
   */
  static easeInQuad(t: number): number {
    return t * t;
  }

  static easeOutQuad(t: number): number {
    return t * (2 - t);
  }

  static easeInOutQuad(t: number): number {
    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
  }

  static easeInCubic(t: number): number {
    return t * t * t;
  }

  static easeOutCubic(t: number): number {
    return (--t) * t * t + 1;
  }

  static easeInOutCubic(t: number): number {
    return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
  }

  /**
   * Spring physics calculation
   */
  static spring(current: number, target: number, velocity: number, stiffness: number, damping: number, deltaTime: number): {
    position: number;
    velocity: number;
  } {
    const force = (target - current) * stiffness;
    velocity += force * deltaTime;
    velocity *= Math.pow(damping, deltaTime);
    current += velocity * deltaTime;
    
    return { position: current, velocity };
  }

  /**
   * Calculate bounce physics
   */
  static bounce(velocity: number, restitution: number = 0.8): number {
    return -velocity * restitution;
  }

  /**
   * Hash function for consistent pseudo-random numbers
   */
  static hash(seed: number): number {
    seed = ((seed >>> 16) ^ seed) * 0x45d9f3b;
    seed = ((seed >>> 16) ^ seed) * 0x45d9f3b;
    seed = (seed >>> 16) ^ seed;
    return seed / 0x100000000; // Normalize to 0-1
  }

  /**
   * Seeded random number generator
   */
  static seededRandom(seed: number, min: number = 0, max: number = 1): number {
    const hash = this.hash(seed);
    return min + hash * (max - min);
  }

  /**
   * Calculate Perlin noise (simplified 1D version)
   */
  static noise1D(x: number): number {
    const i = Math.floor(x);
    const f = x - i;
    const u = f * f * (3 - 2 * f);
    
    return this.lerp(
      this.hash(i),
      this.hash(i + 1),
      u
    ) * 2 - 1; // Scale to -1 to 1
  }

  /**
   * Calculate 2D Perlin noise (simplified)
   */
  static noise2D(x: number, y: number): number {
    const ix = Math.floor(x);
    const iy = Math.floor(y);
    const fx = x - ix;
    const fy = y - iy;
    
    const ux = fx * fx * (3 - 2 * fx);
    const uy = fy * fy * (3 - 2 * fy);
    
    const a = this.hash(ix + iy * 57);
    const b = this.hash(ix + 1 + iy * 57);
    const c = this.hash(ix + (iy + 1) * 57);
    const d = this.hash(ix + 1 + (iy + 1) * 57);
    
    return this.lerp(
      this.lerp(a, b, ux),
      this.lerp(c, d, ux),
      uy
    ) * 2 - 1;
  }
}
