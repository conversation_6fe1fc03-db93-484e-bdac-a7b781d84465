/**
 * Visual effects system for enhanced game polish
 * Provides screen shake, particle effects, and visual feedback
 */

import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';
import { Vector2 } from '@/utils/math/Vector2';
import { MathUtils } from '@/utils/common/MathUtils';

export interface ScreenShakeConfig {
  intensity: number;
  duration: number;
  frequency: number;
  falloff: 'linear' | 'exponential' | 'elastic';
}

export interface ParticleConfig {
  position: Vector2;
  velocity: Vector2;
  acceleration: Vector2;
  color: string;
  size: number;
  lifetime: number;
  fadeOut: boolean;
  gravity: boolean;
}

export interface FlashConfig {
  color: string;
  intensity: number;
  duration: number;
  fadeType: 'linear' | 'exponential';
}

/**
 * Manages visual effects for enhanced game feel
 */
export class VisualEffects {
  private screenShake: {
    active: boolean;
    intensity: number;
    duration: number;
    elapsed: number;
    frequency: number;
    falloff: string;
    offset: Vector2;
  } = {
    active: false,
    intensity: 0,
    duration: 0,
    elapsed: 0,
    frequency: 30,
    falloff: 'exponential',
    offset: new Vector2(0, 0)
  };

  private particles: Array<{
    position: Vector2;
    velocity: Vector2;
    acceleration: Vector2;
    color: string;
    size: number;
    lifetime: number;
    maxLifetime: number;
    fadeOut: boolean;
    gravity: boolean;
  }> = [];

  private flashes: Array<{
    color: string;
    intensity: number;
    duration: number;
    elapsed: number;
    fadeType: string;
  }> = [];

  private freezeFrames: {
    active: boolean;
    duration: number;
    elapsed: number;
  } = {
    active: false,
    duration: 0,
    elapsed: 0
  };

  /**
   * Start screen shake effect
   */
  startScreenShake(config: ScreenShakeConfig): void {
    this.screenShake.active = true;
    this.screenShake.intensity = config.intensity;
    this.screenShake.duration = config.duration;
    this.screenShake.elapsed = 0;
    this.screenShake.frequency = config.frequency;
    this.screenShake.falloff = config.falloff;
  }

  /**
   * Add a particle effect
   */
  addParticle(config: ParticleConfig): void {
    this.particles.push({
      position: config.position.copy(),
      velocity: config.velocity.copy(),
      acceleration: config.acceleration.copy(),
      color: config.color,
      size: config.size,
      lifetime: config.lifetime,
      maxLifetime: config.lifetime,
      fadeOut: config.fadeOut,
      gravity: config.gravity
    });
  }

  /**
   * Create explosion particle effect
   */
  createExplosion(position: Vector2, particleCount: number = 20, intensity: number = 100): void {
    for (let i = 0; i < particleCount; i++) {
      const angle = (Math.PI * 2 * i) / particleCount + MathUtils.random(-0.2, 0.2);
      const speed = MathUtils.random(intensity * 0.5, intensity);
      const velocity = Vector2.fromAngle(angle, speed);
      
      this.addParticle({
        position: position.copy(),
        velocity,
        acceleration: new Vector2(0, 0),
        color: MathUtils.random() > 0.5 ? '#ff6600' : '#ffaa00',
        size: MathUtils.random(2, 6),
        lifetime: MathUtils.random(0.5, 1.5),
        fadeOut: true,
        gravity: true
      });
    }
  }

  /**
   * Create impact particle effect
   */
  createImpact(position: Vector2, direction: Vector2, intensity: number = 50): void {
    const particleCount = Math.floor(intensity / 10);
    
    for (let i = 0; i < particleCount; i++) {
      const spread = Math.PI / 3; // 60 degree spread
      const angle = direction.angle() + MathUtils.random(-spread/2, spread/2);
      const speed = MathUtils.random(intensity * 0.3, intensity * 0.8);
      const velocity = Vector2.fromAngle(angle, speed);
      
      this.addParticle({
        position: position.copy(),
        velocity,
        acceleration: new Vector2(0, 0),
        color: '#ffffff',
        size: MathUtils.random(1, 3),
        lifetime: MathUtils.random(0.2, 0.8),
        fadeOut: true,
        gravity: false
      });
    }
  }

  /**
   * Create trail particle effect
   */
  createTrail(position: Vector2, velocity: Vector2, color: string = '#ffffff'): void {
    this.addParticle({
      position: position.copy(),
      velocity: velocity.copy().multiply(0.3),
      acceleration: new Vector2(0, 0),
      color,
      size: MathUtils.random(1, 2),
      lifetime: 0.3,
      fadeOut: true,
      gravity: false
    });
  }

  /**
   * Start screen flash effect
   */
  startFlash(config: FlashConfig): void {
    this.flashes.push({
      color: config.color,
      intensity: config.intensity,
      duration: config.duration,
      elapsed: 0,
      fadeType: config.fadeType
    });
  }

  /**
   * Start freeze frame effect (brief pause for impact)
   */
  startFreezeFrame(duration: number): void {
    this.freezeFrames.active = true;
    this.freezeFrames.duration = duration;
    this.freezeFrames.elapsed = 0;
  }

  /**
   * Update all visual effects
   */
  update(deltaTime: number): boolean {
    let shouldFreeze = false;

    // Update screen shake
    if (this.screenShake.active) {
      this.updateScreenShake(deltaTime);
    }

    // Update particles
    this.updateParticles(deltaTime);

    // Update flashes
    this.updateFlashes(deltaTime);

    // Update freeze frames
    if (this.freezeFrames.active) {
      this.freezeFrames.elapsed += deltaTime;
      if (this.freezeFrames.elapsed >= this.freezeFrames.duration) {
        this.freezeFrames.active = false;
      } else {
        shouldFreeze = true;
      }
    }

    return shouldFreeze;
  }

  /**
   * Update screen shake effect
   */
  private updateScreenShake(deltaTime: number): void {
    this.screenShake.elapsed += deltaTime;
    
    if (this.screenShake.elapsed >= this.screenShake.duration) {
      this.screenShake.active = false;
      this.screenShake.offset.set(0, 0);
      return;
    }

    // Calculate shake intensity based on falloff
    let intensity = this.screenShake.intensity;
    const progress = this.screenShake.elapsed / this.screenShake.duration;
    
    switch (this.screenShake.falloff) {
      case 'linear':
        intensity *= (1 - progress);
        break;
      case 'exponential':
        intensity *= Math.pow(1 - progress, 2);
        break;
      case 'elastic':
        intensity *= Math.sin((1 - progress) * Math.PI) * (1 - progress);
        break;
    }

    // Generate shake offset
    const time = this.screenShake.elapsed * this.screenShake.frequency;
    this.screenShake.offset.set(
      Math.sin(time) * intensity,
      Math.cos(time * 1.1) * intensity
    );
  }

  /**
   * Update particle effects
   */
  private updateParticles(deltaTime: number): void {
    for (let i = this.particles.length - 1; i >= 0; i--) {
      const particle = this.particles[i];
      
      // Update lifetime
      particle.lifetime -= deltaTime;
      if (particle.lifetime <= 0) {
        this.particles.splice(i, 1);
        continue;
      }

      // Apply gravity
      if (particle.gravity) {
        particle.acceleration.y += 500 * deltaTime; // Gravity acceleration
      }

      // Update physics
      particle.velocity.add(particle.acceleration.copy().multiply(deltaTime));
      particle.position.add(particle.velocity.copy().multiply(deltaTime));

      // Apply friction
      particle.velocity.multiply(0.98);
    }
  }

  /**
   * Update flash effects
   */
  private updateFlashes(deltaTime: number): void {
    for (let i = this.flashes.length - 1; i >= 0; i--) {
      const flash = this.flashes[i];
      flash.elapsed += deltaTime;
      
      if (flash.elapsed >= flash.duration) {
        this.flashes.splice(i, 1);
      }
    }
  }

  /**
   * Render all visual effects
   */
  render(renderer: CanvasRenderer): void {
    // Apply screen shake offset
    if (this.screenShake.active) {
      renderer.save();
      renderer.translate(this.screenShake.offset.x, this.screenShake.offset.y);
    }

    // Render particles
    this.renderParticles(renderer);

    // Restore screen shake transform
    if (this.screenShake.active) {
      renderer.restore();
    }

    // Render screen flashes (after restoring transform)
    this.renderFlashes(renderer);
  }

  /**
   * Render particle effects
   */
  private renderParticles(renderer: CanvasRenderer): void {
    for (const particle of this.particles) {
      let alpha = 1;
      
      if (particle.fadeOut) {
        alpha = particle.lifetime / particle.maxLifetime;
      }

      renderer.setGlobalAlpha(alpha);
      renderer.drawCircle(
        particle.position.x,
        particle.position.y,
        particle.size,
        particle.color
      );
    }
    
    renderer.setGlobalAlpha(1);
  }

  /**
   * Render flash effects
   */
  private renderFlashes(renderer: CanvasRenderer): void {
    const { width, height } = renderer.getCanvasSize();
    
    for (const flash of this.flashes) {
      const progress = flash.elapsed / flash.duration;
      let alpha = flash.intensity;
      
      switch (flash.fadeType) {
        case 'linear':
          alpha *= (1 - progress);
          break;
        case 'exponential':
          alpha *= Math.pow(1 - progress, 2);
          break;
      }

      renderer.setGlobalAlpha(alpha);
      renderer.drawRect(0, 0, width, height, flash.color);
    }
    
    renderer.setGlobalAlpha(1);
  }

  /**
   * Get screen shake offset for camera
   */
  getScreenShakeOffset(): Vector2 {
    return this.screenShake.active ? this.screenShake.offset.copy() : new Vector2(0, 0);
  }

  /**
   * Check if freeze frame is active
   */
  isFreezeFrameActive(): boolean {
    return this.freezeFrames.active;
  }

  /**
   * Clear all effects
   */
  clear(): void {
    this.screenShake.active = false;
    this.screenShake.offset.set(0, 0);
    this.particles.length = 0;
    this.flashes.length = 0;
    this.freezeFrames.active = false;
  }

  /**
   * Get particle count for performance monitoring
   */
  getParticleCount(): number {
    return this.particles.length;
  }
}
