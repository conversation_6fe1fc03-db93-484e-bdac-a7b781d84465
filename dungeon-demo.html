<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dungeon Generator Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #1a1a1a;
            color: #ffffff;
            font-family: 'Courier New', monospace;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        
        #gameCanvas {
            border: 2px solid #444;
            background: #000;
        }
        
        #controls {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 5px;
            font-size: 12px;
            line-height: 1.6;
        }
        
        button {
            background: #333;
            color: white;
            border: 1px solid #666;
            padding: 5px 10px;
            margin: 2px;
            cursor: pointer;
            border-radius: 3px;
        }
        
        button:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <canvas id="gameCanvas" width="1024" height="768"></canvas>
    <div id="controls">
        <div><strong>🏰 Dungeon Generator Demo</strong></div>
        <div>WASD: Move camera</div>
        <div>Mouse wheel: Zoom</div>
        <div>G: Toggle grid</div>
        <div>R: Toggle room IDs</div>
        <div>C: Toggle connections</div>
        <br>
        <button onclick="generateNewDungeon()">Generate New Dungeon</button>
        <button onclick="toggleGrid()">Toggle Grid</button>
        <button onclick="toggleRoomIds()">Toggle Room IDs</button>
        <button onclick="toggleConnections()">Toggle Connections</button>
        <br><br>
        <div id="stats">
            Rooms: <span id="roomCount">0</span><br>
            Corridors: <span id="corridorCount">0</span><br>
            Zoom: <span id="zoomLevel">1.0</span>x
        </div>
    </div>

    <script>
        // Vector2 class
        class Vector2 {
            constructor(x = 0, y = 0) {
                this.x = x;
                this.y = y;
            }
            
            set(x, y) {
                this.x = x;
                this.y = y;
                return this;
            }
            
            copy() {
                return new Vector2(this.x, this.y);
            }
            
            distance(other) {
                const dx = this.x - other.x;
                const dy = this.y - other.y;
                return Math.sqrt(dx * dx + dy * dy);
            }
        }

        // Simplified Dungeon Generator
        class DungeonGenerator {
            constructor(config = {}) {
                this.config = {
                    width: 60,
                    height: 40,
                    roomCount: 8,
                    minRoomSize: 6,
                    maxRoomSize: 12,
                    corridorWidth: 3,
                    maxAttempts: 100,
                    ...config
                };
                this.rooms = [];
                this.corridors = [];
                this.tiles = [];
                this.roomIdCounter = 0;
            }

            generateDungeon() {
                this.reset();
                this.initializeTiles();
                this.generateRooms();
                this.connectRooms();
                this.generateCorridors();
                this.placeTiles();
                
                return {
                    rooms: this.rooms,
                    corridors: this.corridors,
                    tiles: this.tiles
                };
            }

            reset() {
                this.rooms = [];
                this.corridors = [];
                this.tiles = [];
                this.roomIdCounter = 0;
            }

            initializeTiles() {
                this.tiles = [];
                for (let y = 0; y < this.config.height; y++) {
                    this.tiles[y] = [];
                    for (let x = 0; x < this.config.width; x++) {
                        this.tiles[y][x] = { type: 'void' };
                    }
                }
            }

            generateRooms() {
                let attempts = 0;
                
                while (this.rooms.length < this.config.roomCount && attempts < this.config.maxAttempts) {
                    const room = this.generateRandomRoom();
                    
                    if (this.isValidRoomPlacement(room)) {
                        this.rooms.push(room);
                    }
                    
                    attempts++;
                }

                if (this.rooms.length === 0) {
                    const centerRoom = this.createRoom(
                        Math.floor(this.config.width / 2) - 5,
                        Math.floor(this.config.height / 2) - 5,
                        10, 10, 'start'
                    );
                    this.rooms.push(centerRoom);
                }

                this.assignRoomTypes();
            }

            generateRandomRoom() {
                const width = this.randomInt(this.config.minRoomSize, this.config.maxRoomSize);
                const height = this.randomInt(this.config.minRoomSize, this.config.maxRoomSize);
                const x = this.randomInt(1, this.config.width - width - 1);
                const y = this.randomInt(1, this.config.height - height - 1);
                
                return this.createRoom(x, y, width, height);
            }

            createRoom(x, y, width, height, type = 'normal') {
                return {
                    id: this.roomIdCounter++,
                    x, y, width, height,
                    center: new Vector2(x + width / 2, y + height / 2),
                    type,
                    connections: []
                };
            }

            isValidRoomPlacement(room) {
                if (room.x < 1 || room.y < 1 || 
                    room.x + room.width >= this.config.width - 1 || 
                    room.y + room.height >= this.config.height - 1) {
                    return false;
                }

                for (const existingRoom of this.rooms) {
                    if (this.roomsOverlap(room, existingRoom, 2)) {
                        return false;
                    }
                }

                return true;
            }

            roomsOverlap(room1, room2, buffer = 0) {
                return !(room1.x + room1.width + buffer < room2.x ||
                         room2.x + room2.width + buffer < room1.x ||
                         room1.y + room1.height + buffer < room2.y ||
                         room2.y + room2.height + buffer < room1.y);
            }

            assignRoomTypes() {
                if (this.rooms.length === 0) return;

                this.rooms[0].type = 'start';
                if (this.rooms.length > 1) {
                    this.rooms[this.rooms.length - 1].type = 'boss';
                }

                for (let i = 1; i < this.rooms.length - 1; i++) {
                    const rand = Math.random();
                    if (rand < 0.15) {
                        this.rooms[i].type = 'treasure';
                    } else if (rand < 0.25) {
                        this.rooms[i].type = 'shop';
                    }
                }
            }

            connectRooms() {
                if (this.rooms.length < 2) return;

                const connected = new Set();
                const unconnected = new Set(this.rooms.map(room => room.id));
                
                const startRoom = this.rooms[0];
                connected.add(startRoom.id);
                unconnected.delete(startRoom.id);

                while (unconnected.size > 0) {
                    let closestDistance = Infinity;
                    let closestPair = null;

                    for (const connectedId of connected) {
                        const connectedRoom = this.rooms.find(r => r.id === connectedId);
                        
                        for (const unconnectedId of unconnected) {
                            const unconnectedRoom = this.rooms.find(r => r.id === unconnectedId);
                            const distance = connectedRoom.center.distance(unconnectedRoom.center);
                            
                            if (distance < closestDistance) {
                                closestDistance = distance;
                                closestPair = [connectedRoom, unconnectedRoom];
                            }
                        }
                    }

                    if (closestPair) {
                        const [room1, room2] = closestPair;
                        room1.connections.push(room2.id);
                        room2.connections.push(room1.id);
                        connected.add(room2.id);
                        unconnected.delete(room2.id);
                    }
                }
            }

            generateCorridors() {
                const processedConnections = new Set();

                for (const room of this.rooms) {
                    for (const connectionId of room.connections) {
                        const connectionKey = [room.id, connectionId].sort().join('-');
                        
                        if (!processedConnections.has(connectionKey)) {
                            const connectedRoom = this.rooms.find(r => r.id === connectionId);
                            const corridor = this.createCorridor(room, connectedRoom);
                            this.corridors.push(corridor);
                            processedConnections.add(connectionKey);
                        }
                    }
                }
            }

            createCorridor(room1, room2) {
                const start = room1.center.copy();
                const end = room2.center.copy();
                
                const points = [start];
                
                if (Math.random() < 0.5) {
                    points.push(new Vector2(end.x, start.y));
                } else {
                    points.push(new Vector2(start.x, end.y));
                }
                
                points.push(end);

                return { start, end, width: this.config.corridorWidth, points };
            }

            placeTiles() {
                // Place room tiles
                for (const room of this.rooms) {
                    for (let y = room.y; y < room.y + room.height; y++) {
                        for (let x = room.x; x < room.x + room.width; x++) {
                            if (x === room.x || x === room.x + room.width - 1 ||
                                y === room.y || y === room.y + room.height - 1) {
                                this.tiles[y][x] = { type: 'wall', roomId: room.id };
                            } else {
                                this.tiles[y][x] = { type: 'floor', roomId: room.id };
                            }
                        }
                    }
                }

                // Place corridor tiles
                for (const corridor of this.corridors) {
                    for (let i = 0; i < corridor.points.length - 1; i++) {
                        const start = corridor.points[i];
                        const end = corridor.points[i + 1];
                        this.drawCorridorSegment(start, end, corridor.width);
                    }
                }
            }

            drawCorridorSegment(start, end, width) {
                const halfWidth = Math.floor(width / 2);
                
                if (start.x === end.x) {
                    const minY = Math.min(start.y, end.y);
                    const maxY = Math.max(start.y, end.y);
                    
                    for (let y = minY; y <= maxY; y++) {
                        for (let x = start.x - halfWidth; x <= start.x + halfWidth; x++) {
                            if (this.isValidTilePosition(x, y)) {
                                this.tiles[y][x] = { type: 'floor', isCorridorTile: true };
                            }
                        }
                    }
                } else {
                    const minX = Math.min(start.x, end.x);
                    const maxX = Math.max(start.x, end.x);
                    
                    for (let x = minX; x <= maxX; x++) {
                        for (let y = start.y - halfWidth; y <= start.y + halfWidth; y++) {
                            if (this.isValidTilePosition(x, y)) {
                                this.tiles[y][x] = { type: 'floor', isCorridorTile: true };
                            }
                        }
                    }
                }
            }

            isValidTilePosition(x, y) {
                return x >= 0 && x < this.config.width && y >= 0 && y < this.config.height;
            }

            randomInt(min, max) {
                return Math.floor(Math.random() * (max - min + 1)) + min;
            }
        }

        // Dungeon Renderer
        class DungeonRenderer {
            constructor() {
                this.tileSize = 12;
                this.camera = new Vector2(0, 0);
                this.zoom = 1;
                this.showGrid = false;
                this.showRoomIds = false;
                this.showConnections = false;
                
                this.colors = {
                    wall: '#444444',
                    floor: '#cccccc',
                    void: '#000000',
                    corridor: '#aaaaaa',
                    roomBorder: '#666666',
                    connection: '#ff0000'
                };
            }

            render(ctx, dungeon) {
                ctx.save();
                
                // Apply camera transform
                ctx.translate(-this.camera.x * this.zoom, -this.camera.y * this.zoom);
                ctx.scale(this.zoom, this.zoom);

                // Render tiles
                this.renderTiles(ctx, dungeon.tiles);
                
                // Render rooms
                this.renderRooms(ctx, dungeon.rooms);
                
                // Render corridors
                if (this.showConnections) {
                    this.renderCorridors(ctx, dungeon.corridors);
                }
                
                // Render grid
                if (this.showGrid) {
                    this.renderGrid(ctx, dungeon.tiles);
                }

                ctx.restore();
            }

            renderTiles(ctx, tiles) {
                for (let y = 0; y < tiles.length; y++) {
                    for (let x = 0; x < tiles[y].length; x++) {
                        const tile = tiles[y][x];
                        const pixelX = x * this.tileSize;
                        const pixelY = y * this.tileSize;
                        
                        let color = this.colors.void;
                        
                        switch (tile.type) {
                            case 'wall':
                                color = this.colors.wall;
                                break;
                            case 'floor':
                                color = tile.isCorridorTile ? this.colors.corridor : this.colors.floor;
                                break;
                        }
                        
                        ctx.fillStyle = color;
                        ctx.fillRect(pixelX, pixelY, this.tileSize, this.tileSize);
                    }
                }
            }

            renderRooms(ctx, rooms) {
                for (const room of rooms) {
                    const pixelX = room.x * this.tileSize;
                    const pixelY = room.y * this.tileSize;
                    const pixelWidth = room.width * this.tileSize;
                    const pixelHeight = room.height * this.tileSize;
                    
                    // Draw room border
                    ctx.strokeStyle = this.colors.roomBorder;
                    ctx.lineWidth = 2;
                    ctx.strokeRect(pixelX, pixelY, pixelWidth, pixelHeight);
                    
                    // Draw room type indicator
                    const centerX = pixelX + pixelWidth / 2;
                    const centerY = pixelY + pixelHeight / 2;
                    
                    ctx.fillStyle = this.getRoomTypeColor(room.type);
                    
                    switch (room.type) {
                        case 'start':
                            ctx.beginPath();
                            ctx.arc(centerX, centerY, 6, 0, Math.PI * 2);
                            ctx.fill();
                            break;
                        case 'boss':
                            ctx.fillRect(centerX - 6, centerY - 6, 12, 12);
                            break;
                        case 'treasure':
                            ctx.beginPath();
                            ctx.arc(centerX, centerY, 4, 0, Math.PI * 2);
                            ctx.fill();
                            break;
                        case 'shop':
                            ctx.beginPath();
                            ctx.arc(centerX, centerY, 4, 0, Math.PI * 2);
                            ctx.fill();
                            break;
                    }
                    
                    // Draw room ID
                    if (this.showRoomIds) {
                        ctx.fillStyle = '#ffffff';
                        ctx.font = '10px monospace';
                        ctx.textAlign = 'center';
                        ctx.fillText(room.id.toString(), centerX, centerY + 3);
                    }
                }
            }

            renderCorridors(ctx, corridors) {
                ctx.strokeStyle = this.colors.connection;
                ctx.lineWidth = 2;
                
                for (const corridor of corridors) {
                    ctx.beginPath();
                    for (let i = 0; i < corridor.points.length - 1; i++) {
                        const start = corridor.points[i];
                        const end = corridor.points[i + 1];
                        
                        if (i === 0) {
                            ctx.moveTo(start.x * this.tileSize, start.y * this.tileSize);
                        }
                        ctx.lineTo(end.x * this.tileSize, end.y * this.tileSize);
                    }
                    ctx.stroke();
                }
            }

            renderGrid(ctx, tiles) {
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
                ctx.lineWidth = 1;
                
                // Vertical lines
                for (let x = 0; x <= tiles[0].length; x++) {
                    const pixelX = x * this.tileSize;
                    ctx.beginPath();
                    ctx.moveTo(pixelX, 0);
                    ctx.lineTo(pixelX, tiles.length * this.tileSize);
                    ctx.stroke();
                }
                
                // Horizontal lines
                for (let y = 0; y <= tiles.length; y++) {
                    const pixelY = y * this.tileSize;
                    ctx.beginPath();
                    ctx.moveTo(0, pixelY);
                    ctx.lineTo(tiles[0].length * this.tileSize, pixelY);
                    ctx.stroke();
                }
            }

            getRoomTypeColor(type) {
                switch (type) {
                    case 'start': return '#4CAF50';
                    case 'boss': return '#ff0000';
                    case 'treasure': return '#FFD700';
                    case 'shop': return '#2196F3';
                    default: return '#888888';
                }
            }
        }

        // Main application
        class DungeonDemo {
            constructor() {
                this.canvas = document.getElementById('gameCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.generator = new DungeonGenerator();
                this.renderer = new DungeonRenderer();
                this.dungeon = null;
                
                this.keys = new Set();
                this.lastTime = 0;
                
                this.setupInput();
                this.generateNewDungeon();
                this.gameLoop();
            }

            setupInput() {
                window.addEventListener('keydown', (e) => {
                    this.keys.add(e.code);
                    
                    switch (e.code) {
                        case 'KeyG':
                            this.toggleGrid();
                            break;
                        case 'KeyR':
                            this.toggleRoomIds();
                            break;
                        case 'KeyC':
                            this.toggleConnections();
                            break;
                    }
                });
                
                window.addEventListener('keyup', (e) => {
                    this.keys.delete(e.code);
                });
                
                this.canvas.addEventListener('wheel', (e) => {
                    e.preventDefault();
                    const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
                    this.renderer.zoom = Math.max(0.2, Math.min(3, this.renderer.zoom * zoomFactor));
                    document.getElementById('zoomLevel').textContent = this.renderer.zoom.toFixed(1);
                });
            }

            update(deltaTime) {
                const speed = 200 / this.renderer.zoom;
                
                if (this.keys.has('KeyW') || this.keys.has('ArrowUp')) {
                    this.renderer.camera.y -= speed * deltaTime;
                }
                if (this.keys.has('KeyS') || this.keys.has('ArrowDown')) {
                    this.renderer.camera.y += speed * deltaTime;
                }
                if (this.keys.has('KeyA') || this.keys.has('ArrowLeft')) {
                    this.renderer.camera.x -= speed * deltaTime;
                }
                if (this.keys.has('KeyD') || this.keys.has('ArrowRight')) {
                    this.renderer.camera.x += speed * deltaTime;
                }
            }

            render() {
                this.ctx.fillStyle = '#000000';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                
                if (this.dungeon) {
                    this.renderer.render(this.ctx, this.dungeon);
                }
            }

            gameLoop(currentTime = 0) {
                const deltaTime = (currentTime - this.lastTime) / 1000;
                this.lastTime = currentTime;
                
                this.update(deltaTime);
                this.render();
                
                requestAnimationFrame((time) => this.gameLoop(time));
            }

            generateNewDungeon() {
                this.dungeon = this.generator.generateDungeon();
                
                // Center camera on first room
                if (this.dungeon.rooms.length > 0) {
                    const startRoom = this.dungeon.rooms[0];
                    this.renderer.camera.x = startRoom.center.x * this.renderer.tileSize - this.canvas.width / 2;
                    this.renderer.camera.y = startRoom.center.y * this.renderer.tileSize - this.canvas.height / 2;
                }
                
                // Update stats
                document.getElementById('roomCount').textContent = this.dungeon.rooms.length;
                document.getElementById('corridorCount').textContent = this.dungeon.corridors.length;
            }

            toggleGrid() {
                this.renderer.showGrid = !this.renderer.showGrid;
            }

            toggleRoomIds() {
                this.renderer.showRoomIds = !this.renderer.showRoomIds;
            }

            toggleConnections() {
                this.renderer.showConnections = !this.renderer.showConnections;
            }
        }

        // Global functions for buttons
        let demo;
        
        function generateNewDungeon() {
            demo.generateNewDungeon();
        }
        
        function toggleGrid() {
            demo.toggleGrid();
        }
        
        function toggleRoomIds() {
            demo.toggleRoomIds();
        }
        
        function toggleConnections() {
            demo.toggleConnections();
        }

        // Start the demo
        window.addEventListener('load', () => {
            demo = new DungeonDemo();
        });
    </script>
</body>
</html>
