/**
 * Combat system handling weapons, spells, and damage calculations
 */

import { Vector2 } from '@/utils/math/Vector2';
import { Player } from '@/game/entities/Player';
import { Enemy } from '@/game/entities/Enemy';

export interface WeaponStats {
  damage: number;
  attackRate: number; // attacks per second
  range: number;
  projectileSpeed: number;
  manaCost: number;
  criticalChance: number;
  criticalMultiplier: number;
  piercing: boolean;
  homing: boolean;
  homingStrength?: number;
  multishot?: number;
  spread?: number; // in radians
}

export interface Weapon {
  id: string;
  name: string;
  type: 'bow' | 'magic' | 'crossbow' | 'staff';
  stats: WeaponStats;
  level: number;
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  description: string;
  projectileConfig: any;
}

export class CombatSystem {
  private weapons: Map<string, Weapon> = new Map();
  
  constructor() {
    this.initializeWeapons();
  }

  private initializeWeapons(): void {
    // Basic Bow
    this.registerWeapon({
      id: 'basic_bow',
      name: 'Wooden Bow',
      type: 'bow',
      level: 1,
      rarity: 'common',
      description: 'A simple wooden bow for beginners',
      stats: {
        damage: 25,
        attackRate: 3,
        range: 400,
        projectileSpeed: 600,
        manaCost: 0,
        criticalChance: 0.05,
        criticalMultiplier: 2.0,
        piercing: false,
        homing: false
      },
      projectileConfig: {
        size: 8,
        color: '#8B4513',
        lifetime: 3,
        shape: 'arrow'
      }
    });

    // Magic Missile
    this.registerWeapon({
      id: 'magic_missile',
      name: 'Magic Missile',
      type: 'magic',
      level: 1,
      rarity: 'common',
      description: 'Basic magic projectile that homes in on enemies',
      stats: {
        damage: 20,
        attackRate: 4,
        range: 350,
        projectileSpeed: 400,
        manaCost: 5,
        criticalChance: 0.1,
        criticalMultiplier: 1.5,
        piercing: false,
        homing: true,
        homingStrength: 3
      },
      projectileConfig: {
        size: 10,
        color: '#4A90E2',
        lifetime: 4,
        shape: 'orb'
      }
    });

    // Piercing Shot
    this.registerWeapon({
      id: 'piercing_bow',
      name: 'Piercing Bow',
      type: 'bow',
      level: 3,
      rarity: 'uncommon',
      description: 'Arrows pierce through multiple enemies',
      stats: {
        damage: 35,
        attackRate: 2.5,
        range: 500,
        projectileSpeed: 700,
        manaCost: 0,
        criticalChance: 0.08,
        criticalMultiplier: 2.2,
        piercing: true,
        homing: false
      },
      projectileConfig: {
        size: 10,
        color: '#FFD700',
        lifetime: 4,
        shape: 'arrow'
      }
    });

    // Multishot Crossbow
    this.registerWeapon({
      id: 'multishot_crossbow',
      name: 'Triple Crossbow',
      type: 'crossbow',
      level: 4,
      rarity: 'rare',
      description: 'Fires three bolts in a spread pattern',
      stats: {
        damage: 30,
        attackRate: 2,
        range: 450,
        projectileSpeed: 650,
        manaCost: 0,
        criticalChance: 0.12,
        criticalMultiplier: 2.5,
        piercing: false,
        homing: false,
        multishot: 3,
        spread: Math.PI / 8 // 22.5 degrees
      },
      projectileConfig: {
        size: 9,
        color: '#8B0000',
        lifetime: 3.5,
        shape: 'bolt'
      }
    });

    // Fireball Staff
    this.registerWeapon({
      id: 'fireball_staff',
      name: 'Staff of Fireballs',
      type: 'staff',
      level: 5,
      rarity: 'epic',
      description: 'Launches explosive fireballs',
      stats: {
        damage: 50,
        attackRate: 1.5,
        range: 400,
        projectileSpeed: 300,
        manaCost: 15,
        criticalChance: 0.15,
        criticalMultiplier: 3.0,
        piercing: false,
        homing: false
      },
      projectileConfig: {
        size: 16,
        color: '#FF4500',
        lifetime: 5,
        shape: 'fireball',
        explosive: true,
        explosionRadius: 60,
        explosionDamage: 30
      }
    });

    // Lightning Bolt
    this.registerWeapon({
      id: 'lightning_staff',
      name: 'Lightning Staff',
      type: 'staff',
      level: 6,
      rarity: 'legendary',
      description: 'Instant lightning that chains between enemies',
      stats: {
        damage: 40,
        attackRate: 3,
        range: 600,
        projectileSpeed: 2000, // Near-instant
        manaCost: 12,
        criticalChance: 0.2,
        criticalMultiplier: 2.8,
        piercing: true,
        homing: false
      },
      projectileConfig: {
        size: 6,
        color: '#FFFF00',
        lifetime: 0.5,
        shape: 'lightning',
        chaining: true,
        chainCount: 3,
        chainRange: 100
      }
    });
  }

  registerWeapon(weapon: Weapon): void {
    this.weapons.set(weapon.id, weapon);
  }

  getWeapon(id: string): Weapon | null {
    return this.weapons.get(id) || null;
  }

  getAllWeapons(): Weapon[] {
    return Array.from(this.weapons.values());
  }

  getWeaponsByType(type: string): Weapon[] {
    return Array.from(this.weapons.values()).filter(weapon => weapon.type === type);
  }

  getWeaponsByRarity(rarity: string): Weapon[] {
    return Array.from(this.weapons.values()).filter(weapon => weapon.rarity === rarity);
  }

  // Combat calculations
  calculateDamage(
    weapon: Weapon,
    attacker: Player | Enemy,
    target: Player | Enemy,
    isCritical: boolean = false
  ): number {
    let baseDamage = weapon.stats.damage;
    
    // Add attacker's damage bonus
    if ('stats' in attacker && attacker.stats.damage) {
      baseDamage += attacker.stats.damage;
    }
    
    // Apply critical hit
    if (isCritical) {
      baseDamage *= weapon.stats.criticalMultiplier;
    }
    
    // Apply target's defense
    if ('stats' in target && target.stats.defense) {
      baseDamage = Math.max(1, baseDamage - target.stats.defense);
    }
    
    return Math.floor(baseDamage);
  }

  rollCriticalHit(weapon: Weapon, attacker: Player | Enemy): boolean {
    let critChance = weapon.stats.criticalChance;
    
    // Add attacker's critical chance bonus
    if ('stats' in attacker && attacker.stats.criticalChance) {
      critChance += attacker.stats.criticalChance;
    }
    
    return Math.random() < critChance;
  }

  canAttack(weapon: Weapon, attacker: Player | Enemy, lastAttackTime: number): boolean {
    const currentTime = performance.now();
    const attackInterval = 1000 / weapon.stats.attackRate; // Convert to milliseconds
    
    return (currentTime - lastAttackTime) >= attackInterval;
  }

  hasEnoughMana(weapon: Weapon, attacker: Player): boolean {
    if (weapon.stats.manaCost === 0) return true;
    return attacker.stats.mana >= weapon.stats.manaCost;
  }

  consumeMana(weapon: Weapon, attacker: Player): void {
    if (weapon.stats.manaCost > 0) {
      attacker.stats.mana = Math.max(0, attacker.stats.mana - weapon.stats.manaCost);
    }
  }

  // Projectile creation
  createProjectiles(
    weapon: Weapon,
    startPos: Vector2,
    aimDirection: Vector2,
    isPlayerProjectile: boolean,
    gameObjectManager: any
  ): any[] {
    const projectiles: any[] = [];
    const shotCount = weapon.stats.multishot || 1;
    
    for (let i = 0; i < shotCount; i++) {
      let direction = aimDirection.copy();
      
      // Apply spread for multishot
      if (shotCount > 1 && weapon.stats.spread) {
        const spreadAngle = weapon.stats.spread;
        const angleOffset = (i - (shotCount - 1) / 2) * (spreadAngle / (shotCount - 1));
        direction = direction.rotate(angleOffset);
      }
      
      // Create bullet config
      const bulletConfig = {
        damage: weapon.stats.damage,
        speed: weapon.stats.projectileSpeed,
        size: weapon.projectileConfig.size,
        color: weapon.projectileConfig.color,
        lifetime: weapon.projectileConfig.lifetime,
        piercing: weapon.stats.piercing,
        homing: weapon.stats.homing,
        homingStrength: weapon.stats.homingStrength,
        ...weapon.projectileConfig
      };
      
      // Create projectile
      const projectile = gameObjectManager.createBullet(
        startPos.x,
        startPos.y,
        direction,
        bulletConfig,
        isPlayerProjectile
      );
      
      if (projectile) {
        projectiles.push(projectile);
      }
    }
    
    return projectiles;
  }

  // Special weapon effects
  handleSpecialEffects(
    weapon: Weapon,
    projectile: any,
    hitTarget: any,
    gameObjectManager: any
  ): void {
    switch (weapon.projectileConfig.shape) {
      case 'fireball':
        if (weapon.projectileConfig.explosive) {
          this.createExplosion(
            projectile.getPosition(),
            weapon.projectileConfig.explosionRadius,
            weapon.projectileConfig.explosionDamage,
            gameObjectManager
          );
        }
        break;
        
      case 'lightning':
        if (weapon.projectileConfig.chaining) {
          this.createLightningChain(
            projectile.getPosition(),
            hitTarget,
            weapon.projectileConfig.chainCount,
            weapon.projectileConfig.chainRange,
            weapon.stats.damage * 0.5,
            gameObjectManager
          );
        }
        break;
    }
  }

  private createExplosion(
    center: Vector2,
    radius: number,
    damage: number,
    gameObjectManager: any
  ): void {
    // Create explosion visual effect
    gameObjectManager.createExplosion(center.x, center.y, 2);
    
    // TODO: Damage enemies in explosion radius
    // This would require access to enemy list
  }

  private createLightningChain(
    startPos: Vector2,
    firstTarget: any,
    chainCount: number,
    chainRange: number,
    damage: number,
    gameObjectManager: any
  ): void {
    // TODO: Implement lightning chain effect
    // This would create additional projectiles that jump between nearby enemies
  }

  // Weapon upgrade system
  upgradeWeapon(weapon: Weapon): Weapon {
    const upgradedWeapon = { ...weapon };
    upgradedWeapon.level++;
    
    // Increase stats by 10% per level
    const multiplier = 1.1;
    upgradedWeapon.stats = {
      ...weapon.stats,
      damage: Math.floor(weapon.stats.damage * multiplier),
      attackRate: weapon.stats.attackRate * multiplier,
      range: weapon.stats.range * multiplier,
      criticalChance: Math.min(0.5, weapon.stats.criticalChance * multiplier)
    };
    
    return upgradedWeapon;
  }

  // Weapon rarity colors for UI
  getRarityColor(rarity: string): string {
    switch (rarity) {
      case 'common': return '#ffffff';
      case 'uncommon': return '#1eff00';
      case 'rare': return '#0070dd';
      case 'epic': return '#a335ee';
      case 'legendary': return '#ff8000';
      default: return '#ffffff';
    }
  }
}
