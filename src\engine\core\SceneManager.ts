/**
 * Scene management system for handling different game states
 */

import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';

export interface Scene {
  name: string;
  initialize(): void;
  fixedUpdate(deltaTime: number): void;
  update(deltaTime: number): void;
  render(renderer: <PERSON>vas<PERSON><PERSON><PERSON>, alpha: number): void;
  destroy(): void;
  onEnter(): void;
  onExit(): void;
}

export abstract class BaseScene implements Scene {
  public name: string = '';
  protected initialized: boolean = false;

  abstract initialize(): void;
  abstract fixedUpdate(deltaTime: number): void;
  abstract update(deltaTime: number): void;
  abstract render(renderer: CanvasRenderer, alpha: number): void;

  destroy(): void {
    // Override in derived classes if needed
  }

  onEnter(): void {
    if (!this.initialized) {
      this.initialize();
      this.initialized = true;
    }
  }

  onExit(): void {
    // Override in derived classes if needed
  }
}

export class SceneManager {
  private scenes: Map<string, Scene> = new Map();
  private currentScene: Scene | null = null;
  private nextScene: string | null = null;
  private isTransitioning: boolean = false;

  addScene(name: string, scene: Scene): void {
    scene.name = name;
    this.scenes.set(name, scene);
  }

  removeScene(name: string): void {
    const scene = this.scenes.get(name);
    if (scene) {
      if (this.currentScene === scene) {
        this.currentScene = null;
      }
      scene.destroy();
      this.scenes.delete(name);
    }
  }

  switchTo(sceneName: string): void {
    if (!this.scenes.has(sceneName)) {
      console.error(`Scene '${sceneName}' not found`);
      return;
    }

    if (this.isTransitioning) {
      this.nextScene = sceneName;
      return;
    }

    this.performSceneSwitch(sceneName);
  }

  private performSceneSwitch(sceneName: string): void {
    this.isTransitioning = true;

    // Exit current scene
    if (this.currentScene) {
      this.currentScene.onExit();
    }

    // Switch to new scene
    const newScene = this.scenes.get(sceneName);
    if (newScene) {
      this.currentScene = newScene;
      this.currentScene.onEnter();
    }

    this.isTransitioning = false;

    // Handle queued scene switch
    if (this.nextScene) {
      const queuedScene = this.nextScene;
      this.nextScene = null;
      this.switchTo(queuedScene);
    }
  }

  getCurrentScene(): Scene | null {
    return this.currentScene;
  }

  getCurrentSceneName(): string | null {
    return this.currentScene?.name || null;
  }

  fixedUpdate(deltaTime: number): void {
    if (this.currentScene && !this.isTransitioning) {
      this.currentScene.fixedUpdate(deltaTime);
    }
  }

  update(deltaTime: number): void {
    if (this.currentScene && !this.isTransitioning) {
      this.currentScene.update(deltaTime);
    }
  }

  render(renderer: CanvasRenderer, alpha: number): void {
    if (this.currentScene && !this.isTransitioning) {
      this.currentScene.render(renderer, alpha);
    }
  }

  destroy(): void {
    // Destroy all scenes
    for (const scene of this.scenes.values()) {
      scene.destroy();
    }
    this.scenes.clear();
    this.currentScene = null;
    this.nextScene = null;
    this.isTransitioning = false;
  }
}
