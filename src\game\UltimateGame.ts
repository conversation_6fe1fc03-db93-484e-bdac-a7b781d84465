/**
 * Ultimate Game class integrating all advanced systems for the complete demo
 */

import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';
import { InputManager } from '@/engine/input/InputManager';
import { ObjectPoolManager } from '@/engine/core/ObjectPoolManager';
import { PerformanceMonitor } from '@/engine/core/PerformanceMonitor';
import { Player } from './entities/Player';
import { Enemy } from './entities/Enemy';
import { Bullet } from './entities/Bullet';
import { CombatSystem } from './systems/CombatSystem';
import { CollisionSystem } from './systems/CollisionSystem';
import { ParticleSystem } from './systems/ParticleSystem';
import { ProgressionSystem } from './systems/ProgressionSystem';
import { LootSystem } from './systems/LootSystem';
import { InventorySystem } from './systems/InventorySystem';
import { DungeonGenerator } from './systems/DungeonGenerator';
import { DungeonRenderer } from './systems/DungeonRenderer';
import { SkillTreeUI } from './ui/SkillTreeUI';
import { InventoryUI } from './ui/InventoryUI';
import { Vector2 } from '@/utils/math/Vector2';
import { Particle } from './entities/Particle';
import { Item } from './entities/Item';

// Advanced systems
import { AudioSystem } from './systems/AudioSystem';
import { TouchManager } from '@/engine/input/TouchManager';
import { VirtualControls } from '@/engine/ui/VirtualControls';
import { ResponsiveManager } from '@/engine/ui/ResponsiveManager';
import { GameStateManager } from './systems/GameStateManager';
import { ErrorHandler } from '@/utils/error/ErrorHandler';
import { PerformanceDisplay } from '@/engine/ui/PerformanceDisplay';
import { SafeOperations } from '@/utils/safety/SafeOperations';

export interface UltimateGameConfig {
  performanceMonitor?: PerformanceMonitor;
  audioSystem?: AudioSystem;
  touchManager?: TouchManager;
  virtualControls?: VirtualControls;
  responsiveManager?: ResponsiveManager;
  gameStateManager?: GameStateManager;
}

export class UltimateGame {
  private canvas: HTMLCanvasElement;
  private renderer: CanvasRenderer;
  private inputManager: InputManager;
  private poolManager: ObjectPoolManager;
  private performanceMonitor: PerformanceMonitor;
  private errorHandler: ErrorHandler;
  
  // Advanced systems
  private audioSystem: AudioSystem;
  private touchManager?: TouchManager;
  private virtualControls?: VirtualControls;
  private responsiveManager?: ResponsiveManager;
  private gameStateManager: GameStateManager;
  private performanceDisplay: PerformanceDisplay;
  
  // Game systems
  private combatSystem: CombatSystem;
  private collisionSystem: CollisionSystem;
  private particleSystem: ParticleSystem;
  private progressionSystem: ProgressionSystem;
  private lootSystem: LootSystem;
  private inventorySystem: InventorySystem;
  private dungeonGenerator: DungeonGenerator;
  private dungeonRenderer: DungeonRenderer;
  
  // UI systems
  private skillTreeUI: SkillTreeUI;
  private inventoryUI: InventoryUI;
  
  // Game entities
  private player: Player;
  private enemies: Enemy[] = [];
  private bullets: Bullet[] = [];
  private particles: Particle[] = [];
  private items: Item[] = [];
  
  // Game state
  private isRunning: boolean = false;
  private isPaused: boolean = false;
  private gameTime: number = 0;
  private lastFrameTime: number = 0;
  private animationFrameId: number = 0;
  
  // Camera
  private camera: Vector2 = new Vector2(0, 0);
  private cameraTarget: Vector2 = new Vector2(0, 0);
  
  // UI state
  private showSkillTree: boolean = false;
  private showInventory: boolean = false;
  private showPerformance: boolean = true;

  constructor(canvas: HTMLCanvasElement, config: UltimateGameConfig = {}) {
    this.canvas = canvas;
    this.errorHandler = ErrorHandler.getInstance();
    
    try {
      // Initialize core systems
      this.renderer = new CanvasRenderer(canvas);
      this.inputManager = new InputManager(canvas);
      this.poolManager = new ObjectPoolManager();
      this.performanceMonitor = config.performanceMonitor || new PerformanceMonitor(true);
      
      // Initialize advanced systems
      this.audioSystem = config.audioSystem || new AudioSystem();
      this.touchManager = config.touchManager;
      this.virtualControls = config.virtualControls;
      this.responsiveManager = config.responsiveManager;
      this.gameStateManager = config.gameStateManager || new GameStateManager();
      
      // Initialize performance display
      this.performanceDisplay = new PerformanceDisplay(this.performanceMonitor, {
        position: { x: 10, y: 10 },
        showGraph: true,
        showProfiler: true,
        showAlerts: true
      });
      
      this.initializeSystems();
      this.setupEventListeners();
      
    } catch (error) {
      this.errorHandler.critical('Failed to initialize UltimateGame', error);
      throw error;
    }
  }

  private initializeSystems(): void {
    try {
      // Initialize game systems
      this.combatSystem = new CombatSystem(this.poolManager);
      this.collisionSystem = new CollisionSystem();
      this.particleSystem = new ParticleSystem(this.poolManager);
      this.progressionSystem = new ProgressionSystem();
      this.lootSystem = new LootSystem();
      this.inventorySystem = new InventorySystem();
      this.dungeonGenerator = new DungeonGenerator();
      this.dungeonRenderer = new DungeonRenderer();
      
      // Initialize UI systems
      this.skillTreeUI = new SkillTreeUI(this.progressionSystem);
      this.inventoryUI = new InventoryUI(this.inventorySystem);
      
      // Initialize player
      this.player = new Player(new Vector2(512, 384));
      
      // Setup object pools
      this.setupObjectPools();
      
      // Generate initial dungeon
      this.generateNewDungeon();
      
      this.errorHandler.info('All game systems initialized successfully');
      
    } catch (error) {
      this.errorHandler.error('Failed to initialize game systems', error);
      throw error;
    }
  }

  private setupObjectPools(): void {
    this.poolManager.createPool('bullets', () => new Bullet(new Vector2(0, 0), new Vector2(0, 0), 0, 'player'), 200);
    this.poolManager.createPool('particles', () => new Particle(new Vector2(0, 0), new Vector2(0, 0), '#ffffff', 1), 500);
    this.poolManager.createPool('enemies', () => new Enemy(new Vector2(0, 0), 'basic'), 50);
  }

  private setupEventListeners(): void {
    // Keyboard controls
    this.inputManager.onKeyDown('KeyP', () => this.togglePause());
    this.inputManager.onKeyDown('KeyI', () => this.toggleInventory());
    this.inputManager.onKeyDown('KeyK', () => this.toggleSkillTree());
    this.inputManager.onKeyDown('F11', () => this.toggleFullscreen());
    this.inputManager.onKeyDown('KeyF', () => this.toggleFullscreen());
    
    // Performance display toggle
    this.inputManager.onKeyDown('F3', () => this.togglePerformanceDisplay());
    
    // Weapon switching
    for (let i = 1; i <= 6; i++) {
      this.inputManager.onKeyDown(`Digit${i}`, () => this.switchWeapon(i - 1));
    }
    
    // Virtual controls integration
    if (this.virtualControls) {
      this.setupVirtualControlsIntegration();
    }
    
    // Responsive manager integration
    if (this.responsiveManager) {
      this.setupResponsiveIntegration();
    }
  }

  private setupVirtualControlsIntegration(): void {
    if (!this.virtualControls) return;
    
    // Movement joystick
    this.virtualControls.onJoystickMove('movement', (value) => {
      this.handleVirtualMovement(value);
    });
    
    // Attack button
    this.virtualControls.onButtonPress('attack', (pressed) => {
      this.handleVirtualAttack(pressed);
    });
    
    // Special button
    this.virtualControls.onButtonPress('special', (pressed) => {
      this.handleVirtualSpecial(pressed);
    });
  }

  private setupResponsiveIntegration(): void {
    if (!this.responsiveManager) return;
    
    this.responsiveManager.addResizeListener((screenInfo) => {
      this.handleScreenResize(screenInfo);
    });
    
    this.responsiveManager.addOrientationListener((orientation) => {
      this.handleOrientationChange(orientation);
    });
  }

  async initialize(): Promise<void> {
    try {
      // Initialize audio system
      await this.audioSystem.update(0);
      
      // Load initial character data
      const characterData = this.progressionSystem.createNewCharacter();
      this.player.applyCharacterData(characterData);
      
      // Setup audio listeners
      this.setupAudioIntegration();
      
      this.errorHandler.info('UltimateGame initialization complete');
      
    } catch (error) {
      this.errorHandler.error('Failed to complete game initialization', error);
      throw error;
    }
  }

  private setupAudioIntegration(): void {
    // Set listener position to player position
    this.audioSystem.setListenerPosition(this.player.getPosition());
    
    // Play background music
    this.audioSystem.playBackgroundMusic();
  }

  private generateNewDungeon(): void {
    try {
      const dungeon = this.dungeonGenerator.generate(20, 15, 8);
      this.dungeonRenderer.setDungeon(dungeon);
      
      // Spawn enemies in rooms
      this.spawnEnemiesInDungeon(dungeon);
      
      this.errorHandler.info('New dungeon generated');
      
    } catch (error) {
      this.errorHandler.error('Failed to generate dungeon', error);
    }
  }

  private spawnEnemiesInDungeon(dungeon: any): void {
    // Clear existing enemies
    this.enemies = [];
    
    // Spawn enemies in each room (except start room)
    for (let i = 1; i < dungeon.rooms.length; i++) {
      const room = dungeon.rooms[i];
      const enemyCount = Math.floor(Math.random() * 3) + 1;
      
      for (let j = 0; j < enemyCount; j++) {
        const position = new Vector2(
          room.x + Math.random() * room.width,
          room.y + Math.random() * room.height
        );
        
        const enemyTypes = ['basic', 'shooter', 'heavy', 'bomber'];
        const enemyType = enemyTypes[Math.floor(Math.random() * enemyTypes.length)];
        
        const enemy = this.poolManager.getFromPool('enemies') as Enemy;
        if (enemy) {
          enemy.reset(position, enemyType);
          this.enemies.push(enemy);
        }
      }
    }
  }

  start(): void {
    if (this.isRunning) return;
    
    this.isRunning = true;
    this.isPaused = false;
    this.lastFrameTime = performance.now();
    
    this.gameLoop();
    this.errorHandler.info('UltimateGame started');
  }

  private gameLoop = (): void => {
    if (!this.isRunning) return;
    
    this.animationFrameId = requestAnimationFrame(this.gameLoop);
    
    if (this.isPaused) return;
    
    const currentTime = performance.now();
    const deltaTime = (currentTime - this.lastFrameTime) / 1000;
    this.lastFrameTime = currentTime;
    
    // Performance monitoring
    this.performanceMonitor.startProfile('frame');
    this.performanceMonitor.startProfile('update');
    
    this.update(deltaTime);
    
    this.performanceMonitor.endProfile('update');
    this.performanceMonitor.startProfile('render');
    
    this.render();
    
    this.performanceMonitor.endProfile('render');
    this.performanceMonitor.endProfile('frame');
    
    // Update performance display
    this.performanceDisplay.update(deltaTime);
  };

  private update(deltaTime: number): void {
    this.gameTime += deltaTime;
    
    // Update input
    this.inputManager.update();
    
    // Update virtual controls
    if (this.virtualControls) {
      this.virtualControls.update(deltaTime);
    }
    
    // Update game state manager
    this.gameStateManager.update(deltaTime);
    
    // Update player
    this.updatePlayer(deltaTime);
    
    // Update enemies
    this.updateEnemies(deltaTime);
    
    // Update bullets
    this.updateBullets(deltaTime);
    
    // Update particles
    this.updateParticles(deltaTime);
    
    // Update items
    this.updateItems(deltaTime);
    
    // Update systems
    this.updateSystems(deltaTime);
    
    // Update camera
    this.updateCamera(deltaTime);
    
    // Update audio listener position
    this.audioSystem.setListenerPosition(this.player.getPosition());
    
    // Record performance metrics
    this.recordPerformanceMetrics();
  }

  // Virtual control handlers
  handleVirtualMovement(value: Vector2): void {
    if (this.player) {
      this.player.setVirtualMovement(value);
    }
  }

  handleVirtualAttack(pressed: boolean): void {
    if (this.player) {
      this.player.setVirtualAttack(pressed);
    }
  }

  handleVirtualSpecial(pressed: boolean): void {
    if (pressed && this.player) {
      this.player.useSpecialAbility();
    }
  }

  handleVirtualJump(pressed: boolean): void {
    if (pressed && this.player) {
      this.player.jump();
    }
  }

  private updatePlayer(deltaTime: number): void {
    this.player.update(deltaTime, this.inputManager);

    // Handle player shooting
    if (this.inputManager.isKeyPressed('Space') || this.inputManager.isMousePressed(0)) {
      this.handlePlayerShooting();
    }

    // Handle weapon switching
    this.handleWeaponSwitching();
  }

  private updateEnemies(deltaTime: number): void {
    for (let i = this.enemies.length - 1; i >= 0; i--) {
      const enemy = this.enemies[i];
      enemy.update(deltaTime, this.player.getPosition());

      // Remove dead enemies
      if (!enemy.isAlive()) {
        this.handleEnemyDeath(enemy);
        this.enemies.splice(i, 1);
        this.poolManager.returnToPool('enemies', enemy);
      }
    }
  }

  private updateBullets(deltaTime: number): void {
    for (let i = this.bullets.length - 1; i >= 0; i--) {
      const bullet = this.bullets[i];
      bullet.update(deltaTime);

      // Remove bullets that are out of bounds or expired
      if (bullet.shouldRemove()) {
        this.bullets.splice(i, 1);
        this.poolManager.returnToPool('bullets', bullet);
      }
    }
  }

  private updateParticles(deltaTime: number): void {
    for (let i = this.particles.length - 1; i >= 0; i--) {
      const particle = this.particles[i];
      particle.update(deltaTime);

      if (!particle.isAlive()) {
        this.particles.splice(i, 1);
        this.poolManager.returnToPool('particles', particle);
      }
    }
  }

  private updateItems(deltaTime: number): void {
    for (let i = this.items.length - 1; i >= 0; i--) {
      const item = this.items[i];
      item.update(deltaTime);

      // Check for player pickup
      if (this.collisionSystem.checkCollision(this.player, item)) {
        this.handleItemPickup(item);
        this.items.splice(i, 1);
      }
    }
  }

  private updateSystems(deltaTime: number): void {
    // Update collision system
    this.collisionSystem.update(deltaTime);

    // Update particle system
    this.particleSystem.update(deltaTime);

    // Update audio system
    this.audioSystem.update(deltaTime);

    // Check collisions
    this.handleCollisions();
  }

  private updateCamera(deltaTime: number): void {
    // Smooth camera following
    this.cameraTarget = this.player.getPosition().subtract(new Vector2(512, 384));
    this.camera = this.camera.lerp(this.cameraTarget, deltaTime * 5);
  }

  private recordPerformanceMetrics(): void {
    this.performanceMonitor.recordObjectCount(
      this.enemies.length + this.bullets.length + this.particles.length + this.items.length + 1
    );
  }

  private handlePlayerShooting(): void {
    const bullet = this.combatSystem.createPlayerBullet(this.player);
    if (bullet) {
      this.bullets.push(bullet);
      this.audioSystem.onPlayerShoot(this.player.getCurrentWeaponType(), this.player.getPosition());
    }
  }

  private handleWeaponSwitching(): void {
    for (let i = 1; i <= 6; i++) {
      if (this.inputManager.isKeyPressed(`Digit${i}`)) {
        this.switchWeapon(i - 1);
        break;
      }
    }
  }

  private handleCollisions(): void {
    // Player vs enemies
    for (const enemy of this.enemies) {
      if (this.collisionSystem.checkCollision(this.player, enemy)) {
        this.handlePlayerEnemyCollision(enemy);
      }
    }

    // Bullets vs enemies
    for (let i = this.bullets.length - 1; i >= 0; i--) {
      const bullet = this.bullets[i];
      if (bullet.getOwner() === 'player') {
        for (let j = this.enemies.length - 1; j >= 0; j--) {
          const enemy = this.enemies[j];
          if (this.collisionSystem.checkCollision(bullet, enemy)) {
            this.handleBulletEnemyCollision(bullet, enemy);
            this.bullets.splice(i, 1);
            this.poolManager.returnToPool('bullets', bullet);
            break;
          }
        }
      }
    }
  }

  private handlePlayerEnemyCollision(enemy: Enemy): void {
    const damage = enemy.getDamage();
    this.player.takeDamage(damage);
    this.audioSystem.onPlayerHit(damage);

    // Create damage particles
    this.createDamageParticles(this.player.getPosition(), damage);

    if (!this.player.isAlive()) {
      this.handlePlayerDeath();
    }
  }

  private handleBulletEnemyCollision(bullet: Bullet, enemy: Enemy): void {
    const damage = bullet.getDamage();
    enemy.takeDamage(damage);

    this.audioSystem.onEnemyHit(enemy.getPosition(), enemy.getType());
    this.createHitParticles(enemy.getPosition());

    if (!enemy.isAlive()) {
      this.handleEnemyDeath(enemy);
    }
  }

  private handleEnemyDeath(enemy: Enemy): void {
    this.audioSystem.onEnemyDeath(enemy.getPosition(), enemy.getType());
    this.createDeathParticles(enemy.getPosition());

    // Award experience
    const experience = enemy.getExperienceValue();
    this.progressionSystem.addExperience(experience);

    // Drop loot
    const loot = this.lootSystem.generateLoot(enemy.getLevel(), enemy.getType());
    if (loot) {
      this.items.push(loot);
    }
  }

  private handleItemPickup(item: Item): void {
    this.inventorySystem.addItem(item);
    this.audioSystem.onItemPickup(item.getPosition(), item.getRarity());
    this.createPickupParticles(item.getPosition());
  }

  private handlePlayerDeath(): void {
    this.gameStateManager.handlePlayerDeath();
    this.audioSystem.onPlayerHit(100); // Death sound
  }

  private createDamageParticles(position: Vector2, damage: number): void {
    for (let i = 0; i < 5; i++) {
      const particle = this.poolManager.getFromPool('particles') as Particle;
      if (particle) {
        particle.reset(
          position.add(new Vector2(Math.random() * 20 - 10, Math.random() * 20 - 10)),
          new Vector2(Math.random() * 100 - 50, Math.random() * 100 - 50),
          '#ff4444',
          1
        );
        this.particles.push(particle);
      }
    }
  }

  private createHitParticles(position: Vector2): void {
    for (let i = 0; i < 3; i++) {
      const particle = this.poolManager.getFromPool('particles') as Particle;
      if (particle) {
        particle.reset(
          position.add(new Vector2(Math.random() * 10 - 5, Math.random() * 10 - 5)),
          new Vector2(Math.random() * 80 - 40, Math.random() * 80 - 40),
          '#ffff44',
          0.5
        );
        this.particles.push(particle);
      }
    }
  }

  private createDeathParticles(position: Vector2): void {
    for (let i = 0; i < 8; i++) {
      const particle = this.poolManager.getFromPool('particles') as Particle;
      if (particle) {
        particle.reset(
          position.add(new Vector2(Math.random() * 15 - 7.5, Math.random() * 15 - 7.5)),
          new Vector2(Math.random() * 120 - 60, Math.random() * 120 - 60),
          '#ff8844',
          1.5
        );
        this.particles.push(particle);
      }
    }
  }

  private createPickupParticles(position: Vector2): void {
    for (let i = 0; i < 4; i++) {
      const particle = this.poolManager.getFromPool('particles') as Particle;
      if (particle) {
        particle.reset(
          position.add(new Vector2(Math.random() * 8 - 4, Math.random() * 8 - 4)),
          new Vector2(Math.random() * 60 - 30, Math.random() * 60 - 30),
          '#44ff44',
          0.8
        );
        this.particles.push(particle);
      }
    }
  }

  private render(): void {
    this.renderer.save();

    // Apply camera transform
    this.renderer.translate(-this.camera.x, -this.camera.y);

    // Render dungeon
    this.dungeonRenderer.render(this.renderer);

    // Render entities
    this.renderEntities();

    this.renderer.restore();

    // Render UI
    this.renderUI();
  }

  private renderEntities(): void {
    // Render items
    for (const item of this.items) {
      item.render(this.renderer);
    }

    // Render enemies
    for (const enemy of this.enemies) {
      enemy.render(this.renderer);
    }

    // Render player
    this.player.render(this.renderer);

    // Render bullets
    for (const bullet of this.bullets) {
      bullet.render(this.renderer);
    }

    // Render particles
    for (const particle of this.particles) {
      particle.render(this.renderer);
    }
  }

  private renderUI(): void {
    // Render player UI
    this.renderPlayerUI();

    // Render skill tree
    if (this.showSkillTree) {
      this.skillTreeUI.render(this.renderer);
    }

    // Render inventory
    if (this.showInventory) {
      this.inventoryUI.render(this.renderer);
    }

    // Render virtual controls
    if (this.virtualControls) {
      this.virtualControls.render(this.renderer);
    }

    // Render performance display
    if (this.showPerformance) {
      this.performanceDisplay.render(this.renderer);
    }
  }

  private renderPlayerUI(): void {
    const health = this.player.getHealth();
    const maxHealth = this.player.getMaxHealth();
    const mana = this.player.getMana();
    const maxMana = this.player.getMaxMana();

    // Health bar
    this.renderer.drawRect(20, 20, 200, 20, 'rgba(255, 0, 0, 0.3)');
    this.renderer.drawRect(20, 20, (health / maxHealth) * 200, 20, '#ff4444');
    this.renderer.drawText(`Health: ${health}/${maxHealth}`, 25, 35, '14px Arial', '#ffffff');

    // Mana bar
    this.renderer.drawRect(20, 50, 200, 20, 'rgba(0, 0, 255, 0.3)');
    this.renderer.drawRect(20, 50, (mana / maxMana) * 200, 20, '#4444ff');
    this.renderer.drawText(`Mana: ${mana}/${maxMana}`, 25, 65, '14px Arial', '#ffffff');

    // Level and experience
    const level = this.progressionSystem.getLevel();
    const experience = this.progressionSystem.getExperience();
    const nextLevelExp = this.progressionSystem.getExperienceForNextLevel();

    this.renderer.drawText(`Level: ${level}`, 20, 90, '16px Arial', '#ffffff');
    this.renderer.drawText(`EXP: ${experience}/${nextLevelExp}`, 20, 110, '14px Arial', '#ffffff');

    // Current weapon
    const weaponName = this.player.getCurrentWeaponName();
    this.renderer.drawText(`Weapon: ${weaponName}`, 20, 130, '14px Arial', '#ffffff');
  }

  // Control methods
  togglePause(): void {
    this.isPaused = !this.isPaused;
    this.gameStateManager.togglePause();
  }

  toggleInventory(): void {
    this.showInventory = !this.showInventory;
    this.audioSystem.onUIInteraction('click');
  }

  toggleSkillTree(): void {
    this.showSkillTree = !this.showSkillTree;
    this.audioSystem.onUIInteraction('click');
  }

  togglePerformanceDisplay(): void {
    this.showPerformance = !this.showPerformance;
    if (this.showPerformance) {
      this.performanceDisplay.show();
    } else {
      this.performanceDisplay.hide();
    }
  }

  async toggleFullscreen(): Promise<void> {
    if (this.responsiveManager) {
      await this.responsiveManager.toggleFullscreen();
    }
  }

  switchWeapon(weaponIndex: number): void {
    this.player.switchWeapon(weaponIndex);
    this.audioSystem.onUIInteraction('click');
  }

  // Event handlers
  handleScreenResize(screenInfo: any): void {
    this.errorHandler.info(`Screen resized: ${screenInfo.width}x${screenInfo.height}`);
  }

  handleOrientationChange(orientation: string): void {
    this.errorHandler.info(`Orientation changed to ${orientation}`);
  }

  // Getters
  getPlayer(): Player { return this.player; }
  getEnemies(): Enemy[] { return this.enemies; }
  getBullets(): Bullet[] { return this.bullets; }
  getParticles(): Particle[] { return this.particles; }
  getItems(): Item[] { return this.items; }

  isGameRunning(): boolean { return this.isRunning; }
  isGamePaused(): boolean { return this.isPaused; }
  getGameTime(): number { return this.gameTime; }

  // Cleanup
  destroy(): void {
    this.stop();

    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
    }

    this.inputManager.destroy();
    this.audioSystem.destroy();
    this.renderer.destroy();

    if (this.touchManager) {
      this.touchManager.destroy();
    }

    if (this.virtualControls) {
      this.virtualControls.destroy();
    }

    if (this.responsiveManager) {
      this.responsiveManager.destroy();
    }

    this.gameStateManager.destroy();

    this.errorHandler.info('UltimateGame destroyed');
  }

  stop(): void {
    this.isRunning = false;
    this.isPaused = false;
  }

  pause(): void {
    this.isPaused = true;
  }

  resume(): void {
    this.isPaused = false;
    this.lastFrameTime = performance.now();
  }
}
