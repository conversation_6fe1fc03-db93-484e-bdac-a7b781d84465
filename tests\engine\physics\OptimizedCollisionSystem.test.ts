import { describe, it, expect, beforeEach } from 'vitest';
import { OptimizedSpatialGrid, OptimizedCollisionSystem, SpatialObject } from '@/engine/physics/OptimizedCollisionSystem';
import { Vector2 } from '@/utils/math/Vector2';

describe('OptimizedSpatialGrid', () => {
  let grid: OptimizedSpatialGrid<SpatialObject>;

  beforeEach(() => {
    grid = new OptimizedSpatialGrid(64);
  });

  describe('addObject and getPotentialPairs', () => {
    it('should find potential collision pairs', () => {
      const obj1: SpatialObject = {
        position: new Vector2(10, 10),
        collisionRadius: 5,
        id: 'obj1'
      };

      const obj2: SpatialObject = {
        position: new Vector2(12, 12),
        collisionRadius: 5,
        id: 'obj2'
      };

      const obj3: SpatialObject = {
        position: new Vector2(100, 100),
        collisionRadius: 5,
        id: 'obj3'
      };

      grid.addObject(obj1);
      grid.addObject(obj2);
      grid.addObject(obj3);

      const pairs = grid.getPotentialPairs();
      
      // Should find pair between obj1 and obj2 (close together)
      expect(pairs.length).toBe(1);
      expect(pairs[0].objectA.id).toBe('obj1');
      expect(pairs[0].objectB.id).toBe('obj2');
    });

    it('should not find pairs for distant objects', () => {
      const obj1: SpatialObject = {
        position: new Vector2(0, 0),
        collisionRadius: 5,
        id: 'obj1'
      };

      const obj2: SpatialObject = {
        position: new Vector2(1000, 1000),
        collisionRadius: 5,
        id: 'obj2'
      };

      grid.addObject(obj1);
      grid.addObject(obj2);

      const pairs = grid.getPotentialPairs();
      expect(pairs.length).toBe(0);
    });

    it('should handle objects spanning multiple cells', () => {
      const largeObj: SpatialObject = {
        position: new Vector2(32, 32), // On cell boundary
        collisionRadius: 50, // Spans multiple cells
        id: 'large'
      };

      const smallObj: SpatialObject = {
        position: new Vector2(80, 80),
        collisionRadius: 5,
        id: 'small'
      };

      grid.addObject(largeObj);
      grid.addObject(smallObj);

      const pairs = grid.getPotentialPairs();
      expect(pairs.length).toBe(1);
    });
  });

  describe('getObjectsInRegion', () => {
    it('should return objects in specified region', () => {
      const obj1: SpatialObject = {
        position: new Vector2(10, 10),
        collisionRadius: 5,
        id: 'obj1'
      };

      const obj2: SpatialObject = {
        position: new Vector2(50, 50),
        collisionRadius: 5,
        id: 'obj2'
      };

      const obj3: SpatialObject = {
        position: new Vector2(200, 200),
        collisionRadius: 5,
        id: 'obj3'
      };

      grid.addObject(obj1);
      grid.addObject(obj2);
      grid.addObject(obj3);

      const objectsInRegion = grid.getObjectsInRegion(0, 0, 100, 100);
      
      expect(objectsInRegion.length).toBe(2);
      expect(objectsInRegion.map(obj => obj.id)).toContain('obj1');
      expect(objectsInRegion.map(obj => obj.id)).toContain('obj2');
      expect(objectsInRegion.map(obj => obj.id)).not.toContain('obj3');
    });
  });

  describe('clear', () => {
    it('should clear all objects from grid', () => {
      const obj: SpatialObject = {
        position: new Vector2(10, 10),
        collisionRadius: 5,
        id: 'obj'
      };

      grid.addObject(obj);
      expect(grid.getPotentialPairs().length).toBe(0);

      grid.clear();
      
      // Add same object again - should work without issues
      grid.addObject(obj);
      expect(grid.getPotentialPairs().length).toBe(0);
    });
  });
});

describe('OptimizedCollisionSystem', () => {
  let collisionSystem: OptimizedCollisionSystem;

  beforeEach(() => {
    collisionSystem = new OptimizedCollisionSystem(64);
  });

  describe('checkCollisions', () => {
    it('should detect collisions between different groups', () => {
      const groupA: SpatialObject[] = [
        {
          position: new Vector2(10, 10),
          collisionRadius: 5,
          id: 'a1'
        }
      ];

      const groupB: SpatialObject[] = [
        {
          position: new Vector2(12, 12),
          collisionRadius: 5,
          id: 'b1'
        }
      ];

      let collisionDetected = false;
      collisionSystem.checkCollisions(groupA, groupB, () => {
        collisionDetected = true;
      });

      expect(collisionDetected).toBe(true);
    });

    it('should not detect collisions for distant objects', () => {
      const groupA: SpatialObject[] = [
        {
          position: new Vector2(0, 0),
          collisionRadius: 5,
          id: 'a1'
        }
      ];

      const groupB: SpatialObject[] = [
        {
          position: new Vector2(100, 100),
          collisionRadius: 5,
          id: 'b1'
        }
      ];

      let collisionDetected = false;
      collisionSystem.checkCollisions(groupA, groupB, () => {
        collisionDetected = true;
      });

      expect(collisionDetected).toBe(false);
    });

    it('should respect collision check budget', () => {
      collisionSystem.setCollisionBudget(1);

      const groupA: SpatialObject[] = [];
      const groupB: SpatialObject[] = [];

      // Create many overlapping objects
      for (let i = 0; i < 10; i++) {
        groupA.push({
          position: new Vector2(i, 0),
          collisionRadius: 5,
          id: `a${i}`
        });
        groupB.push({
          position: new Vector2(i + 1, 0),
          collisionRadius: 5,
          id: `b${i}`
        });
      }

      let collisionCount = 0;
      collisionSystem.checkCollisions(groupA, groupB, () => {
        collisionCount++;
      });

      // Should stop at budget limit
      expect(collisionCount).toBeLessThanOrEqual(1);
    });
  });

  describe('preciseCollisionCheck', () => {
    it('should return detailed collision information', () => {
      const objA: SpatialObject = {
        position: new Vector2(0, 0),
        collisionRadius: 10,
        id: 'a'
      };

      const objB: SpatialObject = {
        position: new Vector2(15, 0),
        collisionRadius: 10,
        id: 'b'
      };

      const result = collisionSystem.preciseCollisionCheck(objA, objB);

      expect(result.hasCollision).toBe(true);
      expect(result.distance).toBe(15);
      expect(result.overlap).toBe(5); // 20 - 15
      expect(result.normal).toBeDefined();
      expect(result.point).toBeDefined();
    });

    it('should handle objects at same position', () => {
      const objA: SpatialObject = {
        position: new Vector2(0, 0),
        collisionRadius: 5,
        id: 'a'
      };

      const objB: SpatialObject = {
        position: new Vector2(0, 0),
        collisionRadius: 5,
        id: 'b'
      };

      const result = collisionSystem.preciseCollisionCheck(objA, objB);

      expect(result.hasCollision).toBe(true);
      expect(result.distance).toBe(0);
      expect(result.overlap).toBe(10);
      expect(result.normal).toBeDefined();
    });
  });

  describe('queryCircle', () => {
    it('should return objects within circular area', () => {
      const objects: SpatialObject[] = [
        { position: new Vector2(0, 0), collisionRadius: 5, id: 'obj1' },
        { position: new Vector2(10, 0), collisionRadius: 5, id: 'obj2' },
        { position: new Vector2(50, 0), collisionRadius: 5, id: 'obj3' }
      ];

      // Add objects to system
      collisionSystem.checkCollisions(objects, [], () => {});

      const result = collisionSystem.queryCircle(new Vector2(0, 0), 20);
      
      expect(result.length).toBe(2);
      expect(result.map(obj => obj.id)).toContain('obj1');
      expect(result.map(obj => obj.id)).toContain('obj2');
      expect(result.map(obj => obj.id)).not.toContain('obj3');
    });
  });

  describe('queryRectangle', () => {
    it('should return objects within rectangular area', () => {
      const objects: SpatialObject[] = [
        { position: new Vector2(5, 5), collisionRadius: 2, id: 'obj1' },
        { position: new Vector2(15, 15), collisionRadius: 2, id: 'obj2' },
        { position: new Vector2(50, 50), collisionRadius: 2, id: 'obj3' }
      ];

      // Add objects to system
      collisionSystem.checkCollisions(objects, [], () => {});

      const result = collisionSystem.queryRectangle(0, 0, 20, 20);
      
      expect(result.length).toBe(2);
      expect(result.map(obj => obj.id)).toContain('obj1');
      expect(result.map(obj => obj.id)).toContain('obj2');
      expect(result.map(obj => obj.id)).not.toContain('obj3');
    });
  });

  describe('performance tracking', () => {
    it('should track collision checks', () => {
      collisionSystem.resetCollisionChecks();
      expect(collisionSystem.getCollisionChecks()).toBe(0);

      const groupA: SpatialObject[] = [
        { position: new Vector2(0, 0), collisionRadius: 5, id: 'a1' }
      ];
      const groupB: SpatialObject[] = [
        { position: new Vector2(1, 1), collisionRadius: 5, id: 'b1' }
      ];

      collisionSystem.checkCollisions(groupA, groupB, () => {});
      
      expect(collisionSystem.getCollisionChecks()).toBeGreaterThan(0);
    });

    it('should provide performance statistics', () => {
      const stats = collisionSystem.getPerformanceStats();
      
      expect(stats).toHaveProperty('checksPerformed');
      expect(stats).toHaveProperty('maxChecks');
      expect(stats).toHaveProperty('efficiency');
      expect(typeof stats.efficiency).toBe('number');
    });
  });
});
