/**
 * Advanced performance monitoring system for tracking FPS, frame times, memory usage, and profiling
 */

export interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  averageFrameTime: number;
  minFrameTime: number;
  maxFrameTime: number;
  memoryUsage: number;
  objectCount: number;
  drawCalls: number;
  updateTime: number;
  renderTime: number;
  gcTime: number;
  timestamp: number;
}

export interface ProfilerEntry {
  name: string;
  startTime: number;
  endTime: number;
  duration: number;
  calls: number;
  totalTime: number;
  averageTime: number;
  minTime: number;
  maxTime: number;
}

export interface PerformanceAlert {
  type: 'fps_drop' | 'memory_spike' | 'long_frame' | 'gc_pressure';
  message: string;
  value: number;
  threshold: number;
  timestamp: number;
}

export class PerformanceMonitor {
  private frameStartTime: number = 0;
  private frameTimes: number[] = [];
  private readonly maxSamples: number = 60;
  private enabled: boolean;

  private fpsUpdateInterval: number = 1000; // Update FPS every second
  private lastFpsUpdate: number = 0;
  private currentFPS: number = 60;

  // Reduce frequency of expensive operations in production
  private isProduction: boolean = false;
  private lightweightMode: boolean = false;

  // Advanced monitoring
  private currentMetrics: PerformanceMetrics;
  private metricsHistory: PerformanceMetrics[] = [];
  private maxMetricsHistory: number = 300;

  // Profiler data
  private profilerEntries: Map<string, ProfilerEntry> = new Map();
  private activeProfiles: Map<string, number> = new Map();

  // Performance alerts
  private alerts: PerformanceAlert[] = [];
  private alertThresholds = {
    minFPS: 30,
    maxFrameTime: 33.33,
    maxMemoryMB: 100,
    maxGCTime: 5
  };

  // Memory monitoring
  private lastMemoryCheck: number = 0;
  private memoryCheckInterval: number = 1000;
  
  constructor(enabled: boolean = true, lightweightMode: boolean = false) {
    this.enabled = enabled;
    this.lightweightMode = lightweightMode;
    this.isProduction = lightweightMode || (typeof window !== 'undefined' && !window.location.hostname.includes('localhost'));
    this.currentMetrics = this.createEmptyMetrics();
    this.lastMemoryCheck = performance.now();

    // Adjust intervals for lightweight mode
    if (this.lightweightMode) {
      this.fpsUpdateInterval = 2000; // Update less frequently
      this.memoryCheckInterval = 5000; // Check memory less often
      this.maxSamples = 30; // Keep fewer samples
    }
  }

  private createEmptyMetrics(): PerformanceMetrics {
    return {
      fps: 0,
      frameTime: 0,
      averageFrameTime: 0,
      minFrameTime: Infinity,
      maxFrameTime: 0,
      memoryUsage: 0,
      objectCount: 0,
      drawCalls: 0,
      updateTime: 0,
      renderTime: 0,
      gcTime: 0,
      timestamp: performance.now()
    };
  }

  startFrame(): void {
    if (!this.enabled) return;
    this.frameStartTime = performance.now();
  }

  endFrame(): void {
    if (!this.enabled) return;
    
    const frameTime = performance.now() - this.frameStartTime;
    this.frameTimes.push(frameTime);
    
    // Keep only the last N samples
    if (this.frameTimes.length > this.maxSamples) {
      this.frameTimes.shift();
    }
    
    // Update FPS calculation
    const now = performance.now();
    if (now - this.lastFpsUpdate >= this.fpsUpdateInterval) {
      this.updateFPS();
      this.lastFpsUpdate = now;
    }
  }

  private updateFPS(): void {
    if (this.frameTimes.length === 0) return;
    
    const averageFrameTime = this.getAverageFrameTime();
    this.currentFPS = 1000 / averageFrameTime;
  }

  getFPS(): number {
    return this.currentFPS;
  }

  getAverageFrameTime(): number {
    if (this.frameTimes.length === 0) return 16.67; // Default to 60 FPS
    
    const sum = this.frameTimes.reduce((a, b) => a + b, 0);
    return sum / this.frameTimes.length;
  }

  getMinFrameTime(): number {
    if (this.frameTimes.length === 0) return 0;
    return Math.min(...this.frameTimes);
  }

  getMaxFrameTime(): number {
    if (this.frameTimes.length === 0) return 0;
    return Math.max(...this.frameTimes);
  }

  getPerformanceReport(): {
    fps: number;
    avgFrameTime: number;
    minFrameTime: number;
    maxFrameTime: number;
    samples: number;
  } {
    return {
      fps: this.getFPS(),
      avgFrameTime: this.getAverageFrameTime(),
      minFrameTime: this.getMinFrameTime(),
      maxFrameTime: this.getMaxFrameTime(),
      samples: this.frameTimes.length
    };
  }

  reset(): void {
    this.frameTimes = [];
    this.currentFPS = 60;
    this.lastFpsUpdate = performance.now();
  }

  setEnabled(enabled: boolean): void {
    this.enabled = enabled;
    if (!enabled) {
      this.reset();
    }
  }

  // Advanced profiling methods
  startProfile(name: string): void {
    if (!this.enabled) return;
    this.activeProfiles.set(name, performance.now());
  }

  endProfile(name: string): void {
    if (!this.enabled) return;

    const startTime = this.activeProfiles.get(name);
    if (startTime === undefined) return;

    const endTime = performance.now();
    const duration = endTime - startTime;

    this.activeProfiles.delete(name);

    let entry = this.profilerEntries.get(name);
    if (!entry) {
      entry = {
        name,
        startTime,
        endTime,
        duration,
        calls: 0,
        totalTime: 0,
        averageTime: 0,
        minTime: Infinity,
        maxTime: 0
      };
      this.profilerEntries.set(name, entry);
    }

    entry.calls++;
    entry.totalTime += duration;
    entry.averageTime = entry.totalTime / entry.calls;
    entry.minTime = Math.min(entry.minTime, duration);
    entry.maxTime = Math.max(entry.maxTime, duration);
    entry.endTime = endTime;
    entry.duration = duration;
  }

  // Memory and object tracking
  recordObjectCount(count: number): void {
    this.currentMetrics.objectCount = count;
  }

  recordDrawCalls(count: number): void {
    this.currentMetrics.drawCalls = count;
  }

  recordUpdateTime(time: number): void {
    this.currentMetrics.updateTime = time;
  }

  recordRenderTime(time: number): void {
    this.currentMetrics.renderTime = time;
  }

  // Get advanced metrics
  getCurrentMetrics(): PerformanceMetrics {
    this.updateAdvancedMetrics();
    return { ...this.currentMetrics };
  }

  private updateAdvancedMetrics(): void {
    const currentTime = performance.now();

    // Update basic metrics
    this.currentMetrics.fps = this.currentFPS;
    this.currentMetrics.frameTime = this.frameTimes[this.frameTimes.length - 1] || 0;
    this.currentMetrics.timestamp = currentTime;

    // Skip expensive calculations in lightweight mode
    if (!this.lightweightMode) {
      this.currentMetrics.averageFrameTime = this.getAverageFrameTime();
      this.currentMetrics.minFrameTime = this.getMinFrameTime();
      this.currentMetrics.maxFrameTime = this.getMaxFrameTime();

      // Update memory if available
      if (currentTime - this.lastMemoryCheck > this.memoryCheckInterval) {
        this.updateMemoryMetrics();
        this.lastMemoryCheck = currentTime;
      }

      // Check for performance alerts
      this.checkPerformanceAlerts();
    }
  }

  private updateMemoryMetrics(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.currentMetrics.memoryUsage = memory.usedJSHeapSize / (1024 * 1024);
    }
  }

  private checkPerformanceAlerts(): void {
    const currentTime = performance.now();

    if (this.currentMetrics.fps < this.alertThresholds.minFPS) {
      this.addAlert('fps_drop',
        `FPS dropped to ${this.currentMetrics.fps.toFixed(1)}`,
        this.currentMetrics.fps,
        this.alertThresholds.minFPS,
        currentTime
      );
    }

    if (this.currentMetrics.frameTime > this.alertThresholds.maxFrameTime) {
      this.addAlert('long_frame',
        `Frame time: ${this.currentMetrics.frameTime.toFixed(2)}ms`,
        this.currentMetrics.frameTime,
        this.alertThresholds.maxFrameTime,
        currentTime
      );
    }
  }

  private addAlert(type: PerformanceAlert['type'], message: string, value: number, threshold: number, timestamp: number): void {
    const recentAlert = this.alerts.find(alert =>
      alert.type === type && (timestamp - alert.timestamp) < 1000
    );

    if (!recentAlert) {
      this.alerts.push({ type, message, value, threshold, timestamp });

      if (this.alerts.length > 50) {
        this.alerts.shift();
      }

      console.warn(`⚠️ Performance Alert: ${message}`);
    }
  }

  getProfilerData(): ProfilerEntry[] {
    return Array.from(this.profilerEntries.values());
  }

  getAlerts(): PerformanceAlert[] {
    return [...this.alerts];
  }

  clearProfilerData(): void {
    this.profilerEntries.clear();
    this.activeProfiles.clear();
  }

  clearAlerts(): void {
    this.alerts = [];
  }

  exportData(): string {
    const data = {
      currentMetrics: this.currentMetrics,
      profilerData: this.getProfilerData(),
      alerts: this.alerts,
      performanceReport: this.getPerformanceReport()
    };

    return JSON.stringify(data, null, 2);
  }
}
