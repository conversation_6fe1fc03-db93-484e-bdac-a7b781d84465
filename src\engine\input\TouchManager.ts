/**
 * Touch input management system for mobile devices
 */

import { Vector2 } from '@/utils/math/Vector2';
import { <PERSON><PERSON>rHand<PERSON> } from '@/utils/error/ErrorHandler';

export interface TouchPoint {
  id: number;
  position: Vector2;
  startPosition: Vector2;
  deltaPosition: Vector2;
  startTime: number;
  duration: number;
  pressure: number;
  isActive: boolean;
}

export interface TouchGesture {
  type: 'tap' | 'double_tap' | 'long_press' | 'swipe' | 'pinch' | 'rotate';
  position: Vector2;
  startPosition: Vector2;
  deltaPosition: Vector2;
  distance: number;
  angle: number;
  scale: number;
  rotation: number;
  duration: number;
  velocity: Vector2;
}

export interface TouchConfig {
  tapThreshold: number;
  doubleTapThreshold: number;
  longPressThreshold: number;
  swipeThreshold: number;
  pinchThreshold: number;
  maxTapDuration: number;
  maxDoubleTapInterval: number;
  preventDefaultEvents: boolean;
}

export class TouchManager {
  private errorHandler = ErrorHandler.getInstance();
  private canvas: HTMLCanvasElement;
  private isEnabled: boolean = true;
  private config: TouchConfig;
  
  // Touch tracking
  private activeTouches: Map<number, TouchPoint> = new Map();
  private touchHistory: TouchPoint[] = [];
  private maxHistorySize: number = 100;
  
  // Gesture detection
  private lastTapTime: number = 0;
  private lastTapPosition: Vector2 = new Vector2();
  private gestureListeners: Map<string, ((gesture: TouchGesture) => void)[]> = new Map();
  
  // Multi-touch state
  private initialDistance: number = 0;
  private initialAngle: number = 0;
  private initialScale: number = 1;
  private initialRotation: number = 0;

  constructor(canvas: HTMLCanvasElement, config: Partial<TouchConfig> = {}) {
    this.canvas = canvas;
    this.config = {
      tapThreshold: 10,
      doubleTapThreshold: 300,
      longPressThreshold: 500,
      swipeThreshold: 50,
      pinchThreshold: 20,
      maxTapDuration: 200,
      maxDoubleTapInterval: 300,
      preventDefaultEvents: true,
      ...config
    };
    
    this.setupTouchEvents();
  }

  private setupTouchEvents(): void {
    try {
      // Touch events
      this.canvas.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
      this.canvas.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
      this.canvas.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });
      this.canvas.addEventListener('touchcancel', this.handleTouchCancel.bind(this), { passive: false });
      
      // Prevent context menu on long press
      this.canvas.addEventListener('contextmenu', (e) => {
        if (this.config.preventDefaultEvents) {
          e.preventDefault();
        }
      });
      
      // Prevent zoom on double tap
      this.canvas.addEventListener('gesturestart', (e) => {
        if (this.config.preventDefaultEvents) {
          e.preventDefault();
        }
      });
      
      console.log('🤏 Touch events initialized');
    } catch (error) {
      this.errorHandler.error('Failed to setup touch events', error, 'touch');
    }
  }

  private handleTouchStart(event: TouchEvent): void {
    if (!this.isEnabled) return;
    
    if (this.config.preventDefaultEvents) {
      event.preventDefault();
    }
    
    try {
      const currentTime = performance.now();
      
      for (let i = 0; i < event.changedTouches.length; i++) {
        const touch = event.changedTouches[i];
        const position = this.getTouchPosition(touch);
        
        const touchPoint: TouchPoint = {
          id: touch.identifier,
          position: position.copy(),
          startPosition: position.copy(),
          deltaPosition: new Vector2(0, 0),
          startTime: currentTime,
          duration: 0,
          pressure: touch.force || 1,
          isActive: true
        };
        
        this.activeTouches.set(touch.identifier, touchPoint);
        this.addToHistory(touchPoint);
      }
      
      // Handle multi-touch gestures
      if (this.activeTouches.size === 2) {
        this.initializeMultiTouch();
      }
      
    } catch (error) {
      this.errorHandler.error('Touch start handling failed', error, 'touch');
    }
  }

  private handleTouchMove(event: TouchEvent): void {
    if (!this.isEnabled) return;
    
    if (this.config.preventDefaultEvents) {
      event.preventDefault();
    }
    
    try {
      const currentTime = performance.now();
      
      for (let i = 0; i < event.changedTouches.length; i++) {
        const touch = event.changedTouches[i];
        const touchPoint = this.activeTouches.get(touch.identifier);
        
        if (!touchPoint) continue;
        
        const newPosition = this.getTouchPosition(touch);
        touchPoint.deltaPosition = newPosition.subtract(touchPoint.position);
        touchPoint.position = newPosition;
        touchPoint.duration = currentTime - touchPoint.startTime;
        touchPoint.pressure = touch.force || 1;
        
        this.addToHistory(touchPoint);
      }
      
      // Handle multi-touch gestures
      if (this.activeTouches.size === 2) {
        this.handleMultiTouchGestures();
      }
      
    } catch (error) {
      this.errorHandler.error('Touch move handling failed', error, 'touch');
    }
  }

  private handleTouchEnd(event: TouchEvent): void {
    if (!this.isEnabled) return;
    
    if (this.config.preventDefaultEvents) {
      event.preventDefault();
    }
    
    try {
      const currentTime = performance.now();
      
      for (let i = 0; i < event.changedTouches.length; i++) {
        const touch = event.changedTouches[i];
        const touchPoint = this.activeTouches.get(touch.identifier);
        
        if (!touchPoint) continue;
        
        touchPoint.isActive = false;
        touchPoint.duration = currentTime - touchPoint.startTime;
        
        // Detect gestures
        this.detectGestures(touchPoint);
        
        // Remove from active touches
        this.activeTouches.delete(touch.identifier);
      }
      
    } catch (error) {
      this.errorHandler.error('Touch end handling failed', error, 'touch');
    }
  }

  private handleTouchCancel(event: TouchEvent): void {
    if (!this.isEnabled) return;
    
    try {
      for (let i = 0; i < event.changedTouches.length; i++) {
        const touch = event.changedTouches[i];
        this.activeTouches.delete(touch.identifier);
      }
    } catch (error) {
      this.errorHandler.error('Touch cancel handling failed', error, 'touch');
    }
  }

  private getTouchPosition(touch: Touch): Vector2 {
    const rect = this.canvas.getBoundingClientRect();
    return new Vector2(
      touch.clientX - rect.left,
      touch.clientY - rect.top
    );
  }

  private addToHistory(touchPoint: TouchPoint): void {
    this.touchHistory.push({ ...touchPoint });
    
    if (this.touchHistory.length > this.maxHistorySize) {
      this.touchHistory.shift();
    }
  }

  private detectGestures(touchPoint: TouchPoint): void {
    const distance = touchPoint.position.distance(touchPoint.startPosition);
    const duration = touchPoint.duration;
    
    // Tap detection
    if (distance < this.config.tapThreshold && duration < this.config.maxTapDuration) {
      this.handleTap(touchPoint);
    }
    
    // Long press detection
    else if (distance < this.config.tapThreshold && duration > this.config.longPressThreshold) {
      this.handleLongPress(touchPoint);
    }
    
    // Swipe detection
    else if (distance > this.config.swipeThreshold) {
      this.handleSwipe(touchPoint);
    }
  }

  private handleTap(touchPoint: TouchPoint): void {
    const currentTime = performance.now();
    const timeSinceLastTap = currentTime - this.lastTapTime;
    const distanceFromLastTap = touchPoint.position.distance(this.lastTapPosition);
    
    // Double tap detection
    if (timeSinceLastTap < this.config.maxDoubleTapInterval && 
        distanceFromLastTap < this.config.doubleTapThreshold) {
      
      const gesture: TouchGesture = {
        type: 'double_tap',
        position: touchPoint.position.copy(),
        startPosition: touchPoint.startPosition.copy(),
        deltaPosition: touchPoint.deltaPosition.copy(),
        distance: touchPoint.position.distance(touchPoint.startPosition),
        angle: 0,
        scale: 1,
        rotation: 0,
        duration: touchPoint.duration,
        velocity: this.calculateVelocity(touchPoint)
      };
      
      this.emitGesture(gesture);
      this.lastTapTime = 0; // Reset to prevent triple tap
    } else {
      // Single tap
      const gesture: TouchGesture = {
        type: 'tap',
        position: touchPoint.position.copy(),
        startPosition: touchPoint.startPosition.copy(),
        deltaPosition: touchPoint.deltaPosition.copy(),
        distance: touchPoint.position.distance(touchPoint.startPosition),
        angle: 0,
        scale: 1,
        rotation: 0,
        duration: touchPoint.duration,
        velocity: this.calculateVelocity(touchPoint)
      };
      
      this.emitGesture(gesture);
      this.lastTapTime = currentTime;
      this.lastTapPosition = touchPoint.position.copy();
    }
  }

  private handleLongPress(touchPoint: TouchPoint): void {
    const gesture: TouchGesture = {
      type: 'long_press',
      position: touchPoint.position.copy(),
      startPosition: touchPoint.startPosition.copy(),
      deltaPosition: touchPoint.deltaPosition.copy(),
      distance: touchPoint.position.distance(touchPoint.startPosition),
      angle: 0,
      scale: 1,
      rotation: 0,
      duration: touchPoint.duration,
      velocity: this.calculateVelocity(touchPoint)
    };
    
    this.emitGesture(gesture);
  }

  private handleSwipe(touchPoint: TouchPoint): void {
    const delta = touchPoint.position.subtract(touchPoint.startPosition);
    const angle = Math.atan2(delta.y, delta.x);
    
    const gesture: TouchGesture = {
      type: 'swipe',
      position: touchPoint.position.copy(),
      startPosition: touchPoint.startPosition.copy(),
      deltaPosition: delta,
      distance: touchPoint.position.distance(touchPoint.startPosition),
      angle: angle,
      scale: 1,
      rotation: 0,
      duration: touchPoint.duration,
      velocity: this.calculateVelocity(touchPoint)
    };
    
    this.emitGesture(gesture);
  }

  private initializeMultiTouch(): void {
    const touches = Array.from(this.activeTouches.values());
    if (touches.length !== 2) return;
    
    const touch1 = touches[0];
    const touch2 = touches[1];
    
    this.initialDistance = touch1.position.distance(touch2.position);
    this.initialAngle = Math.atan2(
      touch2.position.y - touch1.position.y,
      touch2.position.x - touch1.position.x
    );
    this.initialScale = 1;
    this.initialRotation = 0;
  }

  private handleMultiTouchGestures(): void {
    const touches = Array.from(this.activeTouches.values());
    if (touches.length !== 2) return;
    
    const touch1 = touches[0];
    const touch2 = touches[1];
    
    const currentDistance = touch1.position.distance(touch2.position);
    const currentAngle = Math.atan2(
      touch2.position.y - touch1.position.y,
      touch2.position.x - touch1.position.x
    );
    
    const scale = currentDistance / this.initialDistance;
    const rotation = currentAngle - this.initialAngle;
    
    // Pinch gesture
    if (Math.abs(scale - 1) > 0.1) {
      const centerPosition = new Vector2(
        (touch1.position.x + touch2.position.x) / 2,
        (touch1.position.y + touch2.position.y) / 2
      );
      
      const gesture: TouchGesture = {
        type: 'pinch',
        position: centerPosition,
        startPosition: centerPosition,
        deltaPosition: new Vector2(0, 0),
        distance: currentDistance,
        angle: currentAngle,
        scale: scale,
        rotation: rotation,
        duration: Math.max(touch1.duration, touch2.duration),
        velocity: new Vector2(0, 0)
      };
      
      this.emitGesture(gesture);
    }
  }

  private calculateVelocity(touchPoint: TouchPoint): Vector2 {
    if (touchPoint.duration === 0) return new Vector2(0, 0);
    
    const distance = touchPoint.position.subtract(touchPoint.startPosition);
    const timeInSeconds = touchPoint.duration / 1000;
    
    return new Vector2(
      distance.x / timeInSeconds,
      distance.y / timeInSeconds
    );
  }

  private emitGesture(gesture: TouchGesture): void {
    const listeners = this.gestureListeners.get(gesture.type) || [];
    
    for (const listener of listeners) {
      try {
        listener(gesture);
      } catch (error) {
        this.errorHandler.error(`Gesture listener failed for ${gesture.type}`, error, 'touch');
      }
    }
  }

  // Public API
  addGestureListener(gestureType: string, listener: (gesture: TouchGesture) => void): void {
    if (!this.gestureListeners.has(gestureType)) {
      this.gestureListeners.set(gestureType, []);
    }
    
    this.gestureListeners.get(gestureType)!.push(listener);
  }

  removeGestureListener(gestureType: string, listener: (gesture: TouchGesture) => void): void {
    const listeners = this.gestureListeners.get(gestureType);
    if (!listeners) return;
    
    const index = listeners.indexOf(listener);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }

  getActiveTouches(): TouchPoint[] {
    return Array.from(this.activeTouches.values());
  }

  getTouchCount(): number {
    return this.activeTouches.size;
  }

  isMultiTouch(): boolean {
    return this.activeTouches.size > 1;
  }

  enable(): void {
    this.isEnabled = true;
  }

  disable(): void {
    this.isEnabled = false;
    this.activeTouches.clear();
  }

  isEnabled(): boolean {
    return this.isEnabled;
  }

  // Configuration
  updateConfig(config: Partial<TouchConfig>): void {
    this.config = { ...this.config, ...config };
  }

  getConfig(): TouchConfig {
    return { ...this.config };
  }

  // Cleanup
  destroy(): void {
    this.disable();
    this.gestureListeners.clear();
    this.touchHistory = [];
    
    // Remove event listeners
    this.canvas.removeEventListener('touchstart', this.handleTouchStart.bind(this));
    this.canvas.removeEventListener('touchmove', this.handleTouchMove.bind(this));
    this.canvas.removeEventListener('touchend', this.handleTouchEnd.bind(this));
    this.canvas.removeEventListener('touchcancel', this.handleTouchCancel.bind(this));
  }
}
