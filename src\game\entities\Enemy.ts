/**
 * Enemy entity with AI behavior and bullet hell patterns
 */

import { Vector2 } from '@/utils/math/Vector2';
import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';
import { Player } from './Player';

export interface EnemyStats {
  health: number;
  maxHealth: number;
  damage: number;
  speed: number;
  attackRange: number;
  attackRate: number;
  experienceValue: number;
}

export interface EnemyConfig {
  size: number;
  collisionRadius: number;
  color: string;
  aiType: 'chaser' | 'shooter' | 'circler' | 'bomber';
  bulletPattern: 'single' | 'spread' | 'circle' | 'spiral';
}

export class Enemy {
  public position: Vector2;
  public velocity: Vector2;
  public stats: EnemyStats;
  public config: EnemyConfig;
  
  // State
  public isAlive: boolean = true;
  public isActive: boolean = true;
  
  // AI
  public target: Player | null = null;
  public attackCooldown: number = 0;
  public aiTimer: number = 0;
  public aiState: string = 'idle';
  
  // Animation
  public animationTime: number = 0;
  public hitFlashTimer: number = 0;
  
  // Bullet hell patterns
  public patternPhase: number = 0;
  public burstCount: number = 0;

  constructor(x: number, y: number, type: string = 'basic') {
    this.position = new Vector2(x, y);
    this.velocity = new Vector2(0, 0);
    
    this.initializeByType(type);
  }

  private initializeByType(type: string): void {
    switch (type) {
      case 'basic':
        this.stats = {
          health: 30,
          maxHealth: 30,
          damage: 15,
          speed: 100,
          attackRange: 200,
          attackRate: 1, // attacks per second
          experienceValue: 10
        };
        this.config = {
          size: 24,
          collisionRadius: 12,
          color: '#ff6b6b',
          aiType: 'chaser',
          bulletPattern: 'single'
        };
        break;
        
      case 'shooter':
        this.stats = {
          health: 20,
          maxHealth: 20,
          damage: 12,
          speed: 80,
          attackRange: 300,
          attackRate: 2,
          experienceValue: 15
        };
        this.config = {
          size: 20,
          collisionRadius: 10,
          color: '#ff9800',
          aiType: 'shooter',
          bulletPattern: 'spread'
        };
        break;
        
      case 'heavy':
        this.stats = {
          health: 80,
          maxHealth: 80,
          damage: 25,
          speed: 60,
          attackRange: 150,
          attackRate: 0.5,
          experienceValue: 30
        };
        this.config = {
          size: 36,
          collisionRadius: 18,
          color: '#9c27b0',
          aiType: 'circler',
          bulletPattern: 'circle'
        };
        break;
        
      default:
        this.initializeByType('basic');
    }
  }

  update(deltaTime: number, player: Player, gameObjectManager: any): void {
    if (!this.isAlive || !this.isActive) return;

    this.target = player;
    this.updateAI(deltaTime);
    this.updateMovement(deltaTime);
    this.updateCombat(deltaTime, gameObjectManager);
    this.updateAnimation(deltaTime);
    this.updateTimers(deltaTime);
  }

  private updateAI(deltaTime: number): void {
    if (!this.target) return;

    this.aiTimer += deltaTime;
    const distanceToPlayer = this.position.distance(this.target.position);

    switch (this.config.aiType) {
      case 'chaser':
        this.updateChaserAI(distanceToPlayer);
        break;
      case 'shooter':
        this.updateShooterAI(distanceToPlayer);
        break;
      case 'circler':
        this.updateCirclerAI(distanceToPlayer);
        break;
      case 'bomber':
        this.updateBomberAI(distanceToPlayer);
        break;
    }
  }

  private updateChaserAI(distanceToPlayer: number): void {
    if (!this.target) return;
    
    // Simple chase behavior
    const direction = new Vector2(
      this.target.position.x - this.position.x,
      this.target.position.y - this.position.y
    ).normalize();
    
    this.velocity = direction.multiply(this.stats.speed);
  }

  private updateShooterAI(distanceToPlayer: number): void {
    if (!this.target) return;
    
    // Keep distance and shoot
    const direction = new Vector2(
      this.target.position.x - this.position.x,
      this.target.position.y - this.position.y
    ).normalize();
    
    if (distanceToPlayer < this.stats.attackRange * 0.7) {
      // Too close, back away
      this.velocity = direction.multiply(-this.stats.speed * 0.5);
    } else if (distanceToPlayer > this.stats.attackRange) {
      // Too far, move closer
      this.velocity = direction.multiply(this.stats.speed * 0.8);
    } else {
      // Good distance, strafe
      const perpendicular = new Vector2(-direction.y, direction.x);
      this.velocity = perpendicular.multiply(this.stats.speed * 0.6);
    }
  }

  private updateCirclerAI(distanceToPlayer: number): void {
    if (!this.target) return;
    
    // Circle around player
    const direction = new Vector2(
      this.target.position.x - this.position.x,
      this.target.position.y - this.position.y
    ).normalize();
    
    const perpendicular = new Vector2(-direction.y, direction.x);
    const circleRadius = 120;
    
    if (distanceToPlayer < circleRadius) {
      // Move away and circle
      this.velocity = direction.multiply(-this.stats.speed * 0.3)
        .add(perpendicular.multiply(this.stats.speed * 0.8));
    } else if (distanceToPlayer > circleRadius * 1.5) {
      // Move closer
      this.velocity = direction.multiply(this.stats.speed * 0.7);
    } else {
      // Circle
      this.velocity = perpendicular.multiply(this.stats.speed);
    }
  }

  private updateBomberAI(distanceToPlayer: number): void {
    if (!this.target) return;
    
    // Rush towards player when close
    if (distanceToPlayer < 100) {
      const direction = new Vector2(
        this.target.position.x - this.position.x,
        this.target.position.y - this.position.y
      ).normalize();
      
      this.velocity = direction.multiply(this.stats.speed * 2);
    } else {
      this.updateChaserAI(distanceToPlayer);
    }
  }

  private updateMovement(deltaTime: number): void {
    this.position = this.position.add(this.velocity.multiply(deltaTime));
    
    // Keep enemy in bounds
    this.position.x = Math.max(0, Math.min(1024, this.position.x));
    this.position.y = Math.max(0, Math.min(768, this.position.y));
  }

  private updateCombat(deltaTime: number, gameObjectManager: any): void {
    if (!this.target || !gameObjectManager) return;
    
    this.attackCooldown -= deltaTime;
    
    const distanceToPlayer = this.position.distance(this.target.position);
    
    if (distanceToPlayer <= this.stats.attackRange && this.attackCooldown <= 0) {
      this.performAttack(gameObjectManager);
      this.attackCooldown = 1 / this.stats.attackRate;
    }
  }

  private performAttack(gameObjectManager: any): void {
    if (!this.target) return;
    
    switch (this.config.bulletPattern) {
      case 'single':
        this.shootSingle(gameObjectManager);
        break;
      case 'spread':
        this.shootSpread(gameObjectManager);
        break;
      case 'circle':
        this.shootCircle(gameObjectManager);
        break;
      case 'spiral':
        this.shootSpiral(gameObjectManager);
        break;
    }
  }

  private shootSingle(gameObjectManager: any): void {
    if (!this.target) return;
    
    const direction = new Vector2(
      this.target.position.x - this.position.x,
      this.target.position.y - this.position.y
    ).normalize();
    
    const bulletConfig = {
      damage: this.stats.damage,
      speed: 200,
      size: 6,
      color: this.config.color,
      lifetime: 4,
      piercing: false,
      homing: false
    };
    
    gameObjectManager.createBullet(
      this.position.x,
      this.position.y,
      direction,
      bulletConfig,
      false
    );
  }

  private shootSpread(gameObjectManager: any): void {
    if (!this.target) return;
    
    const baseDirection = new Vector2(
      this.target.position.x - this.position.x,
      this.target.position.y - this.position.y
    ).normalize();
    
    const spreadAngle = Math.PI / 6; // 30 degrees
    const bulletCount = 3;
    
    for (let i = 0; i < bulletCount; i++) {
      const angle = (i - (bulletCount - 1) / 2) * (spreadAngle / (bulletCount - 1));
      const direction = baseDirection.rotate(angle);
      
      const bulletConfig = {
        damage: this.stats.damage * 0.8,
        speed: 180,
        size: 5,
        color: this.config.color,
        lifetime: 4,
        piercing: false,
        homing: false
      };
      
      gameObjectManager.createBullet(
        this.position.x,
        this.position.y,
        direction,
        bulletConfig,
        false
      );
    }
  }

  private shootCircle(gameObjectManager: any): void {
    const bulletCount = 8;
    
    for (let i = 0; i < bulletCount; i++) {
      const angle = (Math.PI * 2 * i) / bulletCount;
      const direction = Vector2.fromAngle(angle);
      
      const bulletConfig = {
        damage: this.stats.damage * 0.6,
        speed: 150,
        size: 4,
        color: this.config.color,
        lifetime: 5,
        piercing: false,
        homing: false
      };
      
      gameObjectManager.createBullet(
        this.position.x,
        this.position.y,
        direction,
        bulletConfig,
        false
      );
    }
  }

  private shootSpiral(gameObjectManager: any): void {
    const bulletCount = 6;
    this.patternPhase += 0.3;
    
    for (let i = 0; i < bulletCount; i++) {
      const angle = (Math.PI * 2 * i) / bulletCount + this.patternPhase;
      const direction = Vector2.fromAngle(angle);
      
      const bulletConfig = {
        damage: this.stats.damage * 0.7,
        speed: 120,
        size: 5,
        color: this.config.color,
        lifetime: 6,
        piercing: false,
        homing: false
      };
      
      gameObjectManager.createBullet(
        this.position.x,
        this.position.y,
        direction,
        bulletConfig,
        false
      );
    }
  }

  private updateAnimation(deltaTime: number): void {
    this.animationTime += deltaTime;
  }

  private updateTimers(deltaTime: number): void {
    if (this.hitFlashTimer > 0) {
      this.hitFlashTimer -= deltaTime;
    }
  }

  takeDamage(damage: number): boolean {
    if (!this.isAlive) return false;
    
    this.stats.health -= damage;
    this.hitFlashTimer = 0.1; // Flash for 0.1 seconds
    
    if (this.stats.health <= 0) {
      this.stats.health = 0;
      this.isAlive = false;
      return true; // Enemy died
    }
    
    return false;
  }

  render(renderer: CanvasRenderer): void {
    if (!this.isAlive || !this.isActive) return;
    
    // Flash white when hit
    let color = this.config.color;
    if (this.hitFlashTimer > 0) {
      color = '#ffffff';
    }
    
    // Draw enemy
    renderer.drawCircle(
      this.position.x,
      this.position.y,
      this.config.size / 2,
      color
    );
    
    // Draw health bar
    this.renderHealthBar(renderer);
    
    // Draw attack range (debug)
    // renderer.setGlobalAlpha(0.1);
    // renderer.drawCircleOutline(this.position.x, this.position.y, this.stats.attackRange, '#ff0000', 1);
    // renderer.setGlobalAlpha(1);
  }

  private renderHealthBar(renderer: CanvasRenderer): void {
    const barWidth = this.config.size + 5;
    const barHeight = 3;
    const barX = this.position.x - barWidth / 2;
    const barY = this.position.y - this.config.size / 2 - 8;
    
    // Background
    renderer.drawRect(barX, barY, barWidth, barHeight, '#333333');
    
    // Health fill
    const healthPercent = this.stats.health / this.stats.maxHealth;
    renderer.drawRect(barX, barY, barWidth * healthPercent, barHeight, this.config.color);
  }

  // Collision detection
  checkCollision(other: { position: Vector2; size?: number; collisionRadius?: number }): boolean {
    const otherRadius = other.collisionRadius || other.size || 16;
    const distance = this.position.distance(other.position);
    return distance < (this.config.collisionRadius + otherRadius);
  }

  // Getters
  getPosition(): Vector2 { return this.position.copy(); }
  getHealthPercent(): number { return this.stats.health / this.stats.maxHealth; }
  getExperienceValue(): number { return this.stats.experienceValue; }
  isDead(): boolean { return !this.isAlive; }
}
