# 🚀 Deployment Guide - Ultimate Bullet Hell RPG

This guide covers deployment strategies and instructions for different environments.

## 📋 Table of Contents

1. [Overview](#-overview)
2. [Build Process](#-build-process)
3. [Environment Configuration](#-environment-configuration)
4. [Deployment Targets](#-deployment-targets)
5. [CI/CD Pipeline](#-cicd-pipeline)
6. [Monitoring](#-monitoring)
7. [Troubleshooting](#-troubleshooting)

## 🎯 Overview

The Ultimate Bullet Hell RPG is a client-side web application built with:
- **Vite** for building and bundling
- **TypeScript** for type safety
- **Canvas API** for rendering
- **Static assets** (no server required)

### Deployment Requirements
- Static file hosting (CDN, web server, or cloud storage)
- HTTPS support (recommended)
- Modern browser support (ES2020+)
- No server-side processing required

## 🔨 Build Process

### Development Build
```bash
npm run build:dev
```
- Source maps enabled
- Debug information included
- Larger bundle size
- Suitable for testing

### Production Build
```bash
npm run build:prod
```
- Minified and optimized
- Tree-shaking applied
- Source maps disabled
- Console logs removed
- Optimized for performance

### Build Output
```
dist/
├── index.html              # Main game entry point
├── demo.html               # Demo versions
├── dungeon-demo.html
├── complete-demo.html
├── ultimate-demo.html
├── map-editor.html
├── assets/                 # Optimized assets
│   ├── js/                 # JavaScript bundles
│   ├── css/                # Stylesheets
│   └── images/             # Optimized images
└── favicon.ico
```

## ⚙️ Environment Configuration

### Environment Variables
Create `.env` files for different environments:

#### `.env.development`
```env
NODE_ENV=development
VITE_API_URL=http://localhost:8080
VITE_DEBUG_MODE=true
VITE_PERFORMANCE_MONITORING=true
```

#### `.env.production`
```env
NODE_ENV=production
VITE_API_URL=https://api.yourdomain.com
VITE_DEBUG_MODE=false
VITE_PERFORMANCE_MONITORING=false
```

### Build-time Configuration
```typescript
// Access environment variables in code
const config = {
  apiUrl: import.meta.env.VITE_API_URL,
  debugMode: import.meta.env.VITE_DEBUG_MODE === 'true',
  version: import.meta.env.VITE_VERSION || '1.0.0'
};
```

## 🎯 Deployment Targets

### 1. Static Web Hosting

#### Netlify
```bash
# Build and deploy
npm run build:prod

# Deploy to Netlify
npx netlify deploy --prod --dir=dist
```

**netlify.toml**:
```toml
[build]
  publish = "dist"
  command = "npm run build:prod"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[build.environment]
  NODE_VERSION = "18"
```

#### Vercel
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

**vercel.json**:
```json
{
  "buildCommand": "npm run build:prod",
  "outputDirectory": "dist",
  "framework": "vite",
  "rewrites": [
    { "source": "/(.*)", "destination": "/index.html" }
  ]
}
```

#### GitHub Pages
```bash
# Build for GitHub Pages
npm run build:prod

# Deploy using gh-pages
npx gh-pages -d dist
```

**GitHub Actions** (`.github/workflows/deploy.yml`):
```yaml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    - run: npm ci
    - run: npm run build:prod
    - uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./dist
```

### 2. Cloud Storage

#### AWS S3 + CloudFront
```bash
# Build
npm run build:prod

# Upload to S3
aws s3 sync dist/ s3://your-bucket-name --delete

# Invalidate CloudFront cache
aws cloudfront create-invalidation --distribution-id YOUR_DISTRIBUTION_ID --paths "/*"
```

#### Google Cloud Storage
```bash
# Build
npm run build:prod

# Upload to GCS
gsutil -m rsync -r -d dist/ gs://your-bucket-name

# Set public access
gsutil -m acl ch -r -u AllUsers:R gs://your-bucket-name
```

### 3. Traditional Web Server

#### Apache
**.htaccess**:
```apache
RewriteEngine On
RewriteBase /

# Handle client-side routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Set cache headers
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>
```

#### Nginx
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /var/www/game;
    index index.html;

    # Handle client-side routing
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Compression
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}
```

## 🔄 CI/CD Pipeline

### Automated Deployment Workflow

1. **Code Push** → Triggers CI/CD
2. **Quality Checks** → Linting, testing, type checking
3. **Build** → Create optimized production build
4. **Security Scan** → Check for vulnerabilities
5. **Performance Test** → Verify performance metrics
6. **Deploy** → Deploy to target environment
7. **Smoke Test** → Verify deployment success
8. **Notify** → Alert team of deployment status

### Environment Promotion
```
Development → Staging → Production
     ↓           ↓          ↓
   Feature    Integration  Release
   Testing      Testing    Testing
```

### Rollback Strategy
```bash
# Quick rollback using git tags
git tag -a v1.2.3 -m "Release v1.2.3"
git push origin v1.2.3

# Rollback to previous version
git checkout v1.2.2
npm run build:prod
# Deploy previous version
```

## 📊 Monitoring

### Performance Monitoring
- **Lighthouse CI**: Automated performance audits
- **Web Vitals**: Core web vitals tracking
- **Bundle Analysis**: Monitor bundle size
- **Error Tracking**: Client-side error monitoring

### Health Checks
```javascript
// Simple health check endpoint
if (window.location.pathname === '/health') {
  document.body.innerHTML = JSON.stringify({
    status: 'healthy',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
}
```

### Analytics Integration
```typescript
// Example analytics tracking
interface AnalyticsEvent {
  category: string;
  action: string;
  label?: string;
  value?: number;
}

function trackEvent(event: AnalyticsEvent): void {
  // Send to analytics service
  if (typeof gtag !== 'undefined') {
    gtag('event', event.action, {
      event_category: event.category,
      event_label: event.label,
      value: event.value
    });
  }
}
```

## 🔧 Troubleshooting

### Common Deployment Issues

#### Build Failures
```bash
# Clear cache and rebuild
rm -rf node_modules dist
npm install
npm run build:prod
```

#### Asset Loading Issues
- Check base URL configuration in `vite.config.js`
- Verify asset paths are relative
- Ensure CORS headers for cross-origin assets

#### Performance Issues
```bash
# Analyze bundle size
npm run build:prod
npx vite-bundle-analyzer dist/

# Check for large dependencies
npx webpack-bundle-analyzer dist/assets/*.js
```

#### Cache Issues
```bash
# Force cache invalidation
# Add version query parameter to assets
# Update service worker cache
# Clear CDN cache
```

### Deployment Checklist

- [ ] Environment variables configured
- [ ] Build completes successfully
- [ ] All tests pass
- [ ] Security audit clean
- [ ] Performance metrics acceptable
- [ ] Assets load correctly
- [ ] Game functions properly
- [ ] Error tracking configured
- [ ] Analytics working
- [ ] Backup/rollback plan ready

### Emergency Procedures

#### Quick Rollback
1. Identify last known good version
2. Deploy previous build
3. Verify functionality
4. Investigate issue
5. Plan fix and redeploy

#### Performance Issues
1. Check server resources
2. Analyze bundle size
3. Review recent changes
4. Implement quick fixes
5. Monitor metrics

---

**Need help?** Check the [troubleshooting section](./DEVELOPMENT.md#troubleshooting) or create an issue.
