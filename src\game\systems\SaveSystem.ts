/**
 * Game state persistence and save system
 */

import { ProgressionData } from './ProgressionSystem';
import { InventoryData } from './InventorySystem';
import { Vector2 } from '@/utils/math/Vector2';

export interface GameSettings {
  masterVolume: number;
  sfxVolume: number;
  musicVolume: number;
  showFPS: boolean;
  showGrid: boolean;
  autoSave: boolean;
  difficulty: 'easy' | 'normal' | 'hard' | 'nightmare';
  controls: {
    moveUp: string;
    moveDown: string;
    moveLeft: string;
    moveRight: string;
    attack: string;
    autoAttack: string;
    inventory: string;
    skills: string;
    pause: string;
  };
}

export interface GameStats {
  totalPlayTime: number;
  enemiesKilled: number;
  bulletsShot: number;
  damageDealt: number;
  damageTaken: number;
  levelsCompleted: number;
  itemsCollected: number;
  goldEarned: number;
  deathCount: number;
  highestLevel: number;
  bestTime: number;
  achievements: string[];
}

export interface PlayerState {
  position: Vector2;
  health: number;
  mana: number;
  currentWeapon: number;
  isAlive: boolean;
  lastSaveTime: number;
}

export interface GameState {
  version: string;
  saveId: string;
  timestamp: number;
  playerState: PlayerState;
  progression: ProgressionData;
  inventory: InventoryData;
  currentLevel: number;
  gameTime: number;
  stats: GameStats;
  settings: GameSettings;
  permadeath: boolean;
}

export interface SaveSlot {
  id: string;
  name: string;
  timestamp: number;
  level: number;
  playtime: number;
  preview: string; // Base64 encoded screenshot
}

export class SaveSystem {
  private static readonly SAVE_VERSION = '1.0.0';
  private static readonly MAX_SAVE_SLOTS = 5;
  private static readonly AUTOSAVE_INTERVAL = 30000; // 30 seconds
  
  private autoSaveTimer: number = 0;
  private lastAutoSave: number = 0;

  constructor() {
    this.initializeDefaultSettings();
  }

  private initializeDefaultSettings(): void {
    if (!this.getSettings()) {
      const defaultSettings: GameSettings = {
        masterVolume: 0.8,
        sfxVolume: 0.7,
        musicVolume: 0.6,
        showFPS: false,
        showGrid: false,
        autoSave: true,
        difficulty: 'normal',
        controls: {
          moveUp: 'KeyW',
          moveDown: 'KeyS',
          moveLeft: 'KeyA',
          moveRight: 'KeyD',
          attack: 'Mouse0',
          autoAttack: 'Space',
          inventory: 'KeyI',
          skills: 'KeyK',
          pause: 'KeyP'
        }
      };
      this.saveSettings(defaultSettings);
    }
  }

  // Save game state
  saveGame(gameState: Partial<GameState>, slotId: string = 'autosave'): boolean {
    try {
      const completeState: GameState = {
        version: SaveSystem.SAVE_VERSION,
        saveId: slotId,
        timestamp: Date.now(),
        playerState: gameState.playerState || this.createDefaultPlayerState(),
        progression: gameState.progression || this.createDefaultProgression(),
        inventory: gameState.inventory || this.createDefaultInventory(),
        currentLevel: gameState.currentLevel || 1,
        gameTime: gameState.gameTime || 0,
        stats: gameState.stats || this.createDefaultStats(),
        settings: gameState.settings || this.getSettings()!,
        permadeath: gameState.permadeath || false
      };

      const saveData = JSON.stringify(completeState);
      const compressed = this.compressData(saveData);
      
      localStorage.setItem(`bulletrpg_save_${slotId}`, compressed);
      
      // Update save slot metadata
      this.updateSaveSlotMetadata(slotId, completeState);
      
      console.log(`✅ Game saved to slot: ${slotId}`);
      return true;
    } catch (error) {
      console.error('❌ Failed to save game:', error);
      return false;
    }
  }

  // Load game state
  loadGame(slotId: string): GameState | null {
    try {
      const compressed = localStorage.getItem(`bulletrpg_save_${slotId}`);
      if (!compressed) {
        console.warn(`No save found for slot: ${slotId}`);
        return null;
      }

      const saveData = this.decompressData(compressed);
      const gameState: GameState = JSON.parse(saveData);
      
      // Version compatibility check
      if (!this.isCompatibleVersion(gameState.version)) {
        console.warn(`Save version ${gameState.version} is not compatible with current version ${SaveSystem.SAVE_VERSION}`);
        return this.migrateSave(gameState);
      }

      console.log(`✅ Game loaded from slot: ${slotId}`);
      return gameState;
    } catch (error) {
      console.error('❌ Failed to load game:', error);
      return null;
    }
  }

  // Auto-save functionality
  updateAutoSave(deltaTime: number, gameState: Partial<GameState>): void {
    const settings = this.getSettings();
    if (!settings?.autoSave) return;

    this.autoSaveTimer += deltaTime * 1000; // Convert to milliseconds
    
    if (this.autoSaveTimer >= SaveSystem.AUTOSAVE_INTERVAL) {
      this.saveGame(gameState, 'autosave');
      this.autoSaveTimer = 0;
      this.lastAutoSave = Date.now();
    }
  }

  // Save slot management
  getSaveSlots(): SaveSlot[] {
    const slots: SaveSlot[] = [];
    
    for (let i = 0; i < SaveSystem.MAX_SAVE_SLOTS; i++) {
      const slotId = `slot_${i}`;
      const metadata = this.getSaveSlotMetadata(slotId);
      
      if (metadata) {
        slots.push(metadata);
      } else {
        slots.push({
          id: slotId,
          name: `Empty Slot ${i + 1}`,
          timestamp: 0,
          level: 0,
          playtime: 0,
          preview: ''
        });
      }
    }
    
    return slots;
  }

  deleteSave(slotId: string): boolean {
    try {
      localStorage.removeItem(`bulletrpg_save_${slotId}`);
      localStorage.removeItem(`bulletrpg_slot_${slotId}`);
      console.log(`✅ Save deleted: ${slotId}`);
      return true;
    } catch (error) {
      console.error('❌ Failed to delete save:', error);
      return false;
    }
  }

  // Settings management
  saveSettings(settings: GameSettings): boolean {
    try {
      localStorage.setItem('bulletrpg_settings', JSON.stringify(settings));
      return true;
    } catch (error) {
      console.error('❌ Failed to save settings:', error);
      return false;
    }
  }

  getSettings(): GameSettings | null {
    try {
      const settings = localStorage.getItem('bulletrpg_settings');
      return settings ? JSON.parse(settings) : null;
    } catch (error) {
      console.error('❌ Failed to load settings:', error);
      return null;
    }
  }

  // Statistics management
  updateStats(stats: Partial<GameStats>): void {
    const currentStats = this.getStats();
    const updatedStats = { ...currentStats, ...stats };
    
    try {
      localStorage.setItem('bulletrpg_stats', JSON.stringify(updatedStats));
    } catch (error) {
      console.error('❌ Failed to update stats:', error);
    }
  }

  getStats(): GameStats {
    try {
      const stats = localStorage.getItem('bulletrpg_stats');
      return stats ? JSON.parse(stats) : this.createDefaultStats();
    } catch (error) {
      console.error('❌ Failed to load stats:', error);
      return this.createDefaultStats();
    }
  }

  // Utility methods
  private updateSaveSlotMetadata(slotId: string, gameState: GameState): void {
    const metadata: SaveSlot = {
      id: slotId,
      name: `Level ${gameState.progression.level} - ${this.formatPlaytime(gameState.gameTime)}`,
      timestamp: gameState.timestamp,
      level: gameState.progression.level,
      playtime: gameState.gameTime,
      preview: '' // TODO: Generate screenshot
    };
    
    localStorage.setItem(`bulletrpg_slot_${slotId}`, JSON.stringify(metadata));
  }

  private getSaveSlotMetadata(slotId: string): SaveSlot | null {
    try {
      const metadata = localStorage.getItem(`bulletrpg_slot_${slotId}`);
      return metadata ? JSON.parse(metadata) : null;
    } catch (error) {
      return null;
    }
  }

  private compressData(data: string): string {
    // Simple compression using base64 encoding
    // In production, consider using a proper compression library
    return btoa(data);
  }

  private decompressData(compressed: string): string {
    return atob(compressed);
  }

  private isCompatibleVersion(version: string): boolean {
    // Simple version compatibility check
    const [major] = version.split('.');
    const [currentMajor] = SaveSystem.SAVE_VERSION.split('.');
    return major === currentMajor;
  }

  private migrateSave(oldSave: any): GameState | null {
    // Handle save migration for older versions
    console.log('🔄 Migrating save data...');
    
    try {
      // Create new save structure with default values
      const migratedSave: GameState = {
        version: SaveSystem.SAVE_VERSION,
        saveId: oldSave.saveId || 'migrated',
        timestamp: Date.now(),
        playerState: oldSave.playerState || this.createDefaultPlayerState(),
        progression: oldSave.progression || this.createDefaultProgression(),
        inventory: oldSave.inventory || this.createDefaultInventory(),
        currentLevel: oldSave.currentLevel || 1,
        gameTime: oldSave.gameTime || 0,
        stats: oldSave.stats || this.createDefaultStats(),
        settings: oldSave.settings || this.getSettings()!,
        permadeath: oldSave.permadeath || false
      };
      
      console.log('✅ Save migration completed');
      return migratedSave;
    } catch (error) {
      console.error('❌ Save migration failed:', error);
      return null;
    }
  }

  private createDefaultPlayerState(): PlayerState {
    return {
      position: new Vector2(512, 384),
      health: 100,
      mana: 50,
      currentWeapon: 0,
      isAlive: true,
      lastSaveTime: Date.now()
    };
  }

  private createDefaultProgression(): ProgressionData {
    return {
      level: 1,
      experience: 0,
      skillPoints: 0,
      totalSkillPoints: 0,
      stats: {
        health: 100,
        maxHealth: 100,
        mana: 50,
        maxMana: 50,
        damage: 25,
        attackSpeed: 1.0,
        criticalChance: 0.05,
        criticalMultiplier: 2.0,
        defense: 5,
        speed: 300,
        spellPower: 20,
        manaRegeneration: 5,
        luck: 1,
        experienceMultiplier: 1.0,
        goldMultiplier: 1.0
      },
      skills: new Map(),
      unlockedAbilities: new Set(),
      unlockedPassives: new Set()
    };
  }

  private createDefaultInventory(): InventoryData {
    return {
      slots: Array(30).fill(null).map(() => ({ item: null, quantity: 0 })),
      equipment: {
        weapon: null,
        helmet: null,
        chest: null,
        legs: null,
        boots: null,
        gloves: null,
        ring1: null,
        ring2: null,
        amulet: null
      },
      gold: 0,
      maxSlots: 30
    };
  }

  private createDefaultStats(): GameStats {
    return {
      totalPlayTime: 0,
      enemiesKilled: 0,
      bulletsShot: 0,
      damageDealt: 0,
      damageTaken: 0,
      levelsCompleted: 0,
      itemsCollected: 0,
      goldEarned: 0,
      deathCount: 0,
      highestLevel: 1,
      bestTime: 0,
      achievements: []
    };
  }

  private formatPlaytime(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  }

  // Export/Import functionality
  exportSave(slotId: string): string | null {
    const gameState = this.loadGame(slotId);
    if (!gameState) return null;
    
    return btoa(JSON.stringify(gameState));
  }

  importSave(saveData: string, slotId: string): boolean {
    try {
      const gameState: GameState = JSON.parse(atob(saveData));
      return this.saveGame(gameState, slotId);
    } catch (error) {
      console.error('❌ Failed to import save:', error);
      return false;
    }
  }

  // Clear all data
  clearAllData(): boolean {
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith('bulletrpg_'));
      keys.forEach(key => localStorage.removeItem(key));
      console.log('✅ All game data cleared');
      return true;
    } catch (error) {
      console.error('❌ Failed to clear data:', error);
      return false;
    }
  }
}
