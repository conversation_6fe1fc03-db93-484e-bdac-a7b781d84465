/**
 * Type definitions for game entities
 */

import { 
  EntityId, 
  Point2D, 
  EntityStats, 
  EntityTransform, 
  Timestamp,
  Milliseconds,
  RGBAColor,
  Brand
} from './common';

// Entity base types
export interface BaseEntity {
  readonly id: EntityId;
  readonly type: string;
  readonly createdAt: Timestamp;
  transform: EntityTransform;
  active: boolean;
}

// Player types
export type PlayerState = 'idle' | 'moving' | 'attacking' | 'dead' | 'invulnerable';

export interface PlayerConfig {
  readonly stats: EntityStats;
  readonly controls: PlayerControls;
  readonly abilities: PlayerAbilities;
  readonly appearance: PlayerAppearance;
}

export interface PlayerControls {
  readonly moveLeft: string;
  readonly moveRight: string;
  readonly moveUp: string;
  readonly moveDown: string;
  readonly shoot: string;
  readonly special: string;
  readonly interact: string;
  readonly pause: string;
}

export interface PlayerAbilities {
  readonly dash: AbilityConfig;
  readonly shield: AbilityConfig;
  readonly timeSlowdown: AbilityConfig;
  readonly multiShot: AbilityConfig;
}

export interface AbilityConfig {
  readonly cooldown: Milliseconds;
  readonly duration: Milliseconds;
  readonly manaCost: number;
  readonly enabled: boolean;
}

export interface PlayerAppearance {
  readonly color: RGBAColor;
  readonly size: number;
  readonly trailEnabled: boolean;
  readonly glowEnabled: boolean;
}

export interface PlayerEntity extends BaseEntity {
  readonly type: 'player';
  state: PlayerState;
  stats: EntityStats;
  config: PlayerConfig;
  lastDamageTime: Timestamp;
  abilities: Record<string, AbilityState>;
}

export interface AbilityState {
  readonly config: AbilityConfig;
  lastUsed: Timestamp;
  isActive: boolean;
  remainingDuration: Milliseconds;
}

// Enemy types
export type EnemyType = 'basic' | 'shooter' | 'tank' | 'boss' | 'elite';
export type EnemyState = 'idle' | 'patrolling' | 'chasing' | 'attacking' | 'fleeing' | 'dead';
export type AIBehavior = 'aggressive' | 'defensive' | 'patrol' | 'ranged' | 'swarm';

export interface EnemyConfig {
  readonly type: EnemyType;
  readonly stats: EntityStats;
  readonly ai: EnemyAIConfig;
  readonly appearance: EnemyAppearance;
  readonly loot: LootConfig;
}

export interface EnemyAIConfig {
  readonly behavior: AIBehavior;
  readonly detectionRange: number;
  readonly attackRange: number;
  readonly attackCooldown: Milliseconds;
  readonly moveSpeed: number;
  readonly aggroTime: Milliseconds;
  readonly fleeHealthThreshold: number;
}

export interface EnemyAppearance {
  readonly color: RGBAColor;
  readonly size: number;
  readonly shape: 'circle' | 'square' | 'triangle';
  readonly animated: boolean;
}

export interface LootConfig {
  readonly experienceValue: number;
  readonly dropChance: number;
  readonly possibleDrops: LootDrop[];
}

export interface LootDrop {
  readonly itemType: string;
  readonly rarity: ItemRarity;
  readonly chance: number;
}

export interface EnemyEntity extends BaseEntity {
  readonly type: 'enemy';
  readonly config: EnemyConfig;
  state: EnemyState;
  stats: EntityStats;
  target: EntityId | null;
  lastAttackTime: Timestamp;
  aggroStartTime: Timestamp;
  patrolPath: Point2D[];
  currentPatrolIndex: number;
}

// Projectile types
export type ProjectileType = 'bullet' | 'missile' | 'laser' | 'magic' | 'explosive';
export type ProjectileOwner = 'player' | 'enemy' | 'neutral';

export interface ProjectileConfig {
  readonly type: ProjectileType;
  readonly damage: number;
  readonly speed: number;
  readonly range: number;
  readonly piercing: boolean;
  readonly explosive: boolean;
  readonly homing: boolean;
  readonly appearance: ProjectileAppearance;
}

export interface ProjectileAppearance {
  readonly color: RGBAColor;
  readonly size: number;
  readonly trail: boolean;
  readonly glow: boolean;
  readonly sprite?: string;
}

export interface ProjectileEntity extends BaseEntity {
  readonly type: 'projectile';
  readonly config: ProjectileConfig;
  readonly owner: ProjectileOwner;
  readonly ownerId: EntityId;
  velocity: Point2D;
  distanceTraveled: number;
  target: EntityId | null; // For homing projectiles
}

// Item types
export type ItemType = 'weapon' | 'armor' | 'consumable' | 'upgrade' | 'key' | 'currency';
export type ItemRarity = 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';

export interface ItemConfig {
  readonly type: ItemType;
  readonly rarity: ItemRarity;
  readonly name: string;
  readonly description: string;
  readonly stackable: boolean;
  readonly maxStack: number;
  readonly value: number;
  readonly effects: ItemEffect[];
}

export interface ItemEffect {
  readonly type: 'stat' | 'ability' | 'passive' | 'active';
  readonly target: string;
  readonly value: number;
  readonly duration?: Milliseconds;
}

export interface ItemEntity extends BaseEntity {
  readonly type: 'item';
  readonly config: ItemConfig;
  quantity: number;
  expiresAt?: Timestamp;
}

// Particle types
export type ParticleType = 'explosion' | 'trail' | 'spark' | 'smoke' | 'magic' | 'blood';

export interface ParticleConfig {
  readonly type: ParticleType;
  readonly lifetime: Milliseconds;
  readonly startColor: RGBAColor;
  readonly endColor: RGBAColor;
  readonly startSize: number;
  readonly endSize: number;
  readonly gravity: Point2D;
  readonly friction: number;
}

export interface ParticleEntity extends BaseEntity {
  readonly type: 'particle';
  readonly config: ParticleConfig;
  velocity: Point2D;
  age: Milliseconds;
  currentColor: RGBAColor;
  currentSize: number;
}

// Collectible types
export type CollectibleType = 'health' | 'mana' | 'experience' | 'coin' | 'powerup';

export interface CollectibleConfig {
  readonly type: CollectibleType;
  readonly value: number;
  readonly autoCollect: boolean;
  readonly collectRange: number;
  readonly lifetime: Milliseconds;
  readonly appearance: CollectibleAppearance;
}

export interface CollectibleAppearance {
  readonly color: RGBAColor;
  readonly size: number;
  readonly glow: boolean;
  readonly pulse: boolean;
  readonly bobbing: boolean;
}

export interface CollectibleEntity extends BaseEntity {
  readonly type: 'collectible';
  readonly config: CollectibleConfig;
  spawnTime: Timestamp;
  collected: boolean;
}

// Union types for all entities
export type GameEntity = 
  | PlayerEntity 
  | EnemyEntity 
  | ProjectileEntity 
  | ItemEntity 
  | ParticleEntity 
  | CollectibleEntity;

// Entity factory types
export interface EntityFactory<T extends GameEntity> {
  create(config: EntityFactoryConfig<T>): T;
  destroy(entity: T): void;
}

export interface EntityFactoryConfig<T extends GameEntity> {
  readonly position: Point2D;
  readonly rotation?: number;
  readonly scale?: Point2D;
  readonly customConfig?: Partial<T>;
}

// Entity manager types
export interface EntityManager {
  readonly entities: ReadonlyMap<EntityId, GameEntity>;
  add<T extends GameEntity>(entity: T): void;
  remove(id: EntityId): boolean;
  get<T extends GameEntity>(id: EntityId): T | undefined;
  getByType<T extends GameEntity>(type: string): T[];
  update(deltaTime: Milliseconds): void;
  clear(): void;
}

// Entity query types
export interface EntityQuery {
  readonly type?: string;
  readonly position?: Point2D;
  readonly radius?: number;
  readonly predicate?: (entity: GameEntity) => boolean;
}

export interface EntityQueryResult {
  readonly entities: readonly GameEntity[];
  readonly count: number;
}

// Type guards for entities
export function isPlayerEntity(entity: GameEntity): entity is PlayerEntity {
  return entity.type === 'player';
}

export function isEnemyEntity(entity: GameEntity): entity is EnemyEntity {
  return entity.type === 'enemy';
}

export function isProjectileEntity(entity: GameEntity): entity is ProjectileEntity {
  return entity.type === 'projectile';
}

export function isItemEntity(entity: GameEntity): entity is ItemEntity {
  return entity.type === 'item';
}

export function isParticleEntity(entity: GameEntity): entity is ParticleEntity {
  return entity.type === 'particle';
}

export function isCollectibleEntity(entity: GameEntity): entity is CollectibleEntity {
  return entity.type === 'collectible';
}

// Entity creation helpers
export function createEntityId(): EntityId {
  return `entity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}` as EntityId;
}

export function createBaseEntity(type: string, position: Point2D): BaseEntity {
  return {
    id: createEntityId(),
    type,
    createdAt: Date.now() as Timestamp,
    transform: {
      position,
      rotation: 0,
      scale: { x: 1, y: 1 }
    },
    active: true
  };
}
