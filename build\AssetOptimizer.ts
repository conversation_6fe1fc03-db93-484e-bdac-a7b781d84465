/**
 * Asset optimization and build system for production deployment
 */

import * as fs from 'fs';
import * as path from 'path';

export interface AssetConfig {
  inputDir: string;
  outputDir: string;
  minifyJS: boolean;
  minifyCSS: boolean;
  optimizeImages: boolean;
  generateSourceMaps: boolean;
  bundleModules: boolean;
  compressionLevel: number;
}

export interface OptimizationResult {
  originalSize: number;
  optimizedSize: number;
  compressionRatio: number;
  files: string[];
  errors: string[];
}

export class AssetOptimizer {
  private config: AssetConfig;
  private results: Map<string, OptimizationResult> = new Map();

  constructor(config: Partial<AssetConfig> = {}) {
    this.config = {
      inputDir: './src',
      outputDir: './dist',
      minifyJS: true,
      minifyCSS: true,
      optimizeImages: true,
      generateSourceMaps: false,
      bundleModules: true,
      compressionLevel: 9,
      ...config
    };
  }

  async optimize(): Promise<void> {
    console.log('🔧 Starting asset optimization...');
    
    try {
      // Create output directory
      await this.ensureDirectory(this.config.outputDir);
      
      // Process different asset types
      await this.processHTMLFiles();
      await this.processTypeScriptFiles();
      await this.processCSSFiles();
      await this.processImageAssets();
      await this.generateManifest();
      
      // Generate optimization report
      this.generateReport();
      
      console.log('✅ Asset optimization completed successfully');
      
    } catch (error) {
      console.error('❌ Asset optimization failed:', error);
      throw error;
    }
  }

  private async processHTMLFiles(): Promise<void> {
    console.log('📄 Processing HTML files...');
    
    const htmlFiles = await this.findFiles('.', '.html');
    
    for (const file of htmlFiles) {
      try {
        let content = await this.readFile(file);
        
        // Minify HTML
        content = this.minifyHTML(content);
        
        // Inline critical CSS
        content = await this.inlineCriticalCSS(content);
        
        // Optimize script loading
        content = this.optimizeScriptLoading(content);
        
        // Update module paths for production
        content = this.updateModulePaths(content);
        
        const outputPath = path.join(this.config.outputDir, file);
        await this.writeFile(outputPath, content);
        
        this.recordOptimization('html', file, content.length);
        
      } catch (error) {
        console.error(`Failed to process HTML file ${file}:`, error);
      }
    }
  }

  private async processTypeScriptFiles(): Promise<void> {
    console.log('📜 Processing TypeScript files...');
    
    if (this.config.bundleModules) {
      await this.bundleModules();
    } else {
      await this.compileIndividualFiles();
    }
  }

  private async bundleModules(): Promise<void> {
    // Create bundled versions of the main modules
    const entryPoints = [
      'src/game/UltimateGame.ts',
      'src/tools/MapEditor.ts',
      'src/engine/core/Game.ts'
    ];
    
    for (const entryPoint of entryPoints) {
      if (await this.fileExists(entryPoint)) {
        const bundle = await this.createBundle(entryPoint);
        const outputName = path.basename(entryPoint, '.ts') + '.bundle.js';
        const outputPath = path.join(this.config.outputDir, outputName);
        
        await this.writeFile(outputPath, bundle);
        this.recordOptimization('bundle', outputName, bundle.length);
      }
    }
  }

  private async createBundle(entryPoint: string): Promise<string> {
    // Simplified bundling - in production, use tools like Rollup or Webpack
    const dependencies = await this.resolveDependencies(entryPoint);
    let bundle = '';
    
    // Add all dependencies in correct order
    for (const dep of dependencies) {
      const content = await this.readFile(dep);
      const compiled = this.compileTypeScript(content);
      bundle += this.wrapModule(dep, compiled) + '\n';
    }
    
    // Minify if enabled
    if (this.config.minifyJS) {
      bundle = this.minifyJavaScript(bundle);
    }
    
    return bundle;
  }

  private async resolveDependencies(entryPoint: string): Promise<string[]> {
    const dependencies: string[] = [];
    const visited = new Set<string>();
    
    const resolve = async (file: string) => {
      if (visited.has(file)) return;
      visited.add(file);
      
      const content = await this.readFile(file);
      const imports = this.extractImports(content);
      
      for (const importPath of imports) {
        const resolvedPath = this.resolveImportPath(file, importPath);
        if (resolvedPath && await this.fileExists(resolvedPath)) {
          await resolve(resolvedPath);
        }
      }
      
      dependencies.push(file);
    };
    
    await resolve(entryPoint);
    return dependencies;
  }

  private extractImports(content: string): string[] {
    const importRegex = /import\s+.*?\s+from\s+['"`]([^'"`]+)['"`]/g;
    const imports: string[] = [];
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      imports.push(match[1]);
    }
    
    return imports;
  }

  private resolveImportPath(currentFile: string, importPath: string): string | null {
    if (importPath.startsWith('@/')) {
      // Handle path aliases
      return importPath.replace('@/', 'src/') + '.ts';
    } else if (importPath.startsWith('./') || importPath.startsWith('../')) {
      // Handle relative imports
      const currentDir = path.dirname(currentFile);
      return path.resolve(currentDir, importPath + '.ts');
    }
    
    return null;
  }

  private compileTypeScript(content: string): string {
    // Simplified TypeScript compilation
    // Remove type annotations and interfaces
    let compiled = content
      .replace(/:\s*[A-Za-z_][A-Za-z0-9_<>[\]|&\s]*(?=[,;=\)\{\}])/g, '')
      .replace(/interface\s+[^{]+\{[^}]*\}/g, '')
      .replace(/export\s+interface\s+[^{]+\{[^}]*\}/g, '')
      .replace(/import\s+type\s+[^;]+;/g, '')
      .replace(/as\s+[A-Za-z_][A-Za-z0-9_<>[\]|&\s]*/g, '');
    
    return compiled;
  }

  private wrapModule(filePath: string, content: string): string {
    const moduleName = filePath.replace(/[^a-zA-Z0-9]/g, '_');
    return `
// Module: ${filePath}
(function(exports) {
${content}
})(window.modules = window.modules || {});
`;
  }

  private async processCSSFiles(): Promise<void> {
    console.log('🎨 Processing CSS files...');
    
    // Extract and optimize inline CSS from HTML files
    const htmlFiles = await this.findFiles('.', '.html');
    let combinedCSS = '';
    
    for (const file of htmlFiles) {
      const content = await this.readFile(file);
      const css = this.extractInlineCSS(content);
      combinedCSS += css + '\n';
    }
    
    if (combinedCSS.trim()) {
      // Optimize CSS
      const optimizedCSS = this.optimizeCSS(combinedCSS);
      
      // Write combined CSS file
      const outputPath = path.join(this.config.outputDir, 'styles.min.css');
      await this.writeFile(outputPath, optimizedCSS);
      
      this.recordOptimization('css', 'styles.min.css', optimizedCSS.length);
    }
  }

  private extractInlineCSS(htmlContent: string): string {
    const styleRegex = /<style[^>]*>([\s\S]*?)<\/style>/gi;
    let css = '';
    let match;
    
    while ((match = styleRegex.exec(htmlContent)) !== null) {
      css += match[1] + '\n';
    }
    
    return css;
  }

  private optimizeCSS(css: string): string {
    if (!this.config.minifyCSS) return css;
    
    return css
      // Remove comments
      .replace(/\/\*[\s\S]*?\*\//g, '')
      // Remove unnecessary whitespace
      .replace(/\s+/g, ' ')
      // Remove whitespace around certain characters
      .replace(/\s*([{}:;,>+~])\s*/g, '$1')
      // Remove trailing semicolons
      .replace(/;}/g, '}')
      // Remove empty rules
      .replace(/[^{}]+\{\s*\}/g, '')
      .trim();
  }

  private async processImageAssets(): Promise<void> {
    console.log('🖼️ Processing image assets...');
    
    if (!this.config.optimizeImages) return;
    
    // For this implementation, we'll focus on base64 encoding small images
    // In production, use tools like imagemin for proper optimization
    
    const imageFiles = await this.findFiles('.', '.png', '.jpg', '.jpeg', '.gif', '.svg');
    
    for (const file of imageFiles) {
      try {
        const stats = await this.getFileStats(file);
        
        // Convert small images to base64 data URLs
        if (stats.size < 10240) { // 10KB threshold
          const base64 = await this.convertToBase64(file);
          const outputPath = path.join(this.config.outputDir, file + '.base64');
          await this.writeFile(outputPath, base64);
          
          this.recordOptimization('image', file, base64.length);
        } else {
          // Copy larger images as-is
          await this.copyFile(file, path.join(this.config.outputDir, file));
        }
        
      } catch (error) {
        console.error(`Failed to process image ${file}:`, error);
      }
    }
  }

  private async convertToBase64(filePath: string): Promise<string> {
    const content = await this.readFileBuffer(filePath);
    const ext = path.extname(filePath).toLowerCase();
    
    let mimeType = 'application/octet-stream';
    switch (ext) {
      case '.png': mimeType = 'image/png'; break;
      case '.jpg':
      case '.jpeg': mimeType = 'image/jpeg'; break;
      case '.gif': mimeType = 'image/gif'; break;
      case '.svg': mimeType = 'image/svg+xml'; break;
    }
    
    return `data:${mimeType};base64,${content.toString('base64')}`;
  }

  private async generateManifest(): Promise<void> {
    console.log('📋 Generating asset manifest...');
    
    const manifest = {
      version: Date.now().toString(),
      timestamp: new Date().toISOString(),
      files: {},
      optimization: {}
    };
    
    // Add file information
    const files = await this.findFiles(this.config.outputDir, '.*');
    for (const file of files) {
      const stats = await this.getFileStats(file);
      const relativePath = path.relative(this.config.outputDir, file);
      
      manifest.files[relativePath] = {
        size: stats.size,
        modified: stats.mtime.toISOString()
      };
    }
    
    // Add optimization results
    for (const [type, result] of this.results) {
      manifest.optimization[type] = {
        originalSize: result.originalSize,
        optimizedSize: result.optimizedSize,
        compressionRatio: result.compressionRatio,
        fileCount: result.files.length
      };
    }
    
    const manifestPath = path.join(this.config.outputDir, 'manifest.json');
    await this.writeFile(manifestPath, JSON.stringify(manifest, null, 2));
  }

  private generateReport(): void {
    console.log('\n📊 Optimization Report:');
    console.log('========================');
    
    let totalOriginal = 0;
    let totalOptimized = 0;
    
    for (const [type, result] of this.results) {
      totalOriginal += result.originalSize;
      totalOptimized += result.optimizedSize;
      
      console.log(`${type.toUpperCase()}:`);
      console.log(`  Files: ${result.files.length}`);
      console.log(`  Original: ${this.formatBytes(result.originalSize)}`);
      console.log(`  Optimized: ${this.formatBytes(result.optimizedSize)}`);
      console.log(`  Savings: ${result.compressionRatio.toFixed(1)}%`);
      console.log('');
    }
    
    const totalSavings = ((totalOriginal - totalOptimized) / totalOriginal) * 100;
    console.log(`TOTAL SAVINGS: ${this.formatBytes(totalOriginal - totalOptimized)} (${totalSavings.toFixed(1)}%)`);
  }

  // Utility methods
  private minifyHTML(html: string): string {
    return html
      .replace(/<!--[\s\S]*?-->/g, '')
      .replace(/\s+/g, ' ')
      .replace(/>\s+</g, '><')
      .trim();
  }

  private async inlineCriticalCSS(html: string): Promise<string> {
    // Extract critical CSS and inline it
    // This is a simplified version - production tools analyze actual usage
    return html;
  }

  private optimizeScriptLoading(html: string): string {
    // Add async/defer attributes to script tags
    return html.replace(/<script\s+src=/g, '<script async src=');
  }

  private updateModulePaths(html: string): string {
    // Update module import paths for production
    return html.replace(/from\s+['"`]\.\/src\//g, "from './");
  }

  private minifyJavaScript(js: string): string {
    if (!this.config.minifyJS) return js;
    
    // Simplified minification
    return js
      .replace(/\/\*[\s\S]*?\*\//g, '')
      .replace(/\/\/.*$/gm, '')
      .replace(/\s+/g, ' ')
      .replace(/\s*([{}:;,=()[\]])\s*/g, '$1')
      .trim();
  }

  private recordOptimization(type: string, file: string, optimizedSize: number): void {
    if (!this.results.has(type)) {
      this.results.set(type, {
        originalSize: 0,
        optimizedSize: 0,
        compressionRatio: 0,
        files: [],
        errors: []
      });
    }
    
    const result = this.results.get(type)!;
    result.files.push(file);
    result.optimizedSize += optimizedSize;
    
    // Calculate compression ratio
    if (result.originalSize > 0) {
      result.compressionRatio = ((result.originalSize - result.optimizedSize) / result.originalSize) * 100;
    }
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // File system operations (simplified for browser environment)
  private async ensureDirectory(dir: string): Promise<void> {
    // In Node.js environment, this would create directories
    console.log(`Ensuring directory: ${dir}`);
  }

  private async findFiles(dir: string, ...extensions: string[]): Promise<string[]> {
    // In browser environment, return predefined file list
    const files = [
      'demo.html',
      'dungeon-demo.html',
      'complete-demo.html',
      'ultimate-demo.html',
      'map-editor.html'
    ];
    
    return files.filter(file => 
      extensions.some(ext => file.endsWith(ext) || ext === '.*')
    );
  }

  private async readFile(filePath: string): Promise<string> {
    // In browser environment, use fetch
    try {
      const response = await fetch(filePath);
      return await response.text();
    } catch {
      return '';
    }
  }

  private async readFileBuffer(filePath: string): Promise<Buffer> {
    // Simplified for browser environment
    return Buffer.from('');
  }

  private async writeFile(filePath: string, content: string): Promise<void> {
    console.log(`Writing file: ${filePath} (${content.length} bytes)`);
  }

  private async copyFile(src: string, dest: string): Promise<void> {
    console.log(`Copying file: ${src} -> ${dest}`);
  }

  private async fileExists(filePath: string): Promise<boolean> {
    try {
      const response = await fetch(filePath, { method: 'HEAD' });
      return response.ok;
    } catch {
      return false;
    }
  }

  private async getFileStats(filePath: string): Promise<{ size: number; mtime: Date }> {
    return {
      size: 1024, // Placeholder
      mtime: new Date()
    };
  }

  private async compileIndividualFiles(): Promise<void> {
    // Compile TypeScript files individually
    console.log('Compiling individual TypeScript files...');
  }
}
