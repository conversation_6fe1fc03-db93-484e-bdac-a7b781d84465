/**
 * Time management system for consistent timing across the game
 */

export class Time {
  private static instance: Time | null = null;
  
  private _deltaTime: number = 0;
  private _time: number = 0;
  private _timeScale: number = 1;
  private _frameCount: number = 0;
  private _startTime: number = 0;
  
  constructor() {
    this._startTime = performance.now();
  }

  static getInstance(): Time {
    if (!Time.instance) {
      Time.instance = new Time();
    }
    return Time.instance;
  }

  update(frameTime: number): void {
    this._deltaTime = (frameTime / 1000) * this._timeScale;
    this._time += this._deltaTime;
    this._frameCount++;
  }

  // Getters
  get deltaTime(): number { return this._deltaTime; }
  get time(): number { return this._time; }
  get timeScale(): number { return this._timeScale; }
  get frameCount(): number { return this._frameCount; }
  get realTime(): number { return (performance.now() - this._startTime) / 1000; }

  // Setters
  set timeScale(value: number) {
    this._timeScale = Math.max(0, value);
  }

  // Utility methods
  reset(): void {
    this._time = 0;
    this._frameCount = 0;
    this._startTime = performance.now();
  }

  pause(): void {
    this._timeScale = 0;
  }

  resume(): void {
    this._timeScale = 1;
  }

  slowMotion(factor: number = 0.5): void {
    this._timeScale = factor;
  }
}
