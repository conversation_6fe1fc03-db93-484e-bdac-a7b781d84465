<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Map Editor - Bullet Hell RPG</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
            color: white;
        }

        #editorContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        #toolbar {
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
            flex-shrink: 0;
        }

        .toolbar-group {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 0 15px;
            border-right: 1px solid rgba(255, 255, 255, 0.2);
        }

        .toolbar-group:last-child {
            border-right: none;
        }

        .toolbar-button {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }

        .toolbar-button:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .toolbar-button.active {
            background: rgba(0, 150, 255, 0.6);
            border-color: #0096ff;
        }

        .toolbar-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 6px 8px;
            border-radius: 4px;
            font-size: 12px;
            width: 120px;
        }

        .toolbar-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        #editorCanvas {
            flex: 1;
            background: #000;
            cursor: crosshair;
        }

        #sidePanel {
            position: absolute;
            top: 60px;
            right: 10px;
            width: 250px;
            background: rgba(0, 0, 0, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 15px;
            max-height: calc(100vh - 80px);
            overflow-y: auto;
        }

        .panel-section {
            margin-bottom: 20px;
        }

        .panel-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #00aaff;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding-bottom: 5px;
        }

        .tool-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        .tool-item {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 12px;
        }

        .tool-item:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .tool-item.selected {
            background: rgba(0, 150, 255, 0.4);
            border-color: #0096ff;
        }

        .tool-icon {
            font-size: 20px;
            margin-bottom: 5px;
            display: block;
        }

        .tool-name {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.8);
        }

        .tool-key {
            font-size: 9px;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 2px;
        }

        .property-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .property-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 4px 6px;
            border-radius: 3px;
            font-size: 11px;
            width: 80px;
        }

        .help-text {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.6);
            line-height: 1.4;
            margin-top: 10px;
        }

        .status-bar {
            background: rgba(0, 0, 0, 0.9);
            padding: 8px 15px;
            font-size: 11px;
            color: rgba(255, 255, 255, 0.8);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-left {
            display: flex;
            gap: 20px;
        }

        .status-right {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .status-indicator {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
        }

        .status-saved {
            background: rgba(0, 255, 0, 0.2);
            color: #00ff00;
        }

        .status-unsaved {
            background: rgba(255, 165, 0, 0.2);
            color: #ffa500;
        }

        .status-playing {
            background: rgba(0, 255, 0, 0.3);
            color: #00ff00;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: #1a1a2e;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 20px;
            max-width: 500px;
            width: 90%;
        }

        .modal-title {
            font-size: 18px;
            margin-bottom: 15px;
            color: #00aaff;
        }

        .modal-buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }

        .modal-button {
            background: rgba(0, 150, 255, 0.6);
            border: 1px solid #0096ff;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .modal-button:hover {
            background: rgba(0, 150, 255, 0.8);
        }

        .modal-button.secondary {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .modal-button.secondary:hover {
            background: rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div id="editorContainer">
        <!-- Toolbar -->
        <div id="toolbar">
            <div class="toolbar-group">
                <span class="toolbar-label">File:</span>
                <button class="toolbar-button" id="newBtn">New (N)</button>
                <button class="toolbar-button" id="saveBtn">Save (S)</button>
                <button class="toolbar-button" id="loadBtn">Load (L)</button>
                <button class="toolbar-button" id="exportBtn">Export</button>
            </div>
            
            <div class="toolbar-group">
                <span class="toolbar-label">Edit:</span>
                <button class="toolbar-button" id="undoBtn">Undo (Z)</button>
                <button class="toolbar-button" id="redoBtn">Redo (Y)</button>
            </div>
            
            <div class="toolbar-group">
                <span class="toolbar-label">View:</span>
                <button class="toolbar-button" id="gridBtn">Grid (G)</button>
                <button class="toolbar-button" id="zoomInBtn">Zoom In</button>
                <button class="toolbar-button" id="zoomOutBtn">Zoom Out</button>
                <button class="toolbar-button" id="resetViewBtn">Reset View</button>
            </div>
            
            <div class="toolbar-group">
                <span class="toolbar-label">Map Name:</span>
                <input type="text" class="toolbar-input" id="mapNameInput" value="Untitled Map">
            </div>
            
            <div class="toolbar-group">
                <button class="toolbar-button" id="playBtn">Play Mode (Space)</button>
                <button class="toolbar-button" id="testBtn">Test Map</button>
            </div>
        </div>

        <!-- Main Editor Area -->
        <canvas id="editorCanvas" width="1024" height="768"></canvas>

        <!-- Side Panel -->
        <div id="sidePanel">
            <div class="panel-section">
                <div class="panel-title">🛠️ Tools</div>
                <div class="tool-grid" id="toolGrid">
                    <!-- Tools will be populated by JavaScript -->
                </div>
            </div>
            
            <div class="panel-section">
                <div class="panel-title">📏 Map Properties</div>
                <div class="property-row">
                    <span>Width:</span>
                    <input type="number" class="property-input" id="mapWidth" value="50" min="10" max="100">
                </div>
                <div class="property-row">
                    <span>Height:</span>
                    <input type="number" class="property-input" id="mapHeight" value="50" min="10" max="100">
                </div>
                <div class="property-row">
                    <span>Difficulty:</span>
                    <input type="number" class="property-input" id="mapDifficulty" value="1" min="1" max="10">
                </div>
            </div>
            
            <div class="panel-section">
                <div class="panel-title">ℹ️ Help</div>
                <div class="help-text">
                    <strong>Controls:</strong><br>
                    • Click & drag to paint tiles<br>
                    • Arrow keys to move camera<br>
                    • Mouse wheel to zoom<br>
                    • Number keys 1-8 to select tools<br>
                    • Space to toggle play mode<br><br>
                    
                    <strong>Tips:</strong><br>
                    • Only one player spawn allowed<br>
                    • Walls on map borders recommended<br>
                    • Test your map before saving<br>
                    • Use doors to connect areas
                </div>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-left">
                <span id="mapInfo">Map: Untitled (50x50)</span>
                <span id="toolInfo">Tool: Wall</span>
                <span id="cameraInfo">Camera: (0, 0)</span>
                <span id="zoomInfo">Zoom: 100%</span>
            </div>
            <div class="status-right">
                <span id="saveStatus" class="status-indicator status-saved">Saved</span>
                <span id="playStatus" class="status-indicator" style="display: none;">Play Mode</span>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <div id="loadModal" class="modal">
        <div class="modal-content">
            <div class="modal-title">Load Map</div>
            <div id="mapList">
                <!-- Map list will be populated -->
            </div>
            <div class="modal-buttons">
                <button class="modal-button secondary" onclick="closeModal('loadModal')">Cancel</button>
            </div>
        </div>
    </div>

    <div id="exportModal" class="modal">
        <div class="modal-content">
            <div class="modal-title">Export Map</div>
            <p>Choose export format:</p>
            <div class="modal-buttons">
                <button class="modal-button" onclick="exportMap('json')">JSON File</button>
                <button class="modal-button" onclick="exportMap('game')">Game Format</button>
                <button class="modal-button secondary" onclick="closeModal('exportModal')">Cancel</button>
            </div>
        </div>
    </div>

    <script type="module">
        import { MapEditor } from './src/tools/MapEditor.js';

        class MapEditorApp {
            constructor() {
                this.canvas = document.getElementById('editorCanvas');
                this.editor = new MapEditor(this.canvas);
                this.isRunning = false;
                
                this.setupUI();
                this.setupEventListeners();
                this.resizeCanvas();
                this.start();
            }

            setupUI() {
                // Populate tool grid
                const toolGrid = document.getElementById('toolGrid');
                const tools = [
                    { id: 'wall', name: 'Wall', icon: '🧱', key: '1' },
                    { id: 'floor', name: 'Floor', icon: '⬜', key: '2' },
                    { id: 'door', name: 'Door', icon: '🚪', key: '3' },
                    { id: 'spawn', name: 'Spawn', icon: '🟢', key: '4' },
                    { id: 'enemy', name: 'Enemy', icon: '🔴', key: '5' },
                    { id: 'treasure', name: 'Treasure', icon: '💎', key: '6' },
                    { id: 'boss', name: 'Boss', icon: '👹', key: '7' },
                    { id: 'eraser', name: 'Eraser', icon: '🧽', key: '8' }
                ];

                tools.forEach((tool, index) => {
                    const toolElement = document.createElement('div');
                    toolElement.className = 'tool-item';
                    if (index === 0) toolElement.classList.add('selected');
                    toolElement.innerHTML = `
                        <span class="tool-icon">${tool.icon}</span>
                        <div class="tool-name">${tool.name}</div>
                        <div class="tool-key">${tool.key}</div>
                    `;
                    toolElement.addEventListener('click', () => this.selectTool(index));
                    toolGrid.appendChild(toolElement);
                });
            }

            setupEventListeners() {
                // Toolbar buttons
                document.getElementById('newBtn').addEventListener('click', () => this.editor.createNewMap());
                document.getElementById('saveBtn').addEventListener('click', () => this.editor.saveMap());
                document.getElementById('loadBtn').addEventListener('click', () => this.showLoadModal());
                document.getElementById('exportBtn').addEventListener('click', () => this.showExportModal());
                document.getElementById('undoBtn').addEventListener('click', () => this.editor.undo());
                document.getElementById('redoBtn').addEventListener('click', () => this.editor.redo());
                document.getElementById('gridBtn').addEventListener('click', () => this.editor.toggleGrid());
                document.getElementById('playBtn').addEventListener('click', () => this.editor.togglePlayMode());

                // Window resize
                window.addEventListener('resize', () => this.resizeCanvas());

                // Map name input
                document.getElementById('mapNameInput').addEventListener('input', (e) => {
                    const map = this.editor.getCurrentMap();
                    map.name = e.target.value;
                });
            }

            selectTool(index) {
                // Update UI
                document.querySelectorAll('.tool-item').forEach((item, i) => {
                    item.classList.toggle('selected', i === index);
                });

                // Update editor (this would need to be exposed by MapEditor)
                // this.editor.selectTool(index);
            }

            showLoadModal() {
                document.getElementById('loadModal').style.display = 'flex';
            }

            showExportModal() {
                document.getElementById('exportModal').style.display = 'flex';
            }

            resizeCanvas() {
                const toolbar = document.getElementById('toolbar');
                const statusBar = document.querySelector('.status-bar');
                const availableHeight = window.innerHeight - toolbar.offsetHeight - statusBar.offsetHeight;
                
                this.canvas.width = window.innerWidth;
                this.canvas.height = availableHeight;
                this.canvas.style.width = window.innerWidth + 'px';
                this.canvas.style.height = availableHeight + 'px';
            }

            start() {
                this.isRunning = true;
                this.gameLoop();
            }

            gameLoop = () => {
                if (!this.isRunning) return;

                // Update editor
                this.editor.update(1/60);

                // Render
                this.editor.render();

                // Update UI
                this.updateStatusBar();

                requestAnimationFrame(this.gameLoop);
            };

            updateStatusBar() {
                const map = this.editor.getCurrentMap();
                document.getElementById('mapInfo').textContent = `Map: ${map.name} (${map.width}x${map.height})`;
                // Additional status updates would go here
            }

            stop() {
                this.isRunning = false;
            }
        }

        // Global functions for modals
        window.closeModal = function(modalId) {
            document.getElementById(modalId).style.display = 'none';
        };

        window.exportMap = function(format) {
            // Export functionality would be implemented here
            console.log(`Exporting in ${format} format`);
            closeModal('exportModal');
        };

        // Start the application
        window.addEventListener('load', () => {
            new MapEditorApp();
        });
    </script>
</body>
</html>
