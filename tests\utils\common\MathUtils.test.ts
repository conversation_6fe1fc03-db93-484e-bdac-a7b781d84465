import { describe, it, expect } from 'vitest';
import { MathUtils } from '@/utils/common/MathUtils';

describe('MathUtils', () => {
  describe('clamp', () => {
    it('should clamp value between min and max', () => {
      expect(MathUtils.clamp(5, 0, 10)).toBe(5);
      expect(MathUtils.clamp(-5, 0, 10)).toBe(0);
      expect(MathUtils.clamp(15, 0, 10)).toBe(10);
    });

    it('should handle edge cases', () => {
      expect(MathUtils.clamp(0, 0, 0)).toBe(0);
      expect(MathUtils.clamp(5, 5, 5)).toBe(5);
    });
  });

  describe('lerp', () => {
    it('should interpolate between two values', () => {
      expect(MathUtils.lerp(0, 10, 0.5)).toBe(5);
      expect(MathUtils.lerp(0, 10, 0)).toBe(0);
      expect(MathUtils.lerp(0, 10, 1)).toBe(10);
    });

    it('should clamp t value', () => {
      expect(MathUtils.lerp(0, 10, -0.5)).toBe(0);
      expect(MathUtils.lerp(0, 10, 1.5)).toBe(10);
    });
  });

  describe('smoothStep', () => {
    it('should provide smooth interpolation', () => {
      expect(MathUtils.smoothStep(0, 10, 0)).toBe(0);
      expect(MathUtils.smoothStep(0, 10, 1)).toBe(10);
      expect(MathUtils.smoothStep(0, 10, 0.5)).toBe(5);
    });
  });

  describe('map', () => {
    it('should map value from one range to another', () => {
      expect(MathUtils.map(5, 0, 10, 0, 100)).toBe(50);
      expect(MathUtils.map(0, 0, 10, 0, 100)).toBe(0);
      expect(MathUtils.map(10, 0, 10, 0, 100)).toBe(100);
    });
  });

  describe('approximately', () => {
    it('should check if numbers are approximately equal', () => {
      expect(MathUtils.approximately(1.0, 1.0000001)).toBe(true);
      expect(MathUtils.approximately(1.0, 1.1)).toBe(false);
      expect(MathUtils.approximately(1.0, 1.01, 0.1)).toBe(true);
    });
  });

  describe('distance', () => {
    it('should calculate distance between two points', () => {
      expect(MathUtils.distance(0, 0, 3, 4)).toBe(5);
      expect(MathUtils.distance(0, 0, 0, 0)).toBe(0);
      expect(MathUtils.distance(-1, -1, 1, 1)).toBeCloseTo(2.828, 2);
    });
  });

  describe('distanceSquared', () => {
    it('should calculate squared distance', () => {
      expect(MathUtils.distanceSquared(0, 0, 3, 4)).toBe(25);
      expect(MathUtils.distanceSquared(0, 0, 0, 0)).toBe(0);
    });
  });

  describe('normalizeAngle', () => {
    it('should normalize angles to 0-2π range', () => {
      expect(MathUtils.normalizeAngle(0)).toBe(0);
      expect(MathUtils.normalizeAngle(Math.PI)).toBe(Math.PI);
      expect(MathUtils.normalizeAngle(3 * Math.PI)).toBeCloseTo(Math.PI, 10);
      expect(MathUtils.normalizeAngle(-Math.PI)).toBeCloseTo(Math.PI, 10);
    });
  });

  describe('angleDifference', () => {
    it('should calculate shortest angular distance', () => {
      expect(MathUtils.angleDifference(0, Math.PI)).toBe(Math.PI);
      expect(MathUtils.angleDifference(0, 1.5 * Math.PI)).toBeCloseTo(-0.5 * Math.PI, 10);
    });
  });

  describe('toRadians', () => {
    it('should convert degrees to radians', () => {
      expect(MathUtils.toRadians(180)).toBeCloseTo(Math.PI, 10);
      expect(MathUtils.toRadians(90)).toBeCloseTo(Math.PI / 2, 10);
      expect(MathUtils.toRadians(0)).toBe(0);
    });
  });

  describe('toDegrees', () => {
    it('should convert radians to degrees', () => {
      expect(MathUtils.toDegrees(Math.PI)).toBeCloseTo(180, 10);
      expect(MathUtils.toDegrees(Math.PI / 2)).toBeCloseTo(90, 10);
      expect(MathUtils.toDegrees(0)).toBe(0);
    });
  });

  describe('random', () => {
    it('should generate random numbers in range', () => {
      for (let i = 0; i < 100; i++) {
        const value = MathUtils.random(5, 10);
        expect(value).toBeGreaterThanOrEqual(5);
        expect(value).toBeLessThan(10);
      }
    });

    it('should use default range 0-1', () => {
      for (let i = 0; i < 100; i++) {
        const value = MathUtils.random();
        expect(value).toBeGreaterThanOrEqual(0);
        expect(value).toBeLessThan(1);
      }
    });
  });

  describe('randomInt', () => {
    it('should generate random integers in range', () => {
      for (let i = 0; i < 100; i++) {
        const value = MathUtils.randomInt(5, 10);
        expect(value).toBeGreaterThanOrEqual(5);
        expect(value).toBeLessThanOrEqual(10);
        expect(Number.isInteger(value)).toBe(true);
      }
    });
  });

  describe('pointInCircle', () => {
    it('should check if point is inside circle', () => {
      expect(MathUtils.pointInCircle(0, 0, 0, 0, 5)).toBe(true);
      expect(MathUtils.pointInCircle(3, 4, 0, 0, 5)).toBe(true);
      expect(MathUtils.pointInCircle(6, 0, 0, 0, 5)).toBe(false);
    });
  });

  describe('pointInRect', () => {
    it('should check if point is inside rectangle', () => {
      expect(MathUtils.pointInRect(5, 5, 0, 0, 10, 10)).toBe(true);
      expect(MathUtils.pointInRect(0, 0, 0, 0, 10, 10)).toBe(true);
      expect(MathUtils.pointInRect(10, 10, 0, 0, 10, 10)).toBe(true);
      expect(MathUtils.pointInRect(15, 5, 0, 0, 10, 10)).toBe(false);
    });
  });

  describe('circlesOverlap', () => {
    it('should check if circles overlap', () => {
      expect(MathUtils.circlesOverlap(0, 0, 5, 0, 0, 5)).toBe(true);
      expect(MathUtils.circlesOverlap(0, 0, 5, 8, 0, 5)).toBe(true);
      expect(MathUtils.circlesOverlap(0, 0, 5, 15, 0, 5)).toBe(false);
    });
  });

  describe('rectsOverlap', () => {
    it('should check if rectangles overlap', () => {
      expect(MathUtils.rectsOverlap(0, 0, 10, 10, 5, 5, 10, 10)).toBe(true);
      expect(MathUtils.rectsOverlap(0, 0, 10, 10, 10, 10, 10, 10)).toBe(false);
      expect(MathUtils.rectsOverlap(0, 0, 10, 10, 20, 20, 10, 10)).toBe(false);
    });
  });

  describe('closestPointOnLine', () => {
    it('should find closest point on line segment', () => {
      const result = MathUtils.closestPointOnLine(5, 5, 0, 0, 10, 0);
      expect(result.x).toBe(5);
      expect(result.y).toBe(0);
    });

    it('should handle zero-length line', () => {
      const result = MathUtils.closestPointOnLine(5, 5, 0, 0, 0, 0);
      expect(result.x).toBe(0);
      expect(result.y).toBe(0);
    });
  });

  describe('triangleArea', () => {
    it('should calculate triangle area', () => {
      expect(MathUtils.triangleArea(0, 0, 4, 0, 0, 3)).toBe(6);
      expect(MathUtils.triangleArea(0, 0, 0, 0, 0, 0)).toBe(0);
    });
  });

  describe('pointInTriangle', () => {
    it('should check if point is inside triangle', () => {
      expect(MathUtils.pointInTriangle(1, 1, 0, 0, 4, 0, 0, 3)).toBe(true);
      expect(MathUtils.pointInTriangle(5, 5, 0, 0, 4, 0, 0, 3)).toBe(false);
    });
  });

  describe('ease functions', () => {
    it('should provide easing functions', () => {
      expect(MathUtils.easeInQuad(0)).toBe(0);
      expect(MathUtils.easeInQuad(1)).toBe(1);
      expect(MathUtils.easeInQuad(0.5)).toBe(0.25);

      expect(MathUtils.easeOutQuad(0)).toBe(0);
      expect(MathUtils.easeOutQuad(1)).toBe(1);

      expect(MathUtils.easeInOutQuad(0)).toBe(0);
      expect(MathUtils.easeInOutQuad(1)).toBe(1);

      expect(MathUtils.easeInCubic(0)).toBe(0);
      expect(MathUtils.easeInCubic(1)).toBe(1);

      expect(MathUtils.easeOutCubic(0)).toBe(0);
      expect(MathUtils.easeOutCubic(1)).toBe(1);

      expect(MathUtils.easeInOutCubic(0)).toBe(0);
      expect(MathUtils.easeInOutCubic(1)).toBe(1);
    });
  });

  describe('spring', () => {
    it('should calculate spring physics', () => {
      const result = MathUtils.spring(0, 10, 0, 1, 0.9, 0.016);
      expect(result.position).toBeGreaterThan(0);
      expect(result.velocity).toBeGreaterThan(0);
    });
  });

  describe('bounce', () => {
    it('should calculate bounce physics', () => {
      expect(MathUtils.bounce(10, 0.8)).toBe(-8);
      expect(MathUtils.bounce(-10, 0.8)).toBe(8);
    });
  });

  describe('hash', () => {
    it('should generate consistent hash values', () => {
      const hash1 = MathUtils.hash(123);
      const hash2 = MathUtils.hash(123);
      expect(hash1).toBe(hash2);
      expect(hash1).toBeGreaterThanOrEqual(0);
      expect(hash1).toBeLessThan(1);
    });
  });

  describe('seededRandom', () => {
    it('should generate consistent seeded random values', () => {
      const value1 = MathUtils.seededRandom(123);
      const value2 = MathUtils.seededRandom(123);
      expect(value1).toBe(value2);
    });

    it('should respect min/max range', () => {
      const value = MathUtils.seededRandom(123, 5, 10);
      expect(value).toBeGreaterThanOrEqual(5);
      expect(value).toBeLessThan(10);
    });
  });

  describe('noise1D', () => {
    it('should generate 1D noise', () => {
      const noise = MathUtils.noise1D(5.5);
      expect(noise).toBeGreaterThanOrEqual(-1);
      expect(noise).toBeLessThanOrEqual(1);
    });

    it('should be consistent for same input', () => {
      const noise1 = MathUtils.noise1D(5.5);
      const noise2 = MathUtils.noise1D(5.5);
      expect(noise1).toBe(noise2);
    });
  });

  describe('noise2D', () => {
    it('should generate 2D noise', () => {
      const noise = MathUtils.noise2D(5.5, 3.2);
      expect(noise).toBeGreaterThanOrEqual(-1);
      expect(noise).toBeLessThanOrEqual(1);
    });

    it('should be consistent for same input', () => {
      const noise1 = MathUtils.noise2D(5.5, 3.2);
      const noise2 = MathUtils.noise2D(5.5, 3.2);
      expect(noise1).toBe(noise2);
    });
  });
});
