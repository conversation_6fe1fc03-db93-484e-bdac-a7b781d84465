/**
 * Advanced screen shake system with multiple shake types and customization
 */

import { Vector2 } from '@/utils/math/Vector2';
import { MathUtils } from '@/utils/common/MathUtils';

export type ShakeType = 'random' | 'sine' | 'perlin' | 'impact' | 'rumble';
export type FalloffType = 'linear' | 'exponential' | 'elastic' | 'bounce';

export interface ShakeConfig {
  intensity: number;
  duration: number;
  frequency: number;
  type: ShakeType;
  falloff: FalloffType;
  direction?: Vector2; // For directional shakes
  layers?: number; // For complex multi-layered shakes
}

export interface ShakeLayer {
  intensity: number;
  frequency: number;
  phase: number;
  direction: Vector2;
  elapsed: number;
}

/**
 * Advanced screen shake system for enhanced game feel
 */
export class ScreenShake {
  private active: boolean = false;
  private totalDuration: number = 0;
  private elapsed: number = 0;
  private falloffType: FalloffType = 'exponential';
  private shakeType: ShakeType = 'random';
  private baseIntensity: number = 0;
  
  private layers: ShakeLayer[] = [];
  private currentOffset: Vector2 = new Vector2(0, 0);
  
  // Noise values for Perlin-like shake
  private noiseX: number = 0;
  private noiseY: number = 0;
  private noiseSpeed: number = 1;

  /**
   * Start a new screen shake effect
   */
  start(config: ShakeConfig): void {
    this.active = true;
    this.totalDuration = config.duration;
    this.elapsed = 0;
    this.falloffType = config.falloff;
    this.shakeType = config.type;
    this.baseIntensity = config.intensity;
    
    // Clear existing layers
    this.layers = [];
    
    // Create shake layers
    const layerCount = config.layers || 1;
    for (let i = 0; i < layerCount; i++) {
      const layer: ShakeLayer = {
        intensity: config.intensity * (1 - i * 0.3), // Reduce intensity for each layer
        frequency: config.frequency * (1 + i * 0.5), // Increase frequency for each layer
        phase: Math.random() * Math.PI * 2,
        direction: config.direction ? config.direction.copy() : new Vector2(1, 1),
        elapsed: 0
      };
      this.layers.push(layer);
    }
    
    // Initialize noise for Perlin shake
    if (this.shakeType === 'perlin') {
      this.noiseX = Math.random() * 1000;
      this.noiseY = Math.random() * 1000;
      this.noiseSpeed = config.frequency / 10;
    }
  }

  /**
   * Add additional shake (stacks with existing)
   */
  addShake(config: ShakeConfig): void {
    if (!this.active) {
      this.start(config);
      return;
    }

    // Extend duration if new shake is longer
    const remainingTime = this.totalDuration - this.elapsed;
    if (config.duration > remainingTime) {
      this.totalDuration = this.elapsed + config.duration;
    }

    // Add intensity (with diminishing returns)
    this.baseIntensity = Math.min(this.baseIntensity + config.intensity * 0.5, config.intensity * 2);

    // Add new layers
    const layerCount = config.layers || 1;
    for (let i = 0; i < layerCount; i++) {
      const layer: ShakeLayer = {
        intensity: config.intensity * (1 - i * 0.3),
        frequency: config.frequency * (1 + i * 0.5),
        phase: Math.random() * Math.PI * 2,
        direction: config.direction ? config.direction.copy() : new Vector2(1, 1),
        elapsed: 0
      };
      this.layers.push(layer);
    }
  }

  /**
   * Update the screen shake effect
   */
  update(deltaTime: number): void {
    if (!this.active) {
      this.currentOffset.set(0, 0);
      return;
    }

    this.elapsed += deltaTime;

    // Check if shake is finished
    if (this.elapsed >= this.totalDuration) {
      this.stop();
      return;
    }

    // Calculate falloff multiplier
    const progress = this.elapsed / this.totalDuration;
    const falloffMultiplier = this.calculateFalloff(progress);

    // Calculate shake offset based on type
    this.currentOffset.set(0, 0);

    switch (this.shakeType) {
      case 'random':
        this.updateRandomShake(falloffMultiplier);
        break;
      case 'sine':
        this.updateSineShake(falloffMultiplier);
        break;
      case 'perlin':
        this.updatePerlinShake(deltaTime, falloffMultiplier);
        break;
      case 'impact':
        this.updateImpactShake(falloffMultiplier);
        break;
      case 'rumble':
        this.updateRumbleShake(falloffMultiplier);
        break;
    }

    // Update layer elapsed times
    for (const layer of this.layers) {
      layer.elapsed += deltaTime;
    }
  }

  /**
   * Calculate falloff based on type
   */
  private calculateFalloff(progress: number): number {
    switch (this.falloffType) {
      case 'linear':
        return 1 - progress;
      case 'exponential':
        return Math.pow(1 - progress, 2);
      case 'elastic':
        return Math.sin((1 - progress) * Math.PI) * (1 - progress);
      case 'bounce':
        const t = 1 - progress;
        if (t < 1 / 2.75) {
          return 7.5625 * t * t;
        } else if (t < 2 / 2.75) {
          const t2 = t - 1.5 / 2.75;
          return 7.5625 * t2 * t2 + 0.75;
        } else if (t < 2.5 / 2.75) {
          const t2 = t - 2.25 / 2.75;
          return 7.5625 * t2 * t2 + 0.9375;
        } else {
          const t2 = t - 2.625 / 2.75;
          return 7.5625 * t2 * t2 + 0.984375;
        }
      default:
        return 1 - progress;
    }
  }

  /**
   * Update random shake
   */
  private updateRandomShake(falloffMultiplier: number): void {
    for (const layer of this.layers) {
      const intensity = layer.intensity * falloffMultiplier;
      const offsetX = (Math.random() - 0.5) * 2 * intensity * layer.direction.x;
      const offsetY = (Math.random() - 0.5) * 2 * intensity * layer.direction.y;
      
      this.currentOffset.add(new Vector2(offsetX, offsetY));
    }
  }

  /**
   * Update sine wave shake
   */
  private updateSineShake(falloffMultiplier: number): void {
    for (const layer of this.layers) {
      const intensity = layer.intensity * falloffMultiplier;
      const time = layer.elapsed * layer.frequency + layer.phase;
      
      const offsetX = Math.sin(time) * intensity * layer.direction.x;
      const offsetY = Math.cos(time * 1.1) * intensity * layer.direction.y;
      
      this.currentOffset.add(new Vector2(offsetX, offsetY));
    }
  }

  /**
   * Update Perlin noise-like shake
   */
  private updatePerlinShake(deltaTime: number, falloffMultiplier: number): void {
    this.noiseX += deltaTime * this.noiseSpeed;
    this.noiseY += deltaTime * this.noiseSpeed * 1.1;

    for (const layer of this.layers) {
      const intensity = layer.intensity * falloffMultiplier;
      
      // Use simple noise approximation
      const offsetX = MathUtils.noise1D(this.noiseX + layer.phase) * intensity * layer.direction.x;
      const offsetY = MathUtils.noise1D(this.noiseY + layer.phase) * intensity * layer.direction.y;
      
      this.currentOffset.add(new Vector2(offsetX, offsetY));
    }
  }

  /**
   * Update impact shake (strong initial shake that fades quickly)
   */
  private updateImpactShake(falloffMultiplier: number): void {
    const impactFalloff = Math.pow(falloffMultiplier, 3); // Faster falloff for impact
    
    for (const layer of this.layers) {
      const intensity = layer.intensity * impactFalloff;
      const time = layer.elapsed * layer.frequency * 2; // Higher frequency for impact
      
      const offsetX = Math.sin(time) * intensity * layer.direction.x;
      const offsetY = Math.sin(time * 1.3) * intensity * layer.direction.y;
      
      this.currentOffset.add(new Vector2(offsetX, offsetY));
    }
  }

  /**
   * Update rumble shake (low frequency, sustained)
   */
  private updateRumbleShake(falloffMultiplier: number): void {
    for (const layer of this.layers) {
      const intensity = layer.intensity * falloffMultiplier;
      const time = layer.elapsed * layer.frequency * 0.5; // Lower frequency for rumble
      
      const offsetX = Math.sin(time) * intensity * layer.direction.x;
      const offsetY = Math.cos(time * 0.8) * intensity * layer.direction.y;
      
      // Add some randomness to rumble
      const randomX = (Math.random() - 0.5) * intensity * 0.3;
      const randomY = (Math.random() - 0.5) * intensity * 0.3;
      
      this.currentOffset.add(new Vector2(offsetX + randomX, offsetY + randomY));
    }
  }

  /**
   * Stop the screen shake
   */
  stop(): void {
    this.active = false;
    this.currentOffset.set(0, 0);
    this.layers = [];
  }

  /**
   * Get the current shake offset
   */
  getOffset(): Vector2 {
    return this.currentOffset.copy();
  }

  /**
   * Check if shake is active
   */
  isActive(): boolean {
    return this.active;
  }

  /**
   * Get shake intensity (0-1)
   */
  getIntensity(): number {
    if (!this.active) return 0;
    
    const progress = this.elapsed / this.totalDuration;
    return this.calculateFalloff(progress);
  }

  /**
   * Get remaining shake time
   */
  getRemainingTime(): number {
    if (!this.active) return 0;
    return Math.max(0, this.totalDuration - this.elapsed);
  }

  /**
   * Preset shake configurations
   */
  static readonly PRESETS = {
    // Light shake for UI feedback
    LIGHT: {
      intensity: 2,
      duration: 0.2,
      frequency: 30,
      type: 'random' as ShakeType,
      falloff: 'exponential' as FalloffType
    },

    // Medium shake for hits
    MEDIUM: {
      intensity: 5,
      duration: 0.4,
      frequency: 25,
      type: 'sine' as ShakeType,
      falloff: 'exponential' as FalloffType
    },

    // Heavy shake for explosions
    HEAVY: {
      intensity: 12,
      duration: 0.8,
      frequency: 20,
      type: 'random' as ShakeType,
      falloff: 'elastic' as FalloffType,
      layers: 2
    },

    // Impact shake for collisions
    IMPACT: {
      intensity: 8,
      duration: 0.3,
      frequency: 40,
      type: 'impact' as ShakeType,
      falloff: 'exponential' as FalloffType
    },

    // Rumble for continuous effects
    RUMBLE: {
      intensity: 3,
      duration: 2.0,
      frequency: 15,
      type: 'rumble' as ShakeType,
      falloff: 'linear' as FalloffType
    }
  };
}
