/**
 * Loot generation and equipment management system
 */

import { Vector2 } from '@/utils/math/Vector2';

export type ItemType = 'weapon' | 'armor' | 'accessory' | 'consumable' | 'material';
export type ItemRarity = 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
export type WeaponType = 'bow' | 'crossbow' | 'staff' | 'wand';
export type ArmorType = 'helmet' | 'chest' | 'legs' | 'boots' | 'gloves';
export type AccessoryType = 'ring' | 'amulet' | 'charm';

export interface ItemStats {
  damage?: number;
  attackSpeed?: number;
  criticalChance?: number;
  criticalMultiplier?: number;
  defense?: number;
  health?: number;
  mana?: number;
  speed?: number;
  spellPower?: number;
  manaRegeneration?: number;
  luck?: number;
  experienceBonus?: number;
  goldBonus?: number;
}

export interface ItemModifier {
  id: string;
  name: string;
  description: string;
  stats: ItemStats;
  rarity: ItemRarity;
}

export interface Item {
  id: string;
  name: string;
  description: string;
  type: ItemType;
  subType?: WeaponType | ArmorType | AccessoryType;
  rarity: ItemRarity;
  level: number;
  baseStats: ItemStats;
  modifiers: ItemModifier[];
  value: number;
  stackSize: number;
  currentStack: number;
  icon: string;
  sprite?: string;
}

export interface LootTable {
  id: string;
  name: string;
  entries: LootEntry[];
}

export interface LootEntry {
  itemId?: string;
  itemType?: ItemType;
  weight: number;
  minLevel: number;
  maxLevel: number;
  quantity: { min: number; max: number };
  rarity?: ItemRarity;
}

export interface DroppedLoot {
  item: Item;
  position: Vector2;
  lifetime: number;
  maxLifetime: number;
  bobOffset: number;
}

export class LootSystem {
  private itemTemplates: Map<string, Partial<Item>> = new Map();
  private modifierPool: ItemModifier[] = [];
  private lootTables: Map<string, LootTable> = new Map();
  private droppedLoot: DroppedLoot[] = [];

  constructor() {
    this.initializeItemTemplates();
    this.initializeModifiers();
    this.initializeLootTables();
  }

  private initializeItemTemplates(): void {
    // Weapons
    this.itemTemplates.set('wooden_bow', {
      name: 'Wooden Bow',
      description: 'A simple wooden bow for beginners',
      type: 'weapon',
      subType: 'bow',
      rarity: 'common',
      baseStats: { damage: 25, attackSpeed: 1.0, criticalChance: 0.05 },
      value: 50,
      stackSize: 1,
      icon: '🏹'
    });

    this.itemTemplates.set('iron_bow', {
      name: 'Iron Bow',
      description: 'A sturdy iron bow with improved accuracy',
      type: 'weapon',
      subType: 'bow',
      rarity: 'uncommon',
      baseStats: { damage: 40, attackSpeed: 1.1, criticalChance: 0.08 },
      value: 150,
      stackSize: 1,
      icon: '🏹'
    });

    this.itemTemplates.set('magic_staff', {
      name: 'Apprentice Staff',
      description: 'A basic staff that channels magical energy',
      type: 'weapon',
      subType: 'staff',
      rarity: 'common',
      baseStats: { spellPower: 30, mana: 20, manaRegeneration: 2 },
      value: 75,
      stackSize: 1,
      icon: '🪄'
    });

    this.itemTemplates.set('fire_staff', {
      name: 'Staff of Flames',
      description: 'A staff imbued with fire magic',
      type: 'weapon',
      subType: 'staff',
      rarity: 'rare',
      baseStats: { spellPower: 60, mana: 30, damage: 15 },
      value: 400,
      stackSize: 1,
      icon: '🔥'
    });

    // Armor
    this.itemTemplates.set('leather_helmet', {
      name: 'Leather Cap',
      description: 'Basic head protection',
      type: 'armor',
      subType: 'helmet',
      rarity: 'common',
      baseStats: { defense: 5, health: 10 },
      value: 30,
      stackSize: 1,
      icon: '🎩'
    });

    this.itemTemplates.set('chain_chest', {
      name: 'Chain Mail',
      description: 'Flexible metal armor',
      type: 'armor',
      subType: 'chest',
      rarity: 'uncommon',
      baseStats: { defense: 15, health: 25 },
      value: 120,
      stackSize: 1,
      icon: '🦺'
    });

    // Accessories
    this.itemTemplates.set('health_ring', {
      name: 'Ring of Vitality',
      description: 'Increases maximum health',
      type: 'accessory',
      subType: 'ring',
      rarity: 'uncommon',
      baseStats: { health: 30 },
      value: 100,
      stackSize: 1,
      icon: '💍'
    });

    this.itemTemplates.set('speed_boots', {
      name: 'Boots of Swiftness',
      description: 'Increases movement speed',
      type: 'armor',
      subType: 'boots',
      rarity: 'rare',
      baseStats: { speed: 50, defense: 8 },
      value: 200,
      stackSize: 1,
      icon: '👢'
    });

    // Consumables
    this.itemTemplates.set('health_potion', {
      name: 'Health Potion',
      description: 'Restores 50 health instantly',
      type: 'consumable',
      rarity: 'common',
      baseStats: { health: 50 },
      value: 25,
      stackSize: 10,
      icon: '🧪'
    });

    this.itemTemplates.set('mana_potion', {
      name: 'Mana Potion',
      description: 'Restores 30 mana instantly',
      type: 'consumable',
      rarity: 'common',
      baseStats: { mana: 30 },
      value: 20,
      stackSize: 10,
      icon: '💙'
    });
  }

  private initializeModifiers(): void {
    // Damage modifiers
    this.modifierPool.push({
      id: 'sharp',
      name: 'Sharp',
      description: '+10% damage',
      stats: { damage: 0.1 },
      rarity: 'common'
    });

    this.modifierPool.push({
      id: 'deadly',
      name: 'Deadly',
      description: '+5% critical chance',
      stats: { criticalChance: 0.05 },
      rarity: 'uncommon'
    });

    this.modifierPool.push({
      id: 'swift',
      name: 'Swift',
      description: '+15% attack speed',
      stats: { attackSpeed: 0.15 },
      rarity: 'uncommon'
    });

    // Defense modifiers
    this.modifierPool.push({
      id: 'sturdy',
      name: 'Sturdy',
      description: '+5 defense',
      stats: { defense: 5 },
      rarity: 'common'
    });

    this.modifierPool.push({
      id: 'fortified',
      name: 'Fortified',
      description: '+20 health',
      stats: { health: 20 },
      rarity: 'uncommon'
    });

    // Magic modifiers
    this.modifierPool.push({
      id: 'mystic',
      name: 'Mystic',
      description: '+20% spell power',
      stats: { spellPower: 0.2 },
      rarity: 'uncommon'
    });

    this.modifierPool.push({
      id: 'arcane',
      name: 'Arcane',
      description: '+15 mana',
      stats: { mana: 15 },
      rarity: 'common'
    });

    // Utility modifiers
    this.modifierPool.push({
      id: 'lucky',
      name: 'Lucky',
      description: '+2 luck',
      stats: { luck: 2 },
      rarity: 'rare'
    });

    this.modifierPool.push({
      id: 'experienced',
      name: 'of Learning',
      description: '+25% experience gain',
      stats: { experienceBonus: 0.25 },
      rarity: 'epic'
    });
  }

  private initializeLootTables(): void {
    // Basic enemy loot table
    this.lootTables.set('basic_enemy', {
      id: 'basic_enemy',
      name: 'Basic Enemy Loot',
      entries: [
        { itemId: 'health_potion', weight: 30, minLevel: 1, maxLevel: 100, quantity: { min: 1, max: 2 } },
        { itemId: 'mana_potion', weight: 20, minLevel: 1, maxLevel: 100, quantity: { min: 1, max: 1 } },
        { itemType: 'weapon', weight: 10, minLevel: 1, maxLevel: 10, quantity: { min: 1, max: 1 }, rarity: 'common' },
        { itemType: 'armor', weight: 15, minLevel: 1, maxLevel: 10, quantity: { min: 1, max: 1 }, rarity: 'common' }
      ]
    });

    // Elite enemy loot table
    this.lootTables.set('elite_enemy', {
      id: 'elite_enemy',
      name: 'Elite Enemy Loot',
      entries: [
        { itemType: 'weapon', weight: 25, minLevel: 5, maxLevel: 20, quantity: { min: 1, max: 1 }, rarity: 'uncommon' },
        { itemType: 'armor', weight: 25, minLevel: 5, maxLevel: 20, quantity: { min: 1, max: 1 }, rarity: 'uncommon' },
        { itemType: 'accessory', weight: 15, minLevel: 5, maxLevel: 20, quantity: { min: 1, max: 1 }, rarity: 'rare' },
        { itemId: 'health_potion', weight: 20, minLevel: 1, maxLevel: 100, quantity: { min: 2, max: 4 } }
      ]
    });

    // Boss loot table
    this.lootTables.set('boss', {
      id: 'boss',
      name: 'Boss Loot',
      entries: [
        { itemType: 'weapon', weight: 30, minLevel: 10, maxLevel: 50, quantity: { min: 1, max: 1 }, rarity: 'rare' },
        { itemType: 'armor', weight: 25, minLevel: 10, maxLevel: 50, quantity: { min: 1, max: 2 }, rarity: 'rare' },
        { itemType: 'accessory', weight: 20, minLevel: 10, maxLevel: 50, quantity: { min: 1, max: 1 }, rarity: 'epic' },
        { itemId: 'health_potion', weight: 15, minLevel: 1, maxLevel: 100, quantity: { min: 3, max: 6 } },
        { itemId: 'mana_potion', weight: 10, minLevel: 1, maxLevel: 100, quantity: { min: 2, max: 4 } }
      ]
    });

    // Treasure chest loot table
    this.lootTables.set('treasure_chest', {
      id: 'treasure_chest',
      name: 'Treasure Chest',
      entries: [
        { itemType: 'weapon', weight: 20, minLevel: 1, maxLevel: 30, quantity: { min: 1, max: 1 }, rarity: 'uncommon' },
        { itemType: 'armor', weight: 20, minLevel: 1, maxLevel: 30, quantity: { min: 1, max: 1 }, rarity: 'uncommon' },
        { itemType: 'accessory', weight: 15, minLevel: 1, maxLevel: 30, quantity: { min: 1, max: 1 }, rarity: 'rare' },
        { itemId: 'health_potion', weight: 25, minLevel: 1, maxLevel: 100, quantity: { min: 2, max: 5 } },
        { itemId: 'mana_potion', weight: 20, minLevel: 1, maxLevel: 100, quantity: { min: 2, max: 4 } }
      ]
    });
  }

  // Loot generation methods
  generateLoot(lootTableId: string, playerLevel: number, luckModifier: number = 0): Item[] {
    const lootTable = this.lootTables.get(lootTableId);
    if (!lootTable) return [];

    const generatedItems: Item[] = [];
    const validEntries = lootTable.entries.filter(entry => 
      playerLevel >= entry.minLevel && playerLevel <= entry.maxLevel
    );

    if (validEntries.length === 0) return [];

    // Calculate total weight
    const totalWeight = validEntries.reduce((sum, entry) => sum + entry.weight, 0);
    
    // Generate 1-3 items based on luck
    const itemCount = Math.min(3, 1 + Math.floor(Math.random() * 2) + Math.floor(luckModifier / 5));

    for (let i = 0; i < itemCount; i++) {
      const roll = Math.random() * totalWeight;
      let currentWeight = 0;

      for (const entry of validEntries) {
        currentWeight += entry.weight;
        if (roll <= currentWeight) {
          const item = this.generateItemFromEntry(entry, playerLevel, luckModifier);
          if (item) {
            generatedItems.push(item);
          }
          break;
        }
      }
    }

    return generatedItems;
  }

  private generateItemFromEntry(entry: LootEntry, playerLevel: number, luckModifier: number): Item | null {
    let itemTemplate: Partial<Item> | undefined;

    if (entry.itemId) {
      itemTemplate = this.itemTemplates.get(entry.itemId);
    } else if (entry.itemType) {
      // Find random item of specified type
      const itemsOfType = Array.from(this.itemTemplates.values())
        .filter(template => template.type === entry.itemType);
      
      if (itemsOfType.length > 0) {
        itemTemplate = itemsOfType[Math.floor(Math.random() * itemsOfType.length)];
      }
    }

    if (!itemTemplate) return null;

    // Generate quantity
    const quantity = Math.floor(Math.random() * (entry.quantity.max - entry.quantity.min + 1)) + entry.quantity.min;

    // Create item
    const item: Item = {
      id: this.generateItemId(),
      name: itemTemplate.name || 'Unknown Item',
      description: itemTemplate.description || '',
      type: itemTemplate.type || 'material',
      subType: itemTemplate.subType,
      rarity: entry.rarity || itemTemplate.rarity || 'common',
      level: playerLevel,
      baseStats: { ...itemTemplate.baseStats } || {},
      modifiers: [],
      value: itemTemplate.value || 1,
      stackSize: itemTemplate.stackSize || 1,
      currentStack: quantity,
      icon: itemTemplate.icon || '❓'
    };

    // Apply level scaling
    this.applyLevelScaling(item, playerLevel);

    // Generate modifiers based on rarity and luck
    this.generateModifiers(item, luckModifier);

    return item;
  }

  private applyLevelScaling(item: Item, level: number): void {
    const scalingFactor = 1 + (level - 1) * 0.1; // 10% increase per level

    if (item.baseStats.damage) {
      item.baseStats.damage = Math.floor(item.baseStats.damage * scalingFactor);
    }
    if (item.baseStats.defense) {
      item.baseStats.defense = Math.floor(item.baseStats.defense * scalingFactor);
    }
    if (item.baseStats.health) {
      item.baseStats.health = Math.floor(item.baseStats.health * scalingFactor);
    }
    if (item.baseStats.mana) {
      item.baseStats.mana = Math.floor(item.baseStats.mana * scalingFactor);
    }
    if (item.baseStats.spellPower) {
      item.baseStats.spellPower = Math.floor(item.baseStats.spellPower * scalingFactor);
    }

    // Scale value
    item.value = Math.floor(item.value * scalingFactor);
  }

  private generateModifiers(item: Item, luckModifier: number): void {
    const rarityModifierChance = {
      'common': 0.1,
      'uncommon': 0.3,
      'rare': 0.6,
      'epic': 0.8,
      'legendary': 1.0
    };

    const baseChance = rarityModifierChance[item.rarity] || 0.1;
    const finalChance = Math.min(0.95, baseChance + luckModifier * 0.05);

    if (Math.random() < finalChance) {
      // Filter modifiers that make sense for this item type
      const validModifiers = this.modifierPool.filter(modifier => 
        this.isModifierValidForItem(modifier, item)
      );

      if (validModifiers.length > 0) {
        const modifier = validModifiers[Math.floor(Math.random() * validModifiers.length)];
        item.modifiers.push(modifier);

        // Update item name
        if (modifier.name.startsWith('of ')) {
          item.name += ` ${modifier.name}`;
        } else {
          item.name = `${modifier.name} ${item.name}`;
        }

        // Increase value
        item.value = Math.floor(item.value * 1.5);
      }
    }
  }

  private isModifierValidForItem(modifier: ItemModifier, item: Item): boolean {
    // Damage modifiers for weapons
    if ((modifier.stats.damage || modifier.stats.criticalChance || modifier.stats.attackSpeed) && 
        item.type === 'weapon') {
      return true;
    }

    // Defense modifiers for armor
    if ((modifier.stats.defense || modifier.stats.health) && item.type === 'armor') {
      return true;
    }

    // Magic modifiers for magic items
    if ((modifier.stats.spellPower || modifier.stats.mana || modifier.stats.manaRegeneration) && 
        (item.subType === 'staff' || item.subType === 'wand' || item.type === 'accessory')) {
      return true;
    }

    // Utility modifiers for accessories
    if ((modifier.stats.luck || modifier.stats.experienceBonus || modifier.stats.goldBonus) && 
        item.type === 'accessory') {
      return true;
    }

    return false;
  }

  private generateItemId(): string {
    return 'item_' + Math.random().toString(36).substr(2, 9);
  }

  // Dropped loot management
  dropLoot(items: Item[], position: Vector2): void {
    for (const item of items) {
      const droppedItem: DroppedLoot = {
        item,
        position: position.copy(),
        lifetime: 30, // 30 seconds
        maxLifetime: 30,
        bobOffset: Math.random() * Math.PI * 2
      };
      this.droppedLoot.push(droppedItem);
    }
  }

  updateDroppedLoot(deltaTime: number): void {
    for (let i = this.droppedLoot.length - 1; i >= 0; i--) {
      const loot = this.droppedLoot[i];
      loot.lifetime -= deltaTime;
      loot.bobOffset += deltaTime * 3; // Bobbing animation

      if (loot.lifetime <= 0) {
        this.droppedLoot.splice(i, 1);
      }
    }
  }

  getDroppedLoot(): DroppedLoot[] {
    return this.droppedLoot;
  }

  pickupLoot(position: Vector2, pickupRadius: number = 32): Item[] {
    const pickedUpItems: Item[] = [];

    for (let i = this.droppedLoot.length - 1; i >= 0; i--) {
      const loot = this.droppedLoot[i];
      const distance = position.distance(loot.position);

      if (distance <= pickupRadius) {
        pickedUpItems.push(loot.item);
        this.droppedLoot.splice(i, 1);
      }
    }

    return pickedUpItems;
  }

  // Utility methods
  getRarityColor(rarity: ItemRarity): string {
    switch (rarity) {
      case 'common': return '#ffffff';
      case 'uncommon': return '#1eff00';
      case 'rare': return '#0070dd';
      case 'epic': return '#a335ee';
      case 'legendary': return '#ff8000';
      default: return '#ffffff';
    }
  }

  calculateItemStats(item: Item): ItemStats {
    const totalStats: ItemStats = { ...item.baseStats };

    // Apply modifier stats
    for (const modifier of item.modifiers) {
      for (const [stat, value] of Object.entries(modifier.stats)) {
        if (stat in totalStats) {
          const currentValue = (totalStats as any)[stat] || 0;
          
          // Handle percentage vs flat modifiers
          if (value < 1 && value > -1 && value !== 0) {
            // Percentage modifier
            (totalStats as any)[stat] = currentValue * (1 + value);
          } else {
            // Flat modifier
            (totalStats as any)[stat] = currentValue + value;
          }
        }
      }
    }

    return totalStats;
  }

  getItemDisplayName(item: Item): string {
    return item.name;
  }

  getItemTooltip(item: Item): string[] {
    const lines: string[] = [];
    
    lines.push(item.name);
    lines.push(`${item.rarity.charAt(0).toUpperCase() + item.rarity.slice(1)} ${item.type}`);
    lines.push('');
    lines.push(item.description);
    
    if (item.modifiers.length > 0) {
      lines.push('');
      for (const modifier of item.modifiers) {
        lines.push(`• ${modifier.description}`);
      }
    }
    
    lines.push('');
    lines.push(`Value: ${item.value} gold`);
    
    return lines;
  }
}
