import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ConfigUtils } from '@/utils/common/ConfigUtils';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock navigator
Object.defineProperty(navigator, 'deviceMemory', {
  value: 4,
  writable: true
});

Object.defineProperty(navigator, 'hardwareConcurrency', {
  value: 4,
  writable: true
});

describe('ConfigUtils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('loadGameConfig', () => {
    it('should return default config when no user config provided', () => {
      const config = ConfigUtils.loadGameConfig();
      
      expect(config.canvas.width).toBe(1024);
      expect(config.canvas.height).toBe(768);
      expect(config.canvas.targetFPS).toBe(60);
      expect(config.debug.showFPS).toBe(false);
    });

    it('should merge user config with defaults', () => {
      const userConfig = {
        canvas: {
          width: 1920,
          height: 1080
        },
        debug: {
          showFPS: true
        }
      };

      const config = ConfigUtils.loadGameConfig(userConfig);
      
      expect(config.canvas.width).toBe(1920);
      expect(config.canvas.height).toBe(1080);
      expect(config.canvas.targetFPS).toBe(60); // Should keep default
      expect(config.debug.showFPS).toBe(true);
    });

    it('should return default config for invalid user config', () => {
      // Mock InputValidator to return invalid
      vi.doMock('@/utils/validation/InputValidator', () => ({
        InputValidator: {
          validateGameConfig: () => ({ isValid: false, errors: ['Invalid config'] })
        }
      }));

      const config = ConfigUtils.loadGameConfig({ invalid: 'config' });
      
      expect(config.canvas.width).toBe(1024); // Should fallback to default
    });
  });

  describe('loadPlayerConfig', () => {
    it('should return default player config', () => {
      const config = ConfigUtils.loadPlayerConfig();
      
      expect(config.stats.health).toBe(100);
      expect(config.controls.moveLeft).toBe('KeyA');
      expect(config.gameplay.maxLives).toBe(3);
    });

    it('should merge user config with defaults', () => {
      const userConfig = {
        stats: {
          health: 150
        },
        controls: {
          moveLeft: 'ArrowLeft'
        }
      };

      const config = ConfigUtils.loadPlayerConfig(userConfig);
      
      expect(config.stats.health).toBe(150);
      expect(config.stats.speed).toBe(200); // Should keep default
      expect(config.controls.moveLeft).toBe('ArrowLeft');
      expect(config.controls.moveRight).toBe('KeyD'); // Should keep default
    });

    it('should validate player stats', () => {
      const userConfig = {
        stats: {
          health: -10,
          speed: 0
        }
      };

      const config = ConfigUtils.loadPlayerConfig(userConfig);
      
      // Should fallback to defaults for invalid stats
      expect(config.stats.health).toBe(100);
      expect(config.stats.speed).toBe(200);
    });

    it('should validate control keys for duplicates', () => {
      const userConfig = {
        controls: {
          moveLeft: 'KeyA',
          moveRight: 'KeyA' // Duplicate key
        }
      };

      const config = ConfigUtils.loadPlayerConfig(userConfig);
      
      // Should fallback to defaults for duplicate controls
      expect(config.controls.moveLeft).toBe('KeyA');
      expect(config.controls.moveRight).toBe('KeyD');
    });
  });

  describe('loadEnemyConfig', () => {
    it('should return default enemy config', () => {
      const config = ConfigUtils.loadEnemyConfig();
      
      expect(config.basic).toBeDefined();
      expect(config.basic.stats.health).toBe(30);
      expect(config.shooter).toBeDefined();
      expect(config.tank).toBeDefined();
    });

    it('should merge user config with defaults', () => {
      const userConfig = {
        basic: {
          stats: {
            health: 50
          }
        },
        custom: {
          stats: {
            health: 100,
            damage: 20,
            speed: 60,
            experienceValue: 30
          },
          ai: {
            type: 'aggressive' as const,
            detectionRange: 200,
            attackRange: 50,
            attackCooldown: 2000
          },
          spawning: {
            weight: 0.5,
            minLevel: 1,
            maxLevel: 5
          }
        }
      };

      const config = ConfigUtils.loadEnemyConfig(userConfig);
      
      expect(config.basic.stats.health).toBe(50);
      expect(config.basic.stats.damage).toBe(5); // Should keep default
      expect(config.custom).toBeDefined();
      expect(config.custom.stats.health).toBe(100);
    });

    it('should validate enemy stats', () => {
      const userConfig = {
        basic: {
          stats: {
            health: -10,
            speed: 0
          }
        }
      };

      const config = ConfigUtils.loadEnemyConfig(userConfig);
      
      // Should fallback to basic defaults for invalid stats
      expect(config.basic.stats.health).toBe(30);
      expect(config.basic.stats.speed).toBe(80);
    });
  });

  describe('saveConfig', () => {
    it('should save config to localStorage', () => {
      const config = { test: 'value' };
      
      ConfigUtils.saveConfig('testKey', config);
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith('testKey', JSON.stringify(config));
    });

    it('should handle localStorage errors gracefully', () => {
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('Storage full');
      });

      // Should not throw
      expect(() => {
        ConfigUtils.saveConfig('testKey', { test: 'value' });
      }).not.toThrow();
    });
  });

  describe('loadConfig', () => {
    it('should load config from localStorage', () => {
      const storedConfig = { test: 'stored' };
      localStorageMock.getItem.mockReturnValue(JSON.stringify(storedConfig));

      const defaultConfig = { test: 'default', other: 'value' };
      const result = ConfigUtils.loadConfig('testKey', defaultConfig);
      
      expect(result.test).toBe('stored');
      expect(result.other).toBe('value'); // Should merge with default
    });

    it('should return default config when localStorage is empty', () => {
      localStorageMock.getItem.mockReturnValue(null);

      const defaultConfig = { test: 'default' };
      const result = ConfigUtils.loadConfig('testKey', defaultConfig);
      
      expect(result).toEqual(defaultConfig);
    });

    it('should handle localStorage errors gracefully', () => {
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('Storage error');
      });

      const defaultConfig = { test: 'default' };
      const result = ConfigUtils.loadConfig('testKey', defaultConfig);
      
      expect(result).toEqual(defaultConfig);
    });

    it('should handle invalid JSON gracefully', () => {
      localStorageMock.getItem.mockReturnValue('invalid json');

      const defaultConfig = { test: 'default' };
      const result = ConfigUtils.loadConfig('testKey', defaultConfig);
      
      expect(result).toEqual(defaultConfig);
    });
  });

  describe('getConfigForResolution', () => {
    it('should return config for high resolution', () => {
      const config = ConfigUtils.getConfigForResolution(2560, 1440);
      
      expect(config.canvas?.width).toBe(2560);
      expect(config.canvas?.height).toBe(1440);
      expect(config.performance?.maxEntities).toBe(500); // Reduced for high res
      expect(config.graphics?.particleCount).toBe(50); // Reduced for high res
    });

    it('should return config for low resolution', () => {
      const config = ConfigUtils.getConfigForResolution(800, 600);
      
      expect(config.canvas?.width).toBe(800);
      expect(config.canvas?.height).toBe(600);
      expect(config.performance?.maxEntities).toBe(1500); // Increased for low res
      expect(config.graphics?.particleCount).toBe(150); // Increased for low res
    });

    it('should return config for standard resolution', () => {
      const config = ConfigUtils.getConfigForResolution(1920, 1080);
      
      expect(config.canvas?.width).toBe(1920);
      expect(config.canvas?.height).toBe(1080);
      // Should not have performance/graphics overrides for standard res
      expect(config.performance).toEqual({});
      expect(config.graphics).toEqual({});
    });
  });

  describe('detectOptimalConfig', () => {
    it('should detect config for low-end device', () => {
      Object.defineProperty(navigator, 'deviceMemory', { value: 2, writable: true });
      Object.defineProperty(navigator, 'hardwareConcurrency', { value: 2, writable: true });

      const config = ConfigUtils.detectOptimalConfig();
      
      expect(config.performance?.maxEntities).toBe(300);
      expect(config.graphics?.particleCount).toBe(50);
      expect(config.graphics?.shadowQuality).toBe('low');
      expect(config.graphics?.antiAliasing).toBe(false);
    });

    it('should detect config for high-end device', () => {
      Object.defineProperty(navigator, 'deviceMemory', { value: 16, writable: true });
      Object.defineProperty(navigator, 'hardwareConcurrency', { value: 12, writable: true });

      const config = ConfigUtils.detectOptimalConfig();
      
      expect(config.performance?.maxEntities).toBe(2000);
      expect(config.graphics?.particleCount).toBe(200);
      expect(config.graphics?.shadowQuality).toBe('high');
      expect(config.graphics?.antiAliasing).toBe(true);
    });

    it('should handle missing device capabilities', () => {
      Object.defineProperty(navigator, 'deviceMemory', { value: undefined, writable: true });
      Object.defineProperty(navigator, 'hardwareConcurrency', { value: undefined, writable: true });

      const config = ConfigUtils.detectOptimalConfig();
      
      // Should return empty config for unknown capabilities
      expect(config).toEqual({});
    });
  });

  describe('validateConfig', () => {
    it('should validate valid config', () => {
      const config = {
        canvas: {
          width: 1024,
          height: 768,
          targetFPS: 60
        },
        performance: {
          maxEntities: 1000
        },
        audio: {
          masterVolume: 0.8
        }
      };

      const result = ConfigUtils.validateConfig(config);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect invalid canvas settings', () => {
      const config = {
        canvas: {
          width: 100, // Too small
          height: 5000, // Too large
          targetFPS: 200 // Too high
        }
      };

      const result = ConfigUtils.validateConfig(config);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Canvas width must be between 320 and 4096');
      expect(result.errors).toContain('Canvas height must be between 240 and 2160');
      expect(result.errors).toContain('Target FPS must be between 30 and 120');
    });

    it('should detect invalid performance settings', () => {
      const config = {
        performance: {
          maxEntities: 10000 // Too high
        }
      };

      const result = ConfigUtils.validateConfig(config);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Max entities must be between 10 and 5000');
    });

    it('should detect invalid audio settings', () => {
      const config = {
        audio: {
          masterVolume: 1.5 // Too high
        }
      };

      const result = ConfigUtils.validateConfig(config);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Master volume must be between 0 and 1');
    });
  });

  describe('getDefaults', () => {
    it('should return all default configurations', () => {
      const defaults = ConfigUtils.getDefaults();
      
      expect(defaults.game).toBeDefined();
      expect(defaults.player).toBeDefined();
      expect(defaults.enemies).toBeDefined();
      
      expect(defaults.game.canvas.width).toBe(1024);
      expect(defaults.player.stats.health).toBe(100);
      expect(defaults.enemies.basic).toBeDefined();
    });
  });
});
