import { describe, it, expect, beforeEach } from 'vitest';
import { CollisionSystem } from '@/engine/physics/CollisionSystem';
import { OptimizedCollisionManager, SpatialObject } from '@/engine/physics/SpatialGrid';
import { Vector2 } from '@/utils/math/Vector2';

describe('Collision Performance Tests', () => {
  const TARGET_FPS = 60;
  const FRAME_TIME_BUDGET = 1000 / TARGET_FPS; // ~16.67ms per frame
  const COLLISION_TIME_BUDGET = FRAME_TIME_BUDGET * 0.3; // 30% of frame time for collisions

  describe('Naive O(n²) vs Optimized Collision Detection', () => {
    const createTestObjects = (count: number): SpatialObject[] => {
      const objects: SpatialObject[] = [];
      for (let i = 0; i < count; i++) {
        objects.push({
          position: {
            x: Math.random() * 1000,
            y: Math.random() * 1000
          },
          collisionRadius: 10 + Math.random() * 20,
          id: `obj_${i}`
        });
      }
      return objects;
    };

    const naiveCollisionDetection = (group1: SpatialObject[], group2: SpatialObject[]): number => {
      let collisions = 0;
      for (const obj1 of group1) {
        for (const obj2 of group2) {
          const dx = obj1.position.x - obj2.position.x;
          const dy = obj1.position.y - obj2.position.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          const minDistance = obj1.collisionRadius + obj2.collisionRadius;
          
          if (distance < minDistance) {
            collisions++;
          }
        }
      }
      return collisions;
    };

    it('should handle 50 objects within frame budget', () => {
      const objects1 = createTestObjects(25);
      const objects2 = createTestObjects(25);
      
      const collisionManager = new OptimizedCollisionManager(64);
      
      const startTime = performance.now();
      
      let collisionCount = 0;
      collisionManager.checkCollisions(
        objects1,
        objects2,
        () => { collisionCount++; }
      );
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeLessThan(COLLISION_TIME_BUDGET);
      expect(collisionManager.getCollisionChecks()).toBeLessThan(objects1.length * objects2.length);
    });

    it('should handle 100 objects within frame budget', () => {
      const objects1 = createTestObjects(50);
      const objects2 = createTestObjects(50);
      
      const collisionManager = new OptimizedCollisionManager(64);
      
      const startTime = performance.now();
      
      let collisionCount = 0;
      collisionManager.checkCollisions(
        objects1,
        objects2,
        () => { collisionCount++; }
      );
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeLessThan(COLLISION_TIME_BUDGET);
    });

    it('should outperform naive approach with many objects', () => {
      const objects1 = createTestObjects(30);
      const objects2 = createTestObjects(30);
      
      // Naive approach
      const naiveStartTime = performance.now();
      const naiveCollisions = naiveCollisionDetection(objects1, objects2);
      const naiveEndTime = performance.now();
      const naiveDuration = naiveEndTime - naiveStartTime;
      
      // Optimized approach
      const collisionManager = new OptimizedCollisionManager(64);
      const optimizedStartTime = performance.now();
      
      let optimizedCollisions = 0;
      collisionManager.checkCollisions(
        objects1,
        objects2,
        () => { optimizedCollisions++; }
      );
      
      const optimizedEndTime = performance.now();
      const optimizedDuration = optimizedEndTime - optimizedStartTime;
      
      // Optimized should be faster
      expect(optimizedDuration).toBeLessThan(naiveDuration);
      
      // Should find similar number of collisions (within reasonable margin)
      expect(Math.abs(optimizedCollisions - naiveCollisions)).toBeLessThan(5);
      
      // Should perform fewer checks
      expect(collisionManager.getCollisionChecks()).toBeLessThan(objects1.length * objects2.length);
    });

    it('should scale well with object count', () => {
      const testCases = [
        { count1: 10, count2: 10 },
        { count1: 20, count2: 20 },
        { count1: 40, count2: 40 },
        { count1: 80, count2: 80 }
      ];
      
      const results: Array<{ objectCount: number; duration: number; checks: number }> = [];
      
      for (const testCase of testCases) {
        const objects1 = createTestObjects(testCase.count1);
        const objects2 = createTestObjects(testCase.count2);
        
        const collisionManager = new OptimizedCollisionManager(64);
        const startTime = performance.now();
        
        collisionManager.checkCollisions(
          objects1,
          objects2,
          () => {}
        );
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        results.push({
          objectCount: testCase.count1 + testCase.count2,
          duration,
          checks: collisionManager.getCollisionChecks()
        });
      }
      
      // Performance should not degrade exponentially
      for (let i = 1; i < results.length; i++) {
        const prev = results[i - 1];
        const curr = results[i];
        
        const objectRatio = curr.objectCount / prev.objectCount;
        const timeRatio = curr.duration / prev.duration;
        
        // Time increase should be less than quadratic
        expect(timeRatio).toBeLessThan(objectRatio * objectRatio);
      }
    });
  });

  describe('Vector2 Performance', () => {
    it('should perform vector operations within budget', () => {
      const vectors: Vector2[] = [];
      for (let i = 0; i < 1000; i++) {
        vectors.push(new Vector2(Math.random() * 100, Math.random() * 100));
      }
      
      const startTime = performance.now();
      
      // Perform common vector operations
      for (let i = 0; i < vectors.length - 1; i++) {
        const v1 = vectors[i];
        const v2 = vectors[i + 1];
        
        // Distance calculation (common in collision detection)
        v1.distance(v2);
        
        // Normalization (common in movement)
        v1.copy().normalize();
        
        // Addition (common in physics)
        v1.copy().add(v2);
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should complete vector operations quickly
      expect(duration).toBeLessThan(5); // 5ms budget for 1000 operations
    });

    it('should prefer magnitudeSquared for distance comparisons', () => {
      const vectors: Vector2[] = [];
      for (let i = 0; i < 1000; i++) {
        vectors.push(new Vector2(Math.random() * 100, Math.random() * 100));
      }
      
      // Test magnitude() performance
      const magnitudeStartTime = performance.now();
      for (const vector of vectors) {
        vector.magnitude();
      }
      const magnitudeEndTime = performance.now();
      const magnitudeDuration = magnitudeEndTime - magnitudeStartTime;
      
      // Test magnitudeSquared() performance
      const magnitudeSquaredStartTime = performance.now();
      for (const vector of vectors) {
        vector.magnitudeSquared();
      }
      const magnitudeSquaredEndTime = performance.now();
      const magnitudeSquaredDuration = magnitudeSquaredEndTime - magnitudeSquaredStartTime;
      
      // magnitudeSquared should be faster (no sqrt)
      expect(magnitudeSquaredDuration).toBeLessThan(magnitudeDuration);
    });
  });

  describe('Object Pool Performance', () => {
    it('should maintain consistent allocation times', () => {
      const { ObjectPool } = await import('@/engine/core/ObjectPool');
      
      class TestObject {
        private active = false;
        public value = 0;
        
        reset() {
          this.value = 0;
        }
        
        isActive() {
          return this.active;
        }
        
        setActive(active: boolean) {
          this.active = active;
        }
      }
      
      const pool = new ObjectPool(() => new TestObject(), 100, 1000);
      
      const allocationTimes: number[] = [];
      
      // Test allocation performance
      for (let i = 0; i < 500; i++) {
        const startTime = performance.now();
        const obj = pool.acquire();
        const endTime = performance.now();
        
        allocationTimes.push(endTime - startTime);
        
        // Use object briefly then release
        obj.value = i;
        pool.release(obj);
      }
      
      // Calculate average allocation time
      const avgTime = allocationTimes.reduce((a, b) => a + b, 0) / allocationTimes.length;
      
      // Should be very fast (sub-millisecond)
      expect(avgTime).toBeLessThan(0.1);
      
      // Should have consistent performance (low variance)
      const variance = allocationTimes.reduce((acc, time) => acc + Math.pow(time - avgTime, 2), 0) / allocationTimes.length;
      const stdDev = Math.sqrt(variance);
      
      expect(stdDev).toBeLessThan(0.05); // Low variance in allocation times
    });
  });

  describe('Spatial Grid Performance', () => {
    it('should handle frequent insertions and removals', () => {
      const { SpatialGrid } = await import('@/engine/physics/SpatialGrid');
      
      const grid = new SpatialGrid(64);
      const objects: SpatialObject[] = [];
      
      // Create objects
      for (let i = 0; i < 200; i++) {
        objects.push({
          position: { x: Math.random() * 1000, y: Math.random() * 1000 },
          collisionRadius: 10,
          id: `obj_${i}`
        });
      }
      
      const startTime = performance.now();
      
      // Insert all objects
      for (const obj of objects) {
        grid.insert(obj);
      }
      
      // Update positions and re-insert (simulating movement)
      for (let frame = 0; frame < 10; frame++) {
        for (const obj of objects) {
          obj.position.x += (Math.random() - 0.5) * 10;
          obj.position.y += (Math.random() - 0.5) * 10;
          grid.update(obj);
        }
      }
      
      // Remove all objects
      for (const obj of objects) {
        grid.remove(obj);
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should handle spatial operations efficiently
      expect(duration).toBeLessThan(10); // 10ms budget for all operations
    });

    it('should provide efficient neighbor queries', () => {
      const { SpatialGrid } = await import('@/engine/physics/SpatialGrid');
      
      const grid = new SpatialGrid(64);
      const objects: SpatialObject[] = [];
      
      // Create clustered objects
      for (let i = 0; i < 100; i++) {
        objects.push({
          position: { 
            x: 500 + (Math.random() - 0.5) * 200, 
            y: 400 + (Math.random() - 0.5) * 200 
          },
          collisionRadius: 10,
          id: `obj_${i}`
        });
        grid.insert(objects[i]);
      }
      
      const queryObject: SpatialObject = {
        position: { x: 500, y: 400 },
        collisionRadius: 50,
        id: 'query'
      };
      
      const startTime = performance.now();
      
      // Perform many neighbor queries
      for (let i = 0; i < 100; i++) {
        const neighbors = grid.getNearbyObjects(queryObject);
        expect(neighbors.length).toBeGreaterThan(0);
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should perform queries efficiently
      expect(duration).toBeLessThan(5); // 5ms for 100 queries
    });
  });

  describe('Frame Time Budget Compliance', () => {
    it('should complete full game update within frame budget', async () => {
      // This test simulates a full game frame update
      const { GameScene } = await import('@/game/scenes/GameScene');
      const { InputManager } = await import('@/engine/input/InputManager');
      
      const gameScene = new GameScene();
      const inputManager = new InputManager();
      
      gameScene.setInputManager(inputManager);
      gameScene.initialize();
      
      // Add many objects to stress test
      for (let i = 0; i < 30; i++) {
        gameScene['spawnEnemy'](Math.random() * 1000, Math.random() * 700, 'basic');
      }
      
      const deltaTime = 1/60; // 60 FPS
      const startTime = performance.now();
      
      // Simulate multiple frames
      for (let frame = 0; frame < 10; frame++) {
        gameScene.fixedUpdate(deltaTime);
        gameScene.update(deltaTime);
      }
      
      const endTime = performance.now();
      const totalDuration = endTime - startTime;
      const avgFrameTime = totalDuration / 10;
      
      // Average frame time should be within budget
      expect(avgFrameTime).toBeLessThan(FRAME_TIME_BUDGET);
    });
  });
});
