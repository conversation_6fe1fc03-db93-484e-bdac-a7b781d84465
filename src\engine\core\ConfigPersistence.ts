/**
 * Configuration persistence system for saving and loading user preferences
 */

import { SafeOperations } from '@/utils/safety/SafeOperations';
import { InputValidator } from '@/utils/validation/InputValidator';

export interface PersistentConfig {
  version: string;
  timestamp: number;
  settings: {
    graphics: GraphicsSettings;
    audio: AudioSettings;
    controls: ControlSettings;
    gameplay: GameplaySettings;
    accessibility: AccessibilitySettings;
  };
}

export interface GraphicsSettings {
  resolution: { width: number; height: number };
  fullscreen: boolean;
  vsync: boolean;
  particleQuality: 'low' | 'medium' | 'high';
  shadowQuality: 'off' | 'low' | 'medium' | 'high';
  antiAliasing: boolean;
  textureQuality: 'low' | 'medium' | 'high';
  frameRateLimit: number;
}

export interface AudioSettings {
  masterVolume: number;
  musicVolume: number;
  sfxVolume: number;
  voiceVolume: number;
  muteWhenUnfocused: boolean;
  audioQuality: 'low' | 'medium' | 'high';
}

export interface ControlSettings {
  keyBindings: Record<string, string>;
  mouseSensitivity: number;
  invertMouseY: boolean;
  gamepadEnabled: boolean;
  gamepadDeadzone: number;
  touchControlsEnabled: boolean;
}

export interface GameplaySettings {
  difficulty: 'easy' | 'normal' | 'hard' | 'nightmare';
  autoSave: boolean;
  autoSaveInterval: number;
  showDamageNumbers: boolean;
  showHealthBars: boolean;
  pauseOnFocusLoss: boolean;
  skipIntros: boolean;
}

export interface AccessibilitySettings {
  colorBlindMode: 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia';
  highContrast: boolean;
  largeText: boolean;
  reducedMotion: boolean;
  screenReader: boolean;
  subtitles: boolean;
  flashingEffects: boolean;
}

/**
 * Manages persistent configuration storage and retrieval
 */
export class ConfigPersistence {
  private static readonly STORAGE_KEY = 'bulletHellRPG_config';
  private static readonly CURRENT_VERSION = '1.0.0';
  private static readonly MAX_BACKUP_CONFIGS = 3;

  private currentConfig: PersistentConfig | null = null;
  private defaultConfig: PersistentConfig;

  constructor() {
    this.defaultConfig = this.createDefaultConfig();
    this.loadConfig();
  }

  /**
   * Create default configuration
   */
  private createDefaultConfig(): PersistentConfig {
    return {
      version: ConfigPersistence.CURRENT_VERSION,
      timestamp: Date.now(),
      settings: {
        graphics: {
          resolution: { width: 1024, height: 768 },
          fullscreen: false,
          vsync: true,
          particleQuality: 'medium',
          shadowQuality: 'medium',
          antiAliasing: true,
          textureQuality: 'medium',
          frameRateLimit: 60
        },
        audio: {
          masterVolume: 0.8,
          musicVolume: 0.7,
          sfxVolume: 0.8,
          voiceVolume: 0.9,
          muteWhenUnfocused: true,
          audioQuality: 'medium'
        },
        controls: {
          keyBindings: {
            moveUp: 'KeyW',
            moveDown: 'KeyS',
            moveLeft: 'KeyA',
            moveRight: 'KeyD',
            shoot: 'Space',
            interact: 'KeyE',
            inventory: 'KeyI',
            pause: 'Escape',
            map: 'KeyM',
            skills: 'KeyK'
          },
          mouseSensitivity: 1.0,
          invertMouseY: false,
          gamepadEnabled: true,
          gamepadDeadzone: 0.1,
          touchControlsEnabled: true
        },
        gameplay: {
          difficulty: 'normal',
          autoSave: true,
          autoSaveInterval: 300, // 5 minutes
          showDamageNumbers: true,
          showHealthBars: true,
          pauseOnFocusLoss: true,
          skipIntros: false
        },
        accessibility: {
          colorBlindMode: 'none',
          highContrast: false,
          largeText: false,
          reducedMotion: false,
          screenReader: false,
          subtitles: false,
          flashingEffects: true
        }
      }
    };
  }

  /**
   * Load configuration from localStorage
   */
  loadConfig(): PersistentConfig {
    try {
      const stored = localStorage.getItem(ConfigPersistence.STORAGE_KEY);
      if (!stored) {
        this.currentConfig = this.defaultConfig;
        return this.currentConfig;
      }

      const parsed = JSON.parse(stored);
      const validation = this.validateConfig(parsed);
      
      if (validation.isValid) {
        this.currentConfig = this.migrateConfig(parsed);
      } else {
        console.warn('Invalid config found, using defaults:', validation.errors);
        this.currentConfig = this.defaultConfig;
        this.saveConfig(); // Save valid default config
      }
    } catch (error) {
      console.error('Failed to load config:', error);
      this.currentConfig = this.defaultConfig;
    }

    return this.currentConfig;
  }

  /**
   * Save configuration to localStorage
   */
  saveConfig(config?: Partial<PersistentConfig>): boolean {
    try {
      if (config) {
        this.currentConfig = this.mergeConfig(this.currentConfig || this.defaultConfig, config);
      }

      if (!this.currentConfig) {
        this.currentConfig = this.defaultConfig;
      }

      // Update timestamp
      this.currentConfig.timestamp = Date.now();

      // Create backup before saving
      this.createBackup();

      // Save to localStorage
      const serialized = JSON.stringify(this.currentConfig, null, 2);
      localStorage.setItem(ConfigPersistence.STORAGE_KEY, serialized);

      return true;
    } catch (error) {
      console.error('Failed to save config:', error);
      return false;
    }
  }

  /**
   * Validate configuration object
   */
  private validateConfig(config: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check basic structure
    if (!config || typeof config !== 'object') {
      errors.push('Config must be an object');
      return { isValid: false, errors };
    }

    // Check version
    if (!config.version || typeof config.version !== 'string') {
      errors.push('Config must have a valid version');
    }

    // Check settings structure
    if (!config.settings || typeof config.settings !== 'object') {
      errors.push('Config must have settings object');
      return { isValid: false, errors };
    }

    // Validate graphics settings
    if (config.settings.graphics) {
      const graphics = config.settings.graphics;
      if (graphics.resolution) {
        if (typeof graphics.resolution.width !== 'number' || graphics.resolution.width < 320) {
          errors.push('Invalid graphics resolution width');
        }
        if (typeof graphics.resolution.height !== 'number' || graphics.resolution.height < 240) {
          errors.push('Invalid graphics resolution height');
        }
      }
      if (graphics.frameRateLimit && (graphics.frameRateLimit < 30 || graphics.frameRateLimit > 240)) {
        errors.push('Invalid frame rate limit');
      }
    }

    // Validate audio settings
    if (config.settings.audio) {
      const audio = config.settings.audio;
      const volumeFields = ['masterVolume', 'musicVolume', 'sfxVolume', 'voiceVolume'];
      for (const field of volumeFields) {
        if (audio[field] !== undefined && (audio[field] < 0 || audio[field] > 1)) {
          errors.push(`Invalid ${field}: must be between 0 and 1`);
        }
      }
    }

    // Validate controls
    if (config.settings.controls) {
      const controls = config.settings.controls;
      if (controls.mouseSensitivity && (controls.mouseSensitivity < 0.1 || controls.mouseSensitivity > 5)) {
        errors.push('Invalid mouse sensitivity');
      }
      if (controls.gamepadDeadzone && (controls.gamepadDeadzone < 0 || controls.gamepadDeadzone > 1)) {
        errors.push('Invalid gamepad deadzone');
      }
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Migrate configuration to current version
   */
  private migrateConfig(config: any): PersistentConfig {
    // If already current version, return as-is
    if (config.version === ConfigPersistence.CURRENT_VERSION) {
      return config;
    }

    console.log(`Migrating config from ${config.version} to ${ConfigPersistence.CURRENT_VERSION}`);

    // Merge with defaults to ensure all new fields are present
    const migrated = this.mergeConfig(this.defaultConfig, config);
    migrated.version = ConfigPersistence.CURRENT_VERSION;

    return migrated;
  }

  /**
   * Merge configuration objects
   */
  private mergeConfig(base: PersistentConfig, override: Partial<PersistentConfig>): PersistentConfig {
    const merged = JSON.parse(JSON.stringify(base)); // Deep clone

    if (override.settings) {
      for (const [category, settings] of Object.entries(override.settings)) {
        if (merged.settings[category as keyof typeof merged.settings]) {
          Object.assign(merged.settings[category as keyof typeof merged.settings], settings);
        }
      }
    }

    if (override.version) merged.version = override.version;
    if (override.timestamp) merged.timestamp = override.timestamp;

    return merged;
  }

  /**
   * Create backup of current config
   */
  private createBackup(): void {
    try {
      const backupKey = `${ConfigPersistence.STORAGE_KEY}_backup_${Date.now()}`;
      const current = localStorage.getItem(ConfigPersistence.STORAGE_KEY);
      
      if (current) {
        localStorage.setItem(backupKey, current);
        this.cleanupOldBackups();
      }
    } catch (error) {
      console.warn('Failed to create config backup:', error);
    }
  }

  /**
   * Clean up old backup configurations
   */
  private cleanupOldBackups(): void {
    try {
      const backupKeys: Array<{ key: string; timestamp: number }> = [];
      
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(`${ConfigPersistence.STORAGE_KEY}_backup_`)) {
          const timestamp = parseInt(key.split('_').pop() || '0');
          backupKeys.push({ key, timestamp });
        }
      }

      // Sort by timestamp (newest first)
      backupKeys.sort((a, b) => b.timestamp - a.timestamp);

      // Remove old backups
      for (let i = ConfigPersistence.MAX_BACKUP_CONFIGS; i < backupKeys.length; i++) {
        localStorage.removeItem(backupKeys[i].key);
      }
    } catch (error) {
      console.warn('Failed to cleanup old backups:', error);
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): PersistentConfig {
    return this.currentConfig || this.defaultConfig;
  }

  /**
   * Get specific settings category
   */
  getSettings<T extends keyof PersistentConfig['settings']>(category: T): PersistentConfig['settings'][T] {
    return this.getConfig().settings[category];
  }

  /**
   * Update specific settings category
   */
  updateSettings<T extends keyof PersistentConfig['settings']>(
    category: T,
    settings: Partial<PersistentConfig['settings'][T]>
  ): boolean {
    const current = this.getConfig();
    const updated = {
      ...current,
      settings: {
        ...current.settings,
        [category]: {
          ...current.settings[category],
          ...settings
        }
      }
    };

    return this.saveConfig(updated);
  }

  /**
   * Reset to default configuration
   */
  resetToDefaults(): boolean {
    this.currentConfig = this.createDefaultConfig();
    return this.saveConfig();
  }

  /**
   * Export configuration as JSON string
   */
  exportConfig(): string {
    return JSON.stringify(this.getConfig(), null, 2);
  }

  /**
   * Import configuration from JSON string
   */
  importConfig(configJson: string): boolean {
    try {
      const parsed = JSON.parse(configJson);
      const validation = this.validateConfig(parsed);
      
      if (validation.isValid) {
        return this.saveConfig(parsed);
      } else {
        console.error('Invalid imported config:', validation.errors);
        return false;
      }
    } catch (error) {
      console.error('Failed to import config:', error);
      return false;
    }
  }

  /**
   * Check if localStorage is available
   */
  static isStorageAvailable(): boolean {
    try {
      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get storage usage information
   */
  getStorageInfo(): { used: number; available: number; percentage: number } {
    try {
      let used = 0;
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          used += key.length + (localStorage.getItem(key)?.length || 0);
        }
      }

      // Estimate available space (most browsers limit to ~5-10MB)
      const estimated = 5 * 1024 * 1024; // 5MB estimate
      const available = Math.max(0, estimated - used);
      const percentage = (used / estimated) * 100;

      return { used, available, percentage };
    } catch {
      return { used: 0, available: 0, percentage: 0 };
    }
  }
}
