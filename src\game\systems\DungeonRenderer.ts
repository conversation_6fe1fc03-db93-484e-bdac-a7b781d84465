/**
 * Dungeon rendering system for visualizing procedurally generated dungeons
 */

import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';
import { Vector2 } from '@/utils/math/Vector2';
import { Room, Corridor, DungeonTile } from './DungeonGenerator';

export interface DungeonRenderConfig {
  tileSize: number;
  showGrid: boolean;
  showRoomIds: boolean;
  showConnections: boolean;
  colors: {
    wall: string;
    floor: string;
    door: string;
    void: string;
    corridor: string;
    roomBorder: string;
    connection: string;
    player: string;
    enemy: string;
    loot: string;
    visited: string;
  };
}

export class DungeonRenderer {
  private config: DungeonRenderConfig;
  private camera: Vector2 = new Vector2(0, 0);
  private zoom: number = 1;

  constructor(config: Partial<DungeonRenderConfig> = {}) {
    this.config = {
      tileSize: 16,
      showGrid: false,
      showRoomIds: false,
      showConnections: false,
      colors: {
        wall: '#444444',
        floor: '#cccccc',
        door: '#8B4513',
        void: '#000000',
        corridor: '#aaaaaa',
        roomBorder: '#666666',
        connection: '#ff0000',
        player: '#4CAF50',
        enemy: '#ff6b6b',
        loot: '#FFD700',
        visited: '#e8f5e8'
      },
      ...config
    };
  }

  setCamera(x: number, y: number): void {
    this.camera.set(x, y);
  }

  setZoom(zoom: number): void {
    this.zoom = Math.max(0.1, Math.min(5, zoom));
  }

  renderDungeon(
    renderer: CanvasRenderer,
    rooms: Room[],
    corridors: Corridor[],
    tiles: DungeonTile[][],
    playerPos?: Vector2,
    enemies?: Vector2[],
    loot?: Vector2[]
  ): void {
    const { width, height } = renderer.getCanvasSize();
    
    // Apply camera transform
    renderer.pushTransform();
    renderer.translate(-this.camera.x * this.zoom, -this.camera.y * this.zoom);
    renderer.scale(this.zoom, this.zoom);

    // Calculate visible area
    const visibleArea = this.calculateVisibleArea(width, height);
    
    // Render tiles
    this.renderTiles(renderer, tiles, visibleArea);
    
    // Render rooms
    if (this.config.showRoomIds || this.config.showConnections) {
      this.renderRooms(renderer, rooms, visibleArea);
    }
    
    // Render corridors
    if (this.config.showConnections) {
      this.renderCorridors(renderer, corridors);
    }
    
    // Render entities
    if (enemies) {
      this.renderEnemies(renderer, enemies, visibleArea);
    }
    
    if (loot) {
      this.renderLoot(renderer, loot, visibleArea);
    }
    
    if (playerPos) {
      this.renderPlayer(renderer, playerPos);
    }
    
    // Render grid
    if (this.config.showGrid) {
      this.renderGrid(renderer, tiles, visibleArea);
    }

    renderer.popTransform();
  }

  private calculateVisibleArea(screenWidth: number, screenHeight: number): {
    startX: number;
    endX: number;
    startY: number;
    endY: number;
  } {
    const tileSize = this.config.tileSize;
    const startX = Math.floor((this.camera.x - screenWidth / (2 * this.zoom)) / tileSize);
    const endX = Math.ceil((this.camera.x + screenWidth / (2 * this.zoom)) / tileSize);
    const startY = Math.floor((this.camera.y - screenHeight / (2 * this.zoom)) / tileSize);
    const endY = Math.ceil((this.camera.y + screenHeight / (2 * this.zoom)) / tileSize);
    
    return { startX, endX, startY, endY };
  }

  private renderTiles(renderer: CanvasRenderer, tiles: DungeonTile[][], visibleArea: any): void {
    const tileSize = this.config.tileSize;
    
    for (let y = Math.max(0, visibleArea.startY); y < Math.min(tiles.length, visibleArea.endY); y++) {
      for (let x = Math.max(0, visibleArea.startX); x < Math.min(tiles[y].length, visibleArea.endX); x++) {
        const tile = tiles[y][x];
        const pixelX = x * tileSize;
        const pixelY = y * tileSize;
        
        let color = this.config.colors.void;
        
        switch (tile.type) {
          case 'wall':
            color = this.config.colors.wall;
            break;
          case 'floor':
            color = tile.isCorridorTile ? this.config.colors.corridor : this.config.colors.floor;
            break;
          case 'door':
            color = this.config.colors.door;
            break;
          case 'void':
            color = this.config.colors.void;
            break;
        }
        
        renderer.drawRect(pixelX, pixelY, tileSize, tileSize, color);
      }
    }
  }

  private renderRooms(renderer: CanvasRenderer, rooms: Room[], visibleArea: any): void {
    const tileSize = this.config.tileSize;
    
    for (const room of rooms) {
      // Check if room is in visible area
      if (room.x + room.width < visibleArea.startX || room.x > visibleArea.endX ||
          room.y + room.height < visibleArea.startY || room.y > visibleArea.endY) {
        continue;
      }
      
      const pixelX = room.x * tileSize;
      const pixelY = room.y * tileSize;
      const pixelWidth = room.width * tileSize;
      const pixelHeight = room.height * tileSize;
      
      // Highlight visited rooms
      if (room.visited) {
        renderer.setGlobalAlpha(0.3);
        renderer.drawRect(pixelX, pixelY, pixelWidth, pixelHeight, this.config.colors.visited);
        renderer.setGlobalAlpha(1);
      }
      
      // Draw room border
      renderer.drawRectOutline(pixelX, pixelY, pixelWidth, pixelHeight, this.config.colors.roomBorder, 2);
      
      // Draw room type indicator
      const centerX = pixelX + pixelWidth / 2;
      const centerY = pixelY + pixelHeight / 2;
      
      switch (room.type) {
        case 'start':
          renderer.drawCircle(centerX, centerY, 8, '#4CAF50');
          break;
        case 'boss':
          renderer.drawRect(centerX - 8, centerY - 8, 16, 16, '#ff0000');
          break;
        case 'treasure':
          renderer.drawCircle(centerX, centerY, 6, '#FFD700');
          break;
        case 'shop':
          renderer.drawCircle(centerX, centerY, 6, '#2196F3');
          break;
      }
      
      // Draw room ID
      if (this.config.showRoomIds) {
        renderer.drawText(
          room.id.toString(),
          centerX - 5,
          centerY - 5,
          '12px monospace',
          '#ffffff'
        );
      }
    }
  }

  private renderCorridors(renderer: CanvasRenderer, corridors: Corridor[]): void {
    for (const corridor of corridors) {
      for (let i = 0; i < corridor.points.length - 1; i++) {
        const start = corridor.points[i];
        const end = corridor.points[i + 1];
        
        renderer.drawLine(
          start.x * this.config.tileSize,
          start.y * this.config.tileSize,
          end.x * this.config.tileSize,
          end.y * this.config.tileSize,
          this.config.colors.connection,
          2
        );
      }
    }
  }

  private renderPlayer(renderer: CanvasRenderer, playerPos: Vector2): void {
    const tileSize = this.config.tileSize;
    const pixelX = playerPos.x * tileSize + tileSize / 2;
    const pixelY = playerPos.y * tileSize + tileSize / 2;
    
    renderer.drawCircle(pixelX, pixelY, tileSize / 3, this.config.colors.player);
  }

  private renderEnemies(renderer: CanvasRenderer, enemies: Vector2[], visibleArea: any): void {
    const tileSize = this.config.tileSize;
    
    for (const enemy of enemies) {
      if (enemy.x < visibleArea.startX || enemy.x > visibleArea.endX ||
          enemy.y < visibleArea.startY || enemy.y > visibleArea.endY) {
        continue;
      }
      
      const pixelX = enemy.x * tileSize + tileSize / 2;
      const pixelY = enemy.y * tileSize + tileSize / 2;
      
      renderer.drawCircle(pixelX, pixelY, tileSize / 4, this.config.colors.enemy);
    }
  }

  private renderLoot(renderer: CanvasRenderer, loot: Vector2[], visibleArea: any): void {
    const tileSize = this.config.tileSize;
    
    for (const item of loot) {
      if (item.x < visibleArea.startX || item.x > visibleArea.endX ||
          item.y < visibleArea.startY || item.y > visibleArea.endY) {
        continue;
      }
      
      const pixelX = item.x * tileSize + tileSize / 2;
      const pixelY = item.y * tileSize + tileSize / 2;
      
      // Draw diamond shape for loot
      const size = tileSize / 4;
      renderer.drawRect(pixelX - size/2, pixelY - size/2, size, size, this.config.colors.loot);
    }
  }

  private renderGrid(renderer: CanvasRenderer, tiles: DungeonTile[][], visibleArea: any): void {
    const tileSize = this.config.tileSize;
    
    renderer.setGlobalAlpha(0.2);
    
    // Vertical lines
    for (let x = visibleArea.startX; x <= visibleArea.endX; x++) {
      const pixelX = x * tileSize;
      renderer.drawLine(
        pixelX,
        visibleArea.startY * tileSize,
        pixelX,
        visibleArea.endY * tileSize,
        '#ffffff',
        1
      );
    }
    
    // Horizontal lines
    for (let y = visibleArea.startY; y <= visibleArea.endY; y++) {
      const pixelY = y * tileSize;
      renderer.drawLine(
        visibleArea.startX * tileSize,
        pixelY,
        visibleArea.endX * tileSize,
        pixelY,
        '#ffffff',
        1
      );
    }
    
    renderer.setGlobalAlpha(1);
  }

  // Utility methods
  worldToTile(worldPos: Vector2): Vector2 {
    return new Vector2(
      Math.floor(worldPos.x / this.config.tileSize),
      Math.floor(worldPos.y / this.config.tileSize)
    );
  }

  tileToWorld(tilePos: Vector2): Vector2 {
    return new Vector2(
      tilePos.x * this.config.tileSize + this.config.tileSize / 2,
      tilePos.y * this.config.tileSize + this.config.tileSize / 2
    );
  }

  screenToWorld(screenPos: Vector2, canvasSize: { width: number; height: number }): Vector2 {
    return new Vector2(
      (screenPos.x - canvasSize.width / 2) / this.zoom + this.camera.x,
      (screenPos.y - canvasSize.height / 2) / this.zoom + this.camera.y
    );
  }

  worldToScreen(worldPos: Vector2, canvasSize: { width: number; height: number }): Vector2 {
    return new Vector2(
      (worldPos.x - this.camera.x) * this.zoom + canvasSize.width / 2,
      (worldPos.y - this.camera.y) * this.zoom + canvasSize.height / 2
    );
  }

  // Camera controls
  followPlayer(playerPos: Vector2, smoothing: number = 0.1): void {
    const targetX = playerPos.x * this.config.tileSize;
    const targetY = playerPos.y * this.config.tileSize;
    
    this.camera.x += (targetX - this.camera.x) * smoothing;
    this.camera.y += (targetY - this.camera.y) * smoothing;
  }

  centerOnRoom(room: Room): void {
    this.camera.x = room.center.x * this.config.tileSize;
    this.camera.y = room.center.y * this.config.tileSize;
  }
}
