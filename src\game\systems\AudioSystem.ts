/**
 * Game-specific audio system integrating with AudioManager
 */

import { AudioManager } from '@/engine/audio/AudioManager';
import { Vector2 } from '@/utils/math/Vector2';

export interface SoundEvent {
  id: string;
  position?: Vector2;
  volume?: number;
  pitch?: number;
  delay?: number;
}

export class AudioSystem {
  private audioManager: AudioManager;
  private listenerPosition: Vector2 = new Vector2(0, 0);
  private isInitialized: boolean = false;
  
  // Sound categories
  private weaponSounds: Map<string, string> = new Map();
  private enemySounds: Map<string, string> = new Map();
  private uiSounds: Map<string, string> = new Map();
  private ambientSounds: Map<string, string> = new Map();

  constructor() {
    this.audioManager = new AudioManager();
    this.initializeSounds();
  }

  private async initializeSounds(): Promise<void> {
    // Generate procedural sounds first
    this.audioManager.createProceduralSounds();
    
    // Map weapon sounds
    this.weaponSounds.set('bow', 'shoot');
    this.weaponSounds.set('magic', 'pickup'); // Different tone for magic
    this.weaponSounds.set('crossbow', 'shoot');
    this.weaponSounds.set('staff', 'levelup'); // Magical sound
    this.weaponSounds.set('fireball', 'explosion');
    this.weaponSounds.set('lightning', 'hit'); // Sharp sound
    
    // Map enemy sounds
    this.enemySounds.set('death', 'enemy_death');
    this.enemySounds.set('hit', 'hit');
    this.enemySounds.set('spawn', 'pickup');
    
    // Map UI sounds
    this.uiSounds.set('click', 'pickup');
    this.uiSounds.set('hover', 'hit');
    this.uiSounds.set('levelup', 'levelup');
    this.uiSounds.set('error', 'explosion');
    this.uiSounds.set('success', 'pickup');
    
    // Create additional procedural sounds for variety
    await this.createVariationSounds();
    
    this.isInitialized = true;
    console.log('🎵 Audio system initialized');
  }

  private async createVariationSounds(): Promise<void> {
    // Create weapon sound variations
    const weaponVariations = [
      { id: 'bow_crit', freq: 1000, duration: 0.12, type: 'square' as OscillatorType },
      { id: 'magic_cast', freq: 500, duration: 0.3, type: 'sine' as OscillatorType },
      { id: 'staff_charge', freq: 300, duration: 0.8, type: 'triangle' as OscillatorType }
    ];
    
    for (const variation of weaponVariations) {
      const buffer = this.audioManager.generateTone(variation.freq, variation.duration, variation.type);
      if (buffer) {
        // Store the buffer in AudioManager's internal storage
        // This would require exposing a method to add generated sounds
      }
    }
  }

  // Weapon sounds
  playWeaponSound(weaponType: string, position?: Vector2, isCritical: boolean = false): void {
    if (!this.isInitialized) return;
    
    let soundId = this.weaponSounds.get(weaponType) || 'shoot';
    
    // Use different sound for critical hits
    if (isCritical && weaponType === 'bow') {
      soundId = 'bow_crit';
    }
    
    const volume = this.getWeaponVolume(weaponType);
    const pitch = this.getWeaponPitch(weaponType, isCritical);
    
    if (position) {
      this.audioManager.playPositional(soundId, position.x, position.y, this.listenerPosition.x, this.listenerPosition.y);
    } else {
      this.audioManager.play(soundId, { volume, pitch });
    }
  }

  private getWeaponVolume(weaponType: string): number {
    switch (weaponType) {
      case 'bow': return 0.6;
      case 'magic': return 0.4;
      case 'crossbow': return 0.7;
      case 'staff': return 0.5;
      case 'fireball': return 0.8;
      case 'lightning': return 0.9;
      default: return 0.5;
    }
  }

  private getWeaponPitch(weaponType: string, isCritical: boolean): number {
    let basePitch = 1.0;
    
    switch (weaponType) {
      case 'bow': basePitch = 1.0; break;
      case 'magic': basePitch = 1.2; break;
      case 'crossbow': basePitch = 0.8; break;
      case 'staff': basePitch = 0.9; break;
      case 'fireball': basePitch = 0.7; break;
      case 'lightning': basePitch = 1.5; break;
    }
    
    // Increase pitch for critical hits
    if (isCritical) {
      basePitch *= 1.3;
    }
    
    // Add slight random variation
    return basePitch + (Math.random() - 0.5) * 0.1;
  }

  // Enemy sounds
  playEnemySound(soundType: string, position?: Vector2, enemyType: string = 'basic'): void {
    if (!this.isInitialized) return;
    
    const soundId = this.enemySounds.get(soundType) || 'hit';
    const volume = this.getEnemyVolume(enemyType);
    const pitch = this.getEnemyPitch(enemyType);
    
    if (position) {
      this.audioManager.playPositional(soundId, position.x, position.y, this.listenerPosition.x, this.listenerPosition.y);
    } else {
      this.audioManager.play(soundId, { volume, pitch });
    }
  }

  private getEnemyVolume(enemyType: string): number {
    switch (enemyType) {
      case 'basic': return 0.5;
      case 'shooter': return 0.4;
      case 'heavy': return 0.7;
      case 'bomber': return 0.6;
      default: return 0.5;
    }
  }

  private getEnemyPitch(enemyType: string): number {
    switch (enemyType) {
      case 'basic': return 1.0;
      case 'shooter': return 1.2;
      case 'heavy': return 0.7;
      case 'bomber': return 0.9;
      default: return 1.0;
    }
  }

  // UI sounds
  playUISound(soundType: string, volume: number = 0.3): void {
    if (!this.isInitialized) return;
    
    const soundId = this.uiSounds.get(soundType) || 'pickup';
    this.audioManager.play(soundId, { volume });
  }

  // Player feedback sounds
  playPlayerHit(damage: number): void {
    const volume = Math.min(0.8, 0.3 + damage / 100);
    const pitch = Math.max(0.7, 1.0 - damage / 200);
    
    this.audioManager.play('hit', { volume, pitch });
  }

  playPlayerLevelUp(): void {
    this.audioManager.play('levelup', { volume: 0.8 });
  }

  playItemPickup(itemRarity: string = 'common'): void {
    let pitch = 1.0;
    let volume = 0.4;
    
    switch (itemRarity) {
      case 'common': pitch = 1.0; volume = 0.4; break;
      case 'uncommon': pitch = 1.1; volume = 0.5; break;
      case 'rare': pitch = 1.2; volume = 0.6; break;
      case 'epic': pitch = 1.3; volume = 0.7; break;
      case 'legendary': pitch = 1.5; volume = 0.8; break;
    }
    
    this.audioManager.play('pickup', { volume, pitch });
  }

  // Explosion effects
  playExplosion(position: Vector2, size: number = 1.0): void {
    const volume = Math.min(0.9, 0.5 * size);
    const pitch = Math.max(0.5, 1.0 - size * 0.2);
    
    this.audioManager.playPositional('explosion', position.x, position.y, this.listenerPosition.x, this.listenerPosition.y);
  }

  // Ambient sounds
  playAmbientSound(soundType: string, loop: boolean = true): void {
    if (!this.isInitialized) return;
    
    // For now, use existing sounds as ambient
    // In a full implementation, these would be longer ambient tracks
    const soundId = this.ambientSounds.get(soundType) || 'pickup';
    this.audioManager.play(soundId, { volume: 0.2, loop });
  }

  stopAmbientSounds(): void {
    // Stop all looping sounds
    for (const [soundId] of this.ambientSounds) {
      this.audioManager.stop(soundId);
    }
  }

  // Music management
  playBackgroundMusic(trackId: string = 'main_theme'): void {
    // For demo purposes, we'll use a generated tone as background music
    this.audioManager.playMusic('levelup'); // Use levelup sound as placeholder music
  }

  stopBackgroundMusic(): void {
    this.audioManager.stopMusic(2); // 2 second fade out
  }

  // Listener position for spatial audio
  setListenerPosition(position: Vector2): void {
    this.listenerPosition = position;
  }

  getListenerPosition(): Vector2 {
    return this.listenerPosition.copy();
  }

  // Volume controls
  setMasterVolume(volume: number): void {
    this.audioManager.setMasterVolume(volume);
  }

  setMusicVolume(volume: number): void {
    this.audioManager.setMusicVolume(volume);
  }

  setSFXVolume(volume: number): void {
    this.audioManager.setSFXVolume(volume);
  }

  getMasterVolume(): number {
    return this.audioManager.getMasterVolume();
  }

  getMusicVolume(): number {
    return this.audioManager.getMusicVolume();
  }

  getSFXVolume(): number {
    return this.audioManager.getSFXVolume();
  }

  // Batch sound events
  playMultipleSounds(events: SoundEvent[]): void {
    for (const event of events) {
      setTimeout(() => {
        if (event.position) {
          this.audioManager.playPositional(
            event.id, 
            event.position.x, 
            event.position.y, 
            this.listenerPosition.x, 
            this.listenerPosition.y
          );
        } else {
          this.audioManager.play(event.id, {
            volume: event.volume,
            pitch: event.pitch
          });
        }
      }, event.delay || 0);
    }
  }

  // Audio feedback for game events
  onPlayerShoot(weaponType: string, position: Vector2, isCritical: boolean = false): void {
    this.playWeaponSound(weaponType, position, isCritical);
  }

  onEnemyHit(position: Vector2, enemyType: string): void {
    this.playEnemySound('hit', position, enemyType);
  }

  onEnemyDeath(position: Vector2, enemyType: string): void {
    this.playEnemySound('death', position, enemyType);
  }

  onPlayerHit(damage: number): void {
    this.playPlayerHit(damage);
  }

  onLevelUp(): void {
    this.playPlayerLevelUp();
  }

  onItemPickup(position: Vector2, rarity: string): void {
    this.playItemPickup(rarity);
  }

  onExplosion(position: Vector2, size: number): void {
    this.playExplosion(position, size);
  }

  onUIInteraction(type: 'click' | 'hover' | 'error' | 'success'): void {
    this.playUISound(type);
  }

  // Update method for time-based effects
  update(deltaTime: number): void {
    this.audioManager.update(deltaTime);
  }

  // Cleanup
  destroy(): void {
    this.audioManager.destroy();
  }
}
