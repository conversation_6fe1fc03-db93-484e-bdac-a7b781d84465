/**
 * Player character with movement, aiming, and combat capabilities
 */

import { Vector2 } from '@/utils/math/Vector2';
import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';
import { InputManager } from '@/engine/input/InputManager';
import { PLAYER, SCREEN } from '@/config/GameConstants';

export interface PlayerStats {
  health: number;
  maxHealth: number;
  mana: number;
  maxMana: number;
  experience: number;
  level: number;
  speed: number;
  damage: number;
  defense: number;
  criticalChance: number;
  criticalMultiplier: number;
}

export interface PlayerConfig {
  size: number;
  collisionRadius: number;
  invulnerabilityTime: number;
  healthRegenRate: number;
  manaRegenRate: number;
}

export class Player {
  public position: Vector2;
  public velocity: Vector2;
  public aimDirection: Vector2;
  public stats: PlayerStats;
  public config: PlayerConfig;
  
  // State
  public isAlive: boolean = true;
  public isInvulnerable: boolean = false;
  public invulnerabilityTimer: number = 0;
  
  // Combat
  public autoAttack: boolean = false;
  public attackCooldown: number = 0;
  public lastAttackTime: number = 0;
  
  // Animation
  public animationTime: number = 0;
  public isMoving: boolean = false;
  public facingDirection: Vector2;
  
  // Equipment (placeholder for future implementation)
  public weapon: any = null;
  public armor: any = null;

  constructor(x: number = SCREEN.WIDTH / 2, y: number = SCREEN.HEIGHT / 2) {
    this.position = new Vector2(x, y);
    this.velocity = new Vector2(0, 0);
    this.aimDirection = new Vector2(1, 0);
    this.facingDirection = new Vector2(1, 0);
    
    this.stats = {
      health: PLAYER.DEFAULT_HEALTH,
      maxHealth: PLAYER.DEFAULT_HEALTH,
      mana: PLAYER.DEFAULT_MANA,
      maxMana: PLAYER.DEFAULT_MANA,
      experience: 0,
      level: 1,
      speed: PLAYER.DEFAULT_SPEED,
      damage: PLAYER.DEFAULT_DAMAGE,
      defense: PLAYER.DEFAULT_DEFENSE,
      criticalChance: 0.05,
      criticalMultiplier: 2.0
    };
    
    this.config = {
      size: 32,
      collisionRadius: PLAYER.COLLISION_RADIUS,
      invulnerabilityTime: PLAYER.INVULNERABILITY_TIME,
      healthRegenRate: PLAYER.REGENERATION_RATE, // HP per second
      manaRegenRate: 5    // MP per second
    };
  }

  update(deltaTime: number, inputManager: InputManager, gameObjectManager: any): void {
    if (!this.isAlive) return;

    this.updateMovement(deltaTime, inputManager);
    this.updateAiming(inputManager);
    this.updateCombat(deltaTime, inputManager, gameObjectManager);
    this.updateRegeneration(deltaTime);
    this.updateInvulnerability(deltaTime);
    this.updateAnimation(deltaTime);
  }

  private updateMovement(deltaTime: number, inputManager: InputManager): void {
    // Get movement input
    const movement = inputManager.getMovementVector();
    
    // Apply movement
    this.velocity = movement.multiply(this.stats.speed);
    this.position = this.position.add(this.velocity.multiply(deltaTime));
    
    // Update movement state
    this.isMoving = !movement.isZero();
    
    // Update facing direction
    if (this.isMoving) {
      this.facingDirection = movement.normalize();
    }
    
    // Keep player in bounds (assuming 1024x768 game area)
    const halfSize = this.config.collisionRadius;
    this.position.x = Math.max(halfSize, Math.min(1024 - halfSize, this.position.x));
    this.position.y = Math.max(halfSize, Math.min(768 - halfSize, this.position.y));
  }

  private updateAiming(inputManager: InputManager): void {
    const mousePos = inputManager.getMousePosition();
    
    // Calculate aim direction from player to mouse
    this.aimDirection = new Vector2(
      mousePos.x - this.position.x,
      mousePos.y - this.position.y
    ).normalize();
    
    // If aim direction is valid, update facing direction
    if (!this.aimDirection.isZero()) {
      this.facingDirection = this.aimDirection.copy();
    }
  }

  private updateCombat(deltaTime: number, inputManager: InputManager, gameObjectManager: any): void {
    // Update attack cooldown
    this.attackCooldown = Math.max(0, this.attackCooldown - deltaTime);
    
    // Toggle auto-attack
    if (inputManager.isActionPressed('attack') || inputManager.isKeyPressed('Space')) {
      this.autoAttack = !this.autoAttack;
    }
    
    // Check for attack input
    const shouldAttack = inputManager.isMouseButtonDown(0) || this.autoAttack;
    
    if (shouldAttack && this.attackCooldown <= 0) {
      this.performAttack(gameObjectManager);
    }
  }

  private performAttack(gameObjectManager: any): void {
    if (!gameObjectManager) return;
    
    // Calculate attack rate based on weapon (default: 5 attacks per second)
    const attackRate = 5;
    this.attackCooldown = 1 / attackRate;
    
    // Create bullet
    const bulletSpeed = 600;
    const bulletConfig = {
      damage: this.stats.damage,
      speed: bulletSpeed,
      size: 8,
      color: '#4CAF50',
      lifetime: 3,
      piercing: false,
      homing: false
    };
    
    // Spawn bullet slightly in front of player
    const spawnOffset = this.aimDirection.multiply(this.config.size / 2 + 5);
    const spawnPos = this.position.add(spawnOffset);
    
    const bullet = gameObjectManager.createBullet(
      spawnPos.x,
      spawnPos.y,
      this.aimDirection,
      bulletConfig,
      true // isPlayerBullet
    );
    
    if (bullet) {
      // Create muzzle flash effect
      gameObjectManager.createMuzzleFlash(spawnPos.x, spawnPos.y, this.aimDirection);
      
      // Consume mana if using magic weapon
      // this.stats.mana = Math.max(0, this.stats.mana - manaCost);
    }
    
    this.lastAttackTime = performance.now();
  }

  private updateRegeneration(deltaTime: number): void {
    // Health regeneration
    if (this.stats.health < this.stats.maxHealth) {
      this.stats.health = Math.min(
        this.stats.maxHealth,
        this.stats.health + this.config.healthRegenRate * deltaTime
      );
    }
    
    // Mana regeneration
    if (this.stats.mana < this.stats.maxMana) {
      this.stats.mana = Math.min(
        this.stats.maxMana,
        this.stats.mana + this.config.manaRegenRate * deltaTime
      );
    }
  }

  private updateInvulnerability(deltaTime: number): void {
    if (this.isInvulnerable) {
      this.invulnerabilityTimer -= deltaTime;
      if (this.invulnerabilityTimer <= 0) {
        this.isInvulnerable = false;
      }
    }
  }

  private updateAnimation(deltaTime: number): void {
    this.animationTime += deltaTime;
  }

  takeDamage(damage: number): boolean {
    if (!this.isAlive || this.isInvulnerable) return false;
    
    // Apply defense
    const actualDamage = Math.max(1, damage - this.stats.defense);
    this.stats.health -= actualDamage;
    
    // Start invulnerability period
    this.isInvulnerable = true;
    this.invulnerabilityTimer = this.config.invulnerabilityTime;
    
    // Check if player died
    if (this.stats.health <= 0) {
      this.stats.health = 0;
      this.isAlive = false;
    }
    
    return true;
  }

  heal(amount: number): void {
    this.stats.health = Math.min(this.stats.maxHealth, this.stats.health + amount);
  }

  restoreMana(amount: number): void {
    this.stats.mana = Math.min(this.stats.maxMana, this.stats.mana + amount);
  }

  gainExperience(amount: number): boolean {
    this.stats.experience += amount;
    
    // Check for level up (simple formula: level * 100 XP per level)
    const xpForNextLevel = this.stats.level * 100;
    if (this.stats.experience >= xpForNextLevel) {
      this.levelUp();
      return true;
    }
    
    return false;
  }

  private levelUp(): void {
    this.stats.level++;
    this.stats.experience = 0; // Reset XP for next level
    
    // Increase stats
    this.stats.maxHealth += 20;
    this.stats.maxMana += 10;
    this.stats.damage += 5;
    this.stats.defense += 2;
    this.stats.speed += 10;
    
    // Restore health and mana on level up
    this.stats.health = this.stats.maxHealth;
    this.stats.mana = this.stats.maxMana;
    
    console.log(`🎉 Level up! Now level ${this.stats.level}`);
  }

  render(renderer: CanvasRenderer): void {
    if (!this.isAlive) return;
    
    // Calculate alpha for invulnerability flashing
    let alpha = 1;
    if (this.isInvulnerable) {
      alpha = 0.5 + 0.5 * Math.sin(this.animationTime * 20);
    }
    
    renderer.setGlobalAlpha(alpha);
    
    // Draw player body
    const size = this.config.size;
    renderer.drawRect(
      this.position.x - size / 2,
      this.position.y - size / 2,
      size,
      size,
      '#4CAF50'
    );
    
    // Draw facing direction indicator
    const indicatorLength = size / 2 + 10;
    const indicatorEnd = this.position.add(this.facingDirection.multiply(indicatorLength));
    renderer.drawLine(
      this.position.x,
      this.position.y,
      indicatorEnd.x,
      indicatorEnd.y,
      '#ffffff',
      3
    );
    
    // Draw health bar
    this.renderHealthBar(renderer);
    
    // Draw mana bar
    this.renderManaBar(renderer);
    
    renderer.setGlobalAlpha(1);
  }

  private renderHealthBar(renderer: CanvasRenderer): void {
    const barWidth = this.config.size + 10;
    const barHeight = 4;
    const barX = this.position.x - barWidth / 2;
    const barY = this.position.y - this.config.size / 2 - 15;
    
    // Background
    renderer.drawRect(barX, barY, barWidth, barHeight, '#333333');
    
    // Health fill
    const healthPercent = this.stats.health / this.stats.maxHealth;
    let healthColor = '#4CAF50';
    if (healthPercent < 0.3) healthColor = '#f44336';
    else if (healthPercent < 0.6) healthColor = '#ff9800';
    
    renderer.drawRect(barX, barY, barWidth * healthPercent, barHeight, healthColor);
  }

  private renderManaBar(renderer: CanvasRenderer): void {
    const barWidth = this.config.size + 10;
    const barHeight = 3;
    const barX = this.position.x - barWidth / 2;
    const barY = this.position.y - this.config.size / 2 - 10;
    
    // Background
    renderer.drawRect(barX, barY, barWidth, barHeight, '#222222');
    
    // Mana fill
    const manaPercent = this.stats.mana / this.stats.maxMana;
    renderer.drawRect(barX, barY, barWidth * manaPercent, barHeight, '#2196F3');
  }

  // Collision detection
  checkCollision(other: { position: Vector2; size?: number; collisionRadius?: number }): boolean {
    const otherRadius = other.collisionRadius || other.size || 16;
    const distance = this.position.distance(other.position);
    return distance < (this.config.collisionRadius + otherRadius);
  }

  // Getters
  getPosition(): Vector2 { return this.position.copy(); }
  getAimDirection(): Vector2 { return this.aimDirection.copy(); }
  getFacingDirection(): Vector2 { return this.facingDirection.copy(); }
  getHealthPercent(): number { return this.stats.health / this.stats.maxHealth; }
  getManaPercent(): number { return this.stats.mana / this.stats.maxMana; }
  getLevel(): number { return this.stats.level; }
  isAutoAttacking(): boolean { return this.autoAttack; }
}
