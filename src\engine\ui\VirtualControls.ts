/**
 * Virtual on-screen controls for mobile devices
 */

import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';
import { TouchManager, TouchGesture } from '@/engine/input/TouchManager';
import { Vector2 } from '@/utils/math/Vector2';

export interface VirtualButton {
  id: string;
  position: Vector2;
  size: Vector2;
  label: string;
  icon?: string;
  isPressed: boolean;
  isVisible: boolean;
  opacity: number;
  color: string;
  pressedColor: string;
  textColor: string;
  borderColor: string;
  borderWidth: number;
  cornerRadius: number;
}

export interface VirtualJoystick {
  id: string;
  position: Vector2;
  radius: number;
  knobRadius: number;
  knobPosition: Vector2;
  isActive: boolean;
  isVisible: boolean;
  opacity: number;
  baseColor: string;
  knobColor: string;
  borderColor: string;
  deadZone: number;
  value: Vector2;
}

export interface VirtualControlsConfig {
  showControls: boolean;
  opacity: number;
  fadeTime: number;
  autoHide: boolean;
  autoHideDelay: number;
  hapticFeedback: boolean;
  buttonSize: number;
  joystickSize: number;
}

export class VirtualControls {
  private canvas: HTMLCanvasElement;
  private touchManager: TouchManager;
  private config: VirtualControlsConfig;
  
  private buttons: Map<string, VirtualButton> = new Map();
  private joysticks: Map<string, VirtualJoystick> = new Map();
  
  private isVisible: boolean = false;
  private fadeTimer: number = 0;
  private autoHideTimer: number = 0;
  
  // Event callbacks
  private buttonCallbacks: Map<string, (pressed: boolean) => void> = new Map();
  private joystickCallbacks: Map<string, (value: Vector2) => void> = new Map();

  constructor(canvas: HTMLCanvasElement, touchManager: TouchManager, config: Partial<VirtualControlsConfig> = {}) {
    this.canvas = canvas;
    this.touchManager = touchManager;
    this.config = {
      showControls: true,
      opacity: 0.7,
      fadeTime: 0.3,
      autoHide: false,
      autoHideDelay: 3000,
      hapticFeedback: true,
      buttonSize: 60,
      joystickSize: 100,
      ...config
    };
    
    this.setupTouchListeners();
    this.createDefaultControls();
  }

  private setupTouchListeners(): void {
    this.touchManager.addGestureListener('tap', this.handleTap.bind(this));
    this.touchManager.addGestureListener('touchstart', this.handleTouchStart.bind(this));
    this.touchManager.addGestureListener('touchmove', this.handleTouchMove.bind(this));
    this.touchManager.addGestureListener('touchend', this.handleTouchEnd.bind(this));
  }

  private createDefaultControls(): void {
    // Movement joystick (left side)
    this.addJoystick('movement', {
      position: new Vector2(80, this.canvas.height - 80),
      radius: this.config.joystickSize / 2,
      knobRadius: 20,
      deadZone: 0.1
    });
    
    // Action buttons (right side)
    const buttonY = this.canvas.height - 80;
    const buttonSpacing = this.config.buttonSize + 10;
    
    this.addButton('attack', {
      position: new Vector2(this.canvas.width - 80, buttonY),
      size: new Vector2(this.config.buttonSize, this.config.buttonSize),
      label: 'ATK',
      icon: '⚔️'
    });
    
    this.addButton('special', {
      position: new Vector2(this.canvas.width - 80 - buttonSpacing, buttonY),
      size: new Vector2(this.config.buttonSize, this.config.buttonSize),
      label: 'SPC',
      icon: '✨'
    });
    
    this.addButton('jump', {
      position: new Vector2(this.canvas.width - 80, buttonY - buttonSpacing),
      size: new Vector2(this.config.buttonSize, this.config.buttonSize),
      label: 'JMP',
      icon: '⬆️'
    });
    
    // Menu buttons (top)
    this.addButton('pause', {
      position: new Vector2(this.canvas.width - 50, 30),
      size: new Vector2(40, 40),
      label: '⏸️',
      icon: '⏸️'
    });
    
    this.addButton('inventory', {
      position: new Vector2(this.canvas.width - 100, 30),
      size: new Vector2(40, 40),
      label: '🎒',
      icon: '🎒'
    });
  }

  // Button management
  addButton(id: string, config: Partial<VirtualButton>): void {
    const button: VirtualButton = {
      id,
      position: new Vector2(0, 0),
      size: new Vector2(60, 60),
      label: '',
      icon: '',
      isPressed: false,
      isVisible: true,
      opacity: this.config.opacity,
      color: 'rgba(255, 255, 255, 0.2)',
      pressedColor: 'rgba(255, 255, 255, 0.4)',
      textColor: '#ffffff',
      borderColor: 'rgba(255, 255, 255, 0.5)',
      borderWidth: 2,
      cornerRadius: 10,
      ...config
    };
    
    this.buttons.set(id, button);
  }

  removeButton(id: string): void {
    this.buttons.delete(id);
    this.buttonCallbacks.delete(id);
  }

  // Joystick management
  addJoystick(id: string, config: Partial<VirtualJoystick>): void {
    const joystick: VirtualJoystick = {
      id,
      position: new Vector2(0, 0),
      radius: 50,
      knobRadius: 20,
      knobPosition: new Vector2(0, 0),
      isActive: false,
      isVisible: true,
      opacity: this.config.opacity,
      baseColor: 'rgba(255, 255, 255, 0.2)',
      knobColor: 'rgba(255, 255, 255, 0.6)',
      borderColor: 'rgba(255, 255, 255, 0.5)',
      deadZone: 0.1,
      value: new Vector2(0, 0),
      ...config
    };
    
    joystick.knobPosition = joystick.position.copy();
    this.joysticks.set(id, joystick);
  }

  removeJoystick(id: string): void {
    this.joysticks.delete(id);
    this.joystickCallbacks.delete(id);
  }

  // Event handling
  private handleTap(gesture: TouchGesture): void {
    this.resetAutoHideTimer();
    
    // Check button taps
    for (const button of this.buttons.values()) {
      if (this.isPointInButton(gesture.position, button)) {
        this.triggerButton(button.id, true);
        
        // Brief press animation
        setTimeout(() => {
          this.triggerButton(button.id, false);
        }, 100);
        
        break;
      }
    }
  }

  private handleTouchStart(gesture: TouchGesture): void {
    this.resetAutoHideTimer();
    
    // Check button presses
    for (const button of this.buttons.values()) {
      if (this.isPointInButton(gesture.position, button)) {
        this.triggerButton(button.id, true);
        return;
      }
    }
    
    // Check joystick activation
    for (const joystick of this.joysticks.values()) {
      if (this.isPointInJoystick(gesture.position, joystick)) {
        joystick.isActive = true;
        this.updateJoystick(joystick, gesture.position);
        return;
      }
    }
  }

  private handleTouchMove(gesture: TouchGesture): void {
    // Update active joysticks
    for (const joystick of this.joysticks.values()) {
      if (joystick.isActive) {
        this.updateJoystick(joystick, gesture.position);
      }
    }
  }

  private handleTouchEnd(gesture: TouchGesture): void {
    // Release all buttons
    for (const button of this.buttons.values()) {
      if (button.isPressed) {
        this.triggerButton(button.id, false);
      }
    }
    
    // Deactivate joysticks
    for (const joystick of this.joysticks.values()) {
      if (joystick.isActive) {
        joystick.isActive = false;
        joystick.knobPosition = joystick.position.copy();
        joystick.value = new Vector2(0, 0);
        this.triggerJoystick(joystick.id, joystick.value);
      }
    }
  }

  private isPointInButton(point: Vector2, button: VirtualButton): boolean {
    return point.x >= button.position.x - button.size.x / 2 &&
           point.x <= button.position.x + button.size.x / 2 &&
           point.y >= button.position.y - button.size.y / 2 &&
           point.y <= button.position.y + button.size.y / 2;
  }

  private isPointInJoystick(point: Vector2, joystick: VirtualJoystick): boolean {
    return point.distance(joystick.position) <= joystick.radius;
  }

  private updateJoystick(joystick: VirtualJoystick, touchPosition: Vector2): void {
    const delta = touchPosition.subtract(joystick.position);
    const distance = delta.magnitude();
    
    if (distance <= joystick.radius) {
      joystick.knobPosition = touchPosition.copy();
    } else {
      const direction = delta.normalize();
      joystick.knobPosition = joystick.position.add(direction.multiply(joystick.radius));
    }
    
    // Calculate normalized value
    const knobDelta = joystick.knobPosition.subtract(joystick.position);
    const normalizedDistance = Math.min(knobDelta.magnitude() / joystick.radius, 1);
    
    if (normalizedDistance > joystick.deadZone) {
      const adjustedDistance = (normalizedDistance - joystick.deadZone) / (1 - joystick.deadZone);
      joystick.value = knobDelta.normalize().multiply(adjustedDistance);
    } else {
      joystick.value = new Vector2(0, 0);
    }
    
    this.triggerJoystick(joystick.id, joystick.value);
  }

  private triggerButton(buttonId: string, pressed: boolean): void {
    const button = this.buttons.get(buttonId);
    if (!button) return;
    
    button.isPressed = pressed;
    
    // Haptic feedback
    if (pressed && this.config.hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(50);
    }
    
    // Trigger callback
    const callback = this.buttonCallbacks.get(buttonId);
    if (callback) {
      callback(pressed);
    }
  }

  private triggerJoystick(joystickId: string, value: Vector2): void {
    const callback = this.joystickCallbacks.get(joystickId);
    if (callback) {
      callback(value);
    }
  }

  // Event listeners
  onButtonPress(buttonId: string, callback: (pressed: boolean) => void): void {
    this.buttonCallbacks.set(buttonId, callback);
  }

  onJoystickMove(joystickId: string, callback: (value: Vector2) => void): void {
    this.joystickCallbacks.set(joystickId, callback);
  }

  // Rendering
  render(renderer: CanvasRenderer): void {
    if (!this.config.showControls || !this.isVisible) return;
    
    const alpha = this.config.opacity * (this.fadeTimer / this.config.fadeTime);
    renderer.setGlobalAlpha(alpha);
    
    // Render joysticks
    for (const joystick of this.joysticks.values()) {
      if (joystick.isVisible) {
        this.renderJoystick(renderer, joystick);
      }
    }
    
    // Render buttons
    for (const button of this.buttons.values()) {
      if (button.isVisible) {
        this.renderButton(renderer, button);
      }
    }
    
    renderer.setGlobalAlpha(1);
  }

  private renderButton(renderer: CanvasRenderer, button: VirtualButton): void {
    const color = button.isPressed ? button.pressedColor : button.color;
    
    // Draw button background
    renderer.drawRoundedRect(
      button.position.x - button.size.x / 2,
      button.position.y - button.size.y / 2,
      button.size.x,
      button.size.y,
      button.cornerRadius,
      color
    );
    
    // Draw border
    renderer.drawRoundedRectOutline(
      button.position.x - button.size.x / 2,
      button.position.y - button.size.y / 2,
      button.size.x,
      button.size.y,
      button.cornerRadius,
      button.borderColor,
      button.borderWidth
    );
    
    // Draw label/icon
    const text = button.icon || button.label;
    if (text) {
      const fontSize = Math.min(button.size.x, button.size.y) * 0.4;
      renderer.drawText(
        text,
        button.position.x,
        button.position.y,
        `${fontSize}px Arial`,
        button.textColor,
        'center',
        'middle'
      );
    }
  }

  private renderJoystick(renderer: CanvasRenderer, joystick: VirtualJoystick): void {
    // Draw base
    renderer.drawCircle(
      joystick.position.x,
      joystick.position.y,
      joystick.radius,
      joystick.baseColor
    );
    
    // Draw base border
    renderer.drawCircleOutline(
      joystick.position.x,
      joystick.position.y,
      joystick.radius,
      joystick.borderColor,
      2
    );
    
    // Draw knob
    renderer.drawCircle(
      joystick.knobPosition.x,
      joystick.knobPosition.y,
      joystick.knobRadius,
      joystick.knobColor
    );
    
    // Draw knob border
    renderer.drawCircleOutline(
      joystick.knobPosition.x,
      joystick.knobPosition.y,
      joystick.knobRadius,
      joystick.borderColor,
      2
    );
  }

  // Visibility and animation
  show(): void {
    this.isVisible = true;
    this.fadeTimer = this.config.fadeTime;
    this.resetAutoHideTimer();
  }

  hide(): void {
    this.isVisible = false;
    this.fadeTimer = 0;
  }

  toggle(): void {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }

  private resetAutoHideTimer(): void {
    if (this.config.autoHide) {
      this.autoHideTimer = this.config.autoHideDelay;
    }
  }

  // Update method
  update(deltaTime: number): void {
    // Handle fade animation
    if (this.isVisible && this.fadeTimer < this.config.fadeTime) {
      this.fadeTimer = Math.min(this.fadeTimer + deltaTime, this.config.fadeTime);
    } else if (!this.isVisible && this.fadeTimer > 0) {
      this.fadeTimer = Math.max(this.fadeTimer - deltaTime, 0);
    }
    
    // Handle auto-hide
    if (this.config.autoHide && this.isVisible && this.autoHideTimer > 0) {
      this.autoHideTimer -= deltaTime * 1000;
      if (this.autoHideTimer <= 0) {
        this.hide();
      }
    }
  }

  // Configuration
  updateConfig(config: Partial<VirtualControlsConfig>): void {
    this.config = { ...this.config, ...config };
  }

  getConfig(): VirtualControlsConfig {
    return { ...this.config };
  }

  // Utility methods
  getButtonValue(buttonId: string): boolean {
    const button = this.buttons.get(buttonId);
    return button ? button.isPressed : false;
  }

  getJoystickValue(joystickId: string): Vector2 {
    const joystick = this.joysticks.get(joystickId);
    return joystick ? joystick.value.copy() : new Vector2(0, 0);
  }

  setButtonVisibility(buttonId: string, visible: boolean): void {
    const button = this.buttons.get(buttonId);
    if (button) {
      button.isVisible = visible;
    }
  }

  setJoystickVisibility(joystickId: string, visible: boolean): void {
    const joystick = this.joysticks.get(joystickId);
    if (joystick) {
      joystick.isVisible = visible;
    }
  }

  // Cleanup
  destroy(): void {
    this.buttons.clear();
    this.joysticks.clear();
    this.buttonCallbacks.clear();
    this.joystickCallbacks.clear();
  }
}
