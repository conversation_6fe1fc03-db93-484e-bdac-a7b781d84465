/**
 * Game object management system with object pooling integration
 */

import { ObjectPool, PoolManager } from '@/engine/core/ObjectPool';
import { Bullet } from '@/game/entities/Bullet';
import { Particle } from '@/game/entities/Particle';
import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';

export class GameObjectManager {
  private poolManager: PoolManager;
  private bulletPool: ObjectPool<Bullet>;
  private particlePool: ObjectPool<Particle>;
  
  // Active object lists for efficient iteration
  private activeBullets: Bullet[] = [];
  private activeParticles: Particle[] = [];
  
  // Performance tracking
  private stats = {
    bulletsCreated: 0,
    bulletsDestroyed: 0,
    particlesCreated: 0,
    particlesDestroyed: 0,
    poolHits: 0,
    poolMisses: 0
  };

  constructor() {
    this.poolManager = new PoolManager();
    this.initializePools();
  }

  private initializePools(): void {
    // Initialize bullet pool
    this.bulletPool = this.poolManager.registerPool(
      'bullets',
      () => new Bullet(),
      50,  // Initial size
      200, // Max size
      (bullet: Bullet) => {
        // Custom reset function if needed
      }
    );

    // Initialize particle pool
    this.particlePool = this.poolManager.registerPool(
      'particles',
      () => new Particle(),
      100, // Initial size
      500, // Max size
      (particle: Particle) => {
        // Custom reset function if needed
      }
    );

    console.log('🎯 Object pools initialized');
  }

  // Bullet management
  createBullet(
    x: number,
    y: number,
    direction: { x: number; y: number },
    config: any,
    isPlayerBullet: boolean = false
  ): Bullet {
    const bullet = this.bulletPool.acquire();

    // Object pool now guarantees a return value (graceful degradation)
    bullet.initialize(x, y, direction, config, isPlayerBullet);
    this.activeBullets.push(bullet);
    this.stats.bulletsCreated++;
    this.stats.poolHits++;
    return bullet;
  }

  // Particle management
  createParticle(
    x: number,
    y: number,
    velocity: { x: number; y: number },
    config: any
  ): Particle {
    const particle = this.particlePool.acquire();

    // Object pool now guarantees a return value (graceful degradation)
    particle.initialize(x, y, velocity, config);
    this.activeParticles.push(particle);
    this.stats.particlesCreated++;
    this.stats.poolHits++;
    return particle;
  }

  // Particle effect helpers
  createExplosion(x: number, y: number, intensity: number = 1): void {
    const particleCount = Math.floor(10 * intensity);
    
    for (let i = 0; i < particleCount; i++) {
      const angle = (Math.PI * 2 * i) / particleCount;
      const speed = 50 + Math.random() * 100 * intensity;
      const velocity = {
        x: Math.cos(angle) * speed,
        y: Math.sin(angle) * speed
      };
      
      this.createParticle(x, y, velocity, {
        lifetime: 0.5 + Math.random() * 0.5,
        startSize: 4 + Math.random() * 4 * intensity,
        endSize: 0,
        startColor: '#ff6b6b',
        endColor: '#ffaa00',
        startAlpha: 1,
        endAlpha: 0,
        gravity: { x: 0, y: 50 },
        drag: 2,
        shape: 'circle'
      });
    }
  }

  createMuzzleFlash(x: number, y: number, direction: { x: number; y: number }): void {
    for (let i = 0; i < 5; i++) {
      const spread = 0.5; // Radians
      const angle = Math.atan2(direction.y, direction.x) + (Math.random() - 0.5) * spread;
      const speed = 100 + Math.random() * 50;
      
      const velocity = {
        x: Math.cos(angle) * speed,
        y: Math.sin(angle) * speed
      };
      
      this.createParticle(x, y, velocity, {
        lifetime: 0.1 + Math.random() * 0.1,
        startSize: 2 + Math.random() * 2,
        endSize: 0,
        startColor: '#ffff00',
        endColor: '#ff8800',
        startAlpha: 0.8,
        endAlpha: 0,
        drag: 5,
        shape: 'circle'
      });
    }
  }

  createBloodSplatter(x: number, y: number, direction: { x: number; y: number }): void {
    for (let i = 0; i < 8; i++) {
      const spread = Math.PI; // Half circle spread
      const angle = Math.atan2(direction.y, direction.x) + (Math.random() - 0.5) * spread;
      const speed = 30 + Math.random() * 70;
      
      const velocity = {
        x: Math.cos(angle) * speed,
        y: Math.sin(angle) * speed
      };
      
      this.createParticle(x, y, velocity, {
        lifetime: 0.8 + Math.random() * 0.4,
        startSize: 2 + Math.random() * 3,
        endSize: 1,
        startColor: '#cc0000',
        endColor: '#660000',
        startAlpha: 0.9,
        endAlpha: 0.3,
        gravity: { x: 0, y: 100 },
        drag: 1,
        shape: 'circle'
      });
    }
  }

  // Update all game objects
  update(deltaTime: number): void {
    this.updateBullets(deltaTime);
    this.updateParticles(deltaTime);
    this.cleanupInactiveObjects();
  }

  private updateBullets(deltaTime: number): void {
    for (const bullet of this.activeBullets) {
      if (bullet.isActive()) {
        bullet.update(deltaTime);
      }
    }
  }

  private updateParticles(deltaTime: number): void {
    for (const particle of this.activeParticles) {
      if (particle.isActive()) {
        particle.update(deltaTime);
      }
    }
  }

  private cleanupInactiveObjects(): void {
    // Clean up bullets
    for (let i = this.activeBullets.length - 1; i >= 0; i--) {
      const bullet = this.activeBullets[i];
      if (!bullet.isActive()) {
        this.bulletPool.release(bullet);
        this.activeBullets.splice(i, 1);
        this.stats.bulletsDestroyed++;
      }
    }

    // Clean up particles
    for (let i = this.activeParticles.length - 1; i >= 0; i--) {
      const particle = this.activeParticles[i];
      if (!particle.isActive()) {
        this.particlePool.release(particle);
        this.activeParticles.splice(i, 1);
        this.stats.particlesDestroyed++;
      }
    }
  }

  // Render all game objects
  render(renderer: CanvasRenderer): void {
    // Render particles first (behind other objects)
    for (const particle of this.activeParticles) {
      if (particle.isActive()) {
        particle.render(renderer);
      }
    }

    // Render bullets
    for (const bullet of this.activeBullets) {
      if (bullet.isActive()) {
        bullet.render(renderer);
      }
    }
  }

  // Collision detection helpers
  getBulletsInRadius(center: { x: number; y: number }, radius: number, playerBullets: boolean): Bullet[] {
    const result: Bullet[] = [];
    
    for (const bullet of this.activeBullets) {
      if (!bullet.isActive() || bullet.isPlayerBullet !== playerBullets) continue;
      
      const distance = Math.sqrt(
        Math.pow(bullet.getPosition().x - center.x, 2) +
        Math.pow(bullet.getPosition().y - center.y, 2)
      );
      
      if (distance <= radius) {
        result.push(bullet);
      }
    }
    
    return result;
  }

  // Getters for active objects
  getActiveBullets(): Bullet[] {
    return this.activeBullets.filter(bullet => bullet.isActive());
  }

  getActiveParticles(): Particle[] {
    return this.activeParticles.filter(particle => particle.isActive());
  }

  getPlayerBullets(): Bullet[] {
    return this.activeBullets.filter(bullet => bullet.isActive() && bullet.isPlayerBullet);
  }

  getEnemyBullets(): Bullet[] {
    return this.activeBullets.filter(bullet => bullet.isActive() && !bullet.isPlayerBullet);
  }

  // Performance and debugging
  getStats(): any {
    const poolStats = this.poolManager.getStats();
    
    return {
      ...this.stats,
      activeBullets: this.activeBullets.length,
      activeParticles: this.activeParticles.length,
      poolStats: Object.fromEntries(poolStats),
      poolEfficiency: this.stats.poolHits / (this.stats.poolHits + this.stats.poolMisses) * 100
    };
  }

  clearAll(): void {
    // Return all objects to pools
    for (const bullet of this.activeBullets) {
      this.bulletPool.release(bullet);
    }
    for (const particle of this.activeParticles) {
      this.particlePool.release(particle);
    }
    
    this.activeBullets = [];
    this.activeParticles = [];
  }

  destroy(): void {
    this.clearAll();
    this.poolManager.destroy();
  }
}
