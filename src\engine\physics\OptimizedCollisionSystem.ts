/**
 * Optimized collision detection system with improved performance
 * Uses spatial partitioning and efficient algorithms to reduce computational complexity
 */

import { Vector2 } from '@/utils/math/Vector2';
import { CollisionResult, Collider } from './CollisionSystem';

// Reusable objects to avoid allocations
const tempVector1 = new Vector2();
const tempVector2 = new Vector2();
const tempVector3 = new Vector2();

export interface SpatialObject {
  position: Vector2;
  collisionRadius: number;
  id: string;
  bounds?: { x: number; y: number; width: number; height: number };
}

export interface CollisionPair<T extends SpatialObject> {
  objectA: T;
  objectB: T;
  distance: number;
}

/**
 * Optimized spatial grid for broad-phase collision detection
 */
export class OptimizedSpatialGrid<T extends SpatialObject> {
  private grid: Map<string, T[]> = new Map();
  private cellSize: number;
  private objectCells: Map<string, string[]> = new Map();

  constructor(cellSize: number = 64) {
    this.cellSize = cellSize;
  }

  /**
   * Clear the grid for the next frame
   */
  clear(): void {
    this.grid.clear();
    this.objectCells.clear();
  }

  /**
   * Add object to the grid
   */
  addObject(obj: T): void {
    const cells = this.getObjectCells(obj);
    this.objectCells.set(obj.id, cells);

    for (const cellKey of cells) {
      if (!this.grid.has(cellKey)) {
        this.grid.set(cellKey, []);
      }
      this.grid.get(cellKey)!.push(obj);
    }
  }

  /**
   * Get all cells that an object occupies
   */
  private getObjectCells(obj: T): string[] {
    const cells: string[] = [];
    const radius = obj.collisionRadius;
    
    const minX = Math.floor((obj.position.x - radius) / this.cellSize);
    const maxX = Math.floor((obj.position.x + radius) / this.cellSize);
    const minY = Math.floor((obj.position.y - radius) / this.cellSize);
    const maxY = Math.floor((obj.position.y + radius) / this.cellSize);

    for (let x = minX; x <= maxX; x++) {
      for (let y = minY; y <= maxY; y++) {
        cells.push(`${x},${y}`);
      }
    }

    return cells;
  }

  /**
   * Get potential collision pairs using spatial partitioning
   */
  getPotentialPairs(): CollisionPair<T>[] {
    const pairs: CollisionPair<T>[] = [];
    const checkedPairs = new Set<string>();

    for (const cell of this.grid.values()) {
      if (cell.length < 2) continue;

      for (let i = 0; i < cell.length; i++) {
        for (let j = i + 1; j < cell.length; j++) {
          const objA = cell[i];
          const objB = cell[j];
          
          // Create unique pair key
          const pairKey = objA.id < objB.id ? 
            `${objA.id}:${objB.id}` : 
            `${objB.id}:${objA.id}`;

          if (checkedPairs.has(pairKey)) continue;
          checkedPairs.add(pairKey);

          // Quick distance check
          const distance = objA.position.distance(objB.position);
          const minDistance = objA.collisionRadius + objB.collisionRadius;

          if (distance <= minDistance * 1.1) { // Small buffer for potential collisions
            pairs.push({ objectA: objA, objectB: objB, distance });
          }
        }
      }
    }

    return pairs;
  }

  /**
   * Get objects in a specific region
   */
  getObjectsInRegion(x: number, y: number, width: number, height: number): T[] {
    const objects = new Set<T>();
    
    const minCellX = Math.floor(x / this.cellSize);
    const maxCellX = Math.floor((x + width) / this.cellSize);
    const minCellY = Math.floor(y / this.cellSize);
    const maxCellY = Math.floor((y + height) / this.cellSize);

    for (let cellX = minCellX; cellX <= maxCellX; cellX++) {
      for (let cellY = minCellY; cellY <= maxCellY; cellY++) {
        const cellKey = `${cellX},${cellY}`;
        const cell = this.grid.get(cellKey);
        if (cell) {
          cell.forEach(obj => objects.add(obj));
        }
      }
    }

    return Array.from(objects);
  }
}

/**
 * High-performance collision detection system
 */
export class OptimizedCollisionSystem {
  private spatialGrid: OptimizedSpatialGrid<SpatialObject>;
  private collisionChecks: number = 0;
  private maxCollisionChecks: number = 1000; // Performance budget

  constructor(cellSize: number = 64) {
    this.spatialGrid = new OptimizedSpatialGrid(cellSize);
  }

  /**
   * Reset collision check counter
   */
  resetCollisionChecks(): void {
    this.collisionChecks = 0;
  }

  /**
   * Get number of collision checks performed this frame
   */
  getCollisionChecks(): number {
    return this.collisionChecks;
  }

  /**
   * Check collisions between two groups of objects
   */
  checkCollisions<T extends SpatialObject, U extends SpatialObject>(
    groupA: T[],
    groupB: U[],
    onCollision: (objA: T, objB: U) => void,
    maxChecks?: number
  ): void {
    this.spatialGrid.clear();

    // Add all objects to spatial grid
    [...groupA, ...groupB].forEach(obj => {
      this.spatialGrid.addObject(obj);
    });

    const pairs = this.spatialGrid.getPotentialPairs();
    const checksLimit = maxChecks || this.maxCollisionChecks;
    let checksPerformed = 0;

    for (const pair of pairs) {
      if (checksPerformed >= checksLimit) {
        console.warn(`Collision check limit reached: ${checksLimit}`);
        break;
      }

      // Check if objects are from different groups
      const isAFromGroupA = groupA.some(obj => obj.id === pair.objectA.id);
      const isBFromGroupB = groupB.some(obj => obj.id === pair.objectB.id);
      const isAFromGroupB = groupB.some(obj => obj.id === pair.objectA.id);
      const isBFromGroupA = groupA.some(obj => obj.id === pair.objectB.id);

      if ((isAFromGroupA && isBFromGroupB) || (isAFromGroupB && isBFromGroupA)) {
        if (this.fastCollisionCheck(pair.objectA, pair.objectB)) {
          if (isAFromGroupA && isBFromGroupB) {
            onCollision(pair.objectA as T, pair.objectB as U);
          } else {
            onCollision(pair.objectB as T, pair.objectA as U);
          }
        }
        checksPerformed++;
      }
    }

    this.collisionChecks += checksPerformed;
  }

  /**
   * Fast collision check using circle-circle collision
   */
  private fastCollisionCheck(objA: SpatialObject, objB: SpatialObject): boolean {
    // Use temp vectors to avoid allocations
    tempVector1.set(objB.position.x - objA.position.x, objB.position.y - objA.position.y);
    const distanceSquared = tempVector1.x * tempVector1.x + tempVector1.y * tempVector1.y;
    const minDistance = objA.collisionRadius + objB.collisionRadius;
    
    return distanceSquared <= minDistance * minDistance;
  }

  /**
   * Precise collision check with detailed result
   */
  preciseCollisionCheck(objA: SpatialObject, objB: SpatialObject): CollisionResult {
    // Use temp vectors for calculations
    tempVector1.set(objB.position.x - objA.position.x, objB.position.y - objA.position.y);
    const distance = tempVector1.magnitude();
    const minDistance = objA.collisionRadius + objB.collisionRadius;

    if (distance <= minDistance) {
      const overlap = minDistance - distance;
      
      // Calculate normal (reuse tempVector1)
      if (distance > 0) {
        tempVector1.normalize();
      } else {
        tempVector1.set(1, 0); // Default normal if objects are at same position
      }

      // Calculate collision point
      tempVector2.set(objA.position.x, objA.position.y)
        .add(tempVector1.copy().multiply(objA.collisionRadius));

      return {
        hasCollision: true,
        distance,
        overlap,
        normal: tempVector1.copy(),
        point: tempVector2.copy()
      };
    }

    return { hasCollision: false };
  }

  /**
   * Query objects in a circular area
   */
  queryCircle(center: Vector2, radius: number): SpatialObject[] {
    const regionObjects = this.spatialGrid.getObjectsInRegion(
      center.x - radius,
      center.y - radius,
      radius * 2,
      radius * 2
    );

    return regionObjects.filter(obj => {
      const distance = center.distance(obj.position);
      return distance <= radius + obj.collisionRadius;
    });
  }

  /**
   * Query objects in a rectangular area
   */
  queryRectangle(x: number, y: number, width: number, height: number): SpatialObject[] {
    return this.spatialGrid.getObjectsInRegion(x, y, width, height).filter(obj => {
      // Check if object's circle intersects with rectangle
      const closestX = Math.max(x, Math.min(obj.position.x, x + width));
      const closestY = Math.max(y, Math.min(obj.position.y, y + height));
      
      tempVector1.set(obj.position.x - closestX, obj.position.y - closestY);
      const distanceSquared = tempVector1.x * tempVector1.x + tempVector1.y * tempVector1.y;
      
      return distanceSquared <= obj.collisionRadius * obj.collisionRadius;
    });
  }

  /**
   * Set performance budget for collision checks per frame
   */
  setCollisionBudget(maxChecks: number): void {
    this.maxCollisionChecks = maxChecks;
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats(): { checksPerformed: number; maxChecks: number; efficiency: number } {
    return {
      checksPerformed: this.collisionChecks,
      maxChecks: this.maxCollisionChecks,
      efficiency: this.maxCollisionChecks > 0 ? this.collisionChecks / this.maxCollisionChecks : 0
    };
  }
}
