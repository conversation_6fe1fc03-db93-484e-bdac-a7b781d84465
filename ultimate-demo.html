<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Ultimate Bullet Hell RPG - Complete Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
            touch-action: none;
            user-select: none;
            -webkit-user-select: none;
            -webkit-touch-callout: none;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%);
        }

        #gameCanvas {
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            box-shadow: 0 0 30px rgba(0, 150, 255, 0.3);
            background: #000;
            cursor: crosshair;
        }

        #loadingScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            transition: opacity 0.5s ease-out;
        }

        #loadingScreen.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loading-title {
            font-size: 3rem;
            font-weight: bold;
            color: #fff;
            text-shadow: 0 0 20px rgba(0, 150, 255, 0.8);
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
        }

        .loading-subtitle {
            font-size: 1.2rem;
            color: #aaa;
            margin-bottom: 3rem;
            text-align: center;
            max-width: 600px;
            line-height: 1.6;
        }

        .loading-bar {
            width: 400px;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg, #00d4ff, #0099cc);
            border-radius: 4px;
            transition: width 0.3s ease;
            box-shadow: 0 0 10px rgba(0, 150, 255, 0.5);
        }

        .loading-text {
            color: #ccc;
            font-size: 0.9rem;
            margin-top: 1rem;
        }

        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
            max-width: 800px;
        }

        .feature-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .feature-title {
            color: #fff;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .feature-desc {
            color: #aaa;
            font-size: 0.8rem;
        }

        .controls-hint {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            text-align: center;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px 20px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            z-index: 100;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @media (max-width: 768px) {
            .loading-title {
                font-size: 2rem;
            }
            
            .loading-bar {
                width: 300px;
            }
            
            .feature-list {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }
            
            .controls-hint {
                font-size: 0.8rem;
                padding: 8px 16px;
            }
        }

        /* Performance indicator */
        #performanceIndicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 200;
        }

        /* Error display */
        #errorDisplay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            max-width: 500px;
            text-align: center;
            display: none;
            z-index: 1001;
        }

        .error-title {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .error-message {
            margin-bottom: 15px;
        }

        .error-button {
            background: #fff;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }

        .error-button:hover {
            background: #f0f0f0;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas" width="1024" height="768"></canvas>
        
        <div id="loadingScreen">
            <div class="loading-title">🎮 Ultimate Bullet Hell RPG</div>
            <div class="loading-subtitle">
                Experience the complete game with all advanced systems integrated:<br>
                Object pooling, procedural generation, character progression, and more!
            </div>
            
            <div class="loading-bar">
                <div class="loading-progress" id="loadingProgress" style="width: 0%"></div>
            </div>
            <div class="loading-text" id="loadingText">Initializing systems...</div>
            
            <div class="feature-list">
                <div class="feature-item">
                    <div class="feature-icon">🎯</div>
                    <div class="feature-title">Object Pooling</div>
                    <div class="feature-desc">Zero-allocation gameplay for 60 FPS</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">⚔️</div>
                    <div class="feature-title">Combat System</div>
                    <div class="feature-desc">6 unique weapons with special effects</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🤖</div>
                    <div class="feature-title">AI & Bullet Hell</div>
                    <div class="feature-desc">4 enemy types with varied patterns</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🏰</div>
                    <div class="feature-title">Procedural Dungeons</div>
                    <div class="feature-desc">Infinite dungeon generation</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📈</div>
                    <div class="feature-title">Character Progression</div>
                    <div class="feature-desc">3 skill trees with 25+ skills</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🎒</div>
                    <div class="feature-title">Loot & Equipment</div>
                    <div class="feature-desc">Dynamic items with rarity system</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔊</div>
                    <div class="feature-title">Audio System</div>
                    <div class="feature-desc">Procedural sounds & spatial audio</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📱</div>
                    <div class="feature-title">Mobile Support</div>
                    <div class="feature-desc">Touch controls & responsive design</div>
                </div>
            </div>
        </div>
        
        <div class="controls-hint" id="controlsHint">
            🖱️ Mouse to aim • WASD to move • Space for auto-attack • 1-6 to switch weapons<br>
            📱 Touch: Virtual joystick & buttons • P to pause • I for inventory • K for skills
        </div>
        
        <div id="performanceIndicator">
            FPS: <span id="fpsCounter">60</span> | 
            Objects: <span id="objectCounter">0</span> | 
            Memory: <span id="memoryCounter">0</span>MB
        </div>
        
        <div id="errorDisplay">
            <div class="error-title">⚠️ Error Occurred</div>
            <div class="error-message" id="errorMessage"></div>
            <button class="error-button" onclick="location.reload()">Reload Game</button>
        </div>
    </div>

    <script type="module">
        // Import all necessary modules
        import { UltimateGame } from './src/game/UltimateGame.js';
        import { ErrorHandler } from './src/utils/error/ErrorHandler.js';
        import { PerformanceMonitor } from './src/engine/core/PerformanceMonitor.js';
        import { AudioSystem } from './src/game/systems/AudioSystem.js';
        import { TouchManager } from './src/engine/input/TouchManager.js';
        import { VirtualControls } from './src/engine/ui/VirtualControls.js';
        import { ResponsiveManager } from './src/engine/ui/ResponsiveManager.js';
        import { SaveSystem } from './src/game/systems/SaveSystem.js';
        import { GameStateManager } from './src/game/systems/GameStateManager.js';

        class UltimateDemo {
            constructor() {
                this.canvas = document.getElementById('gameCanvas');
                this.loadingScreen = document.getElementById('loadingScreen');
                this.loadingProgress = document.getElementById('loadingProgress');
                this.loadingText = document.getElementById('loadingText');
                this.controlsHint = document.getElementById('controlsHint');
                this.errorDisplay = document.getElementById('errorDisplay');
                
                this.game = null;
                this.errorHandler = ErrorHandler.getInstance();
                this.performanceMonitor = new PerformanceMonitor(true);
                this.audioSystem = null;
                this.touchManager = null;
                this.virtualControls = null;
                this.responsiveManager = null;
                this.gameStateManager = null;
                
                this.loadingSteps = [
                    { name: 'Initializing error handling...', duration: 200 },
                    { name: 'Setting up responsive design...', duration: 300 },
                    { name: 'Initializing audio system...', duration: 500 },
                    { name: 'Setting up touch controls...', duration: 400 },
                    { name: 'Loading game systems...', duration: 800 },
                    { name: 'Generating procedural content...', duration: 600 },
                    { name: 'Initializing object pools...', duration: 300 },
                    { name: 'Setting up save system...', duration: 200 },
                    { name: 'Finalizing setup...', duration: 400 }
                ];
                
                this.currentStep = 0;
                this.setupErrorHandling();
                this.startLoading();
            }

            setupErrorHandling() {
                this.errorHandler.addErrorListener((error) => {
                    if (error.level === 'critical') {
                        this.showError(error.message);
                    }
                });
            }

            async startLoading() {
                try {
                    for (let i = 0; i < this.loadingSteps.length; i++) {
                        const step = this.loadingSteps[i];
                        this.loadingText.textContent = step.name;
                        
                        await this.executeLoadingStep(i);
                        
                        const progress = ((i + 1) / this.loadingSteps.length) * 100;
                        this.loadingProgress.style.width = `${progress}%`;
                        
                        await this.delay(step.duration);
                    }
                    
                    await this.initializeGame();
                    this.hideLoadingScreen();
                    
                } catch (error) {
                    this.errorHandler.critical('Failed to initialize game', error);
                    this.showError('Failed to load the game. Please refresh and try again.');
                }
            }

            async executeLoadingStep(stepIndex) {
                switch (stepIndex) {
                    case 0: // Error handling
                        this.errorHandler.info('Error handling system initialized');
                        break;
                        
                    case 1: // Responsive design
                        this.responsiveManager = new ResponsiveManager(this.canvas, {
                            baseWidth: 1024,
                            baseHeight: 768,
                            maintainAspectRatio: true,
                            centerContent: true,
                            allowFullscreen: true
                        });
                        break;
                        
                    case 2: // Audio system
                        this.audioSystem = new AudioSystem();
                        await this.delay(100); // Simulate audio loading
                        break;
                        
                    case 3: // Touch controls
                        this.touchManager = new TouchManager(this.canvas);
                        this.virtualControls = new VirtualControls(this.canvas, this.touchManager, {
                            showControls: this.responsiveManager.isTouchDevice(),
                            opacity: 0.7,
                            autoHide: false
                        });
                        break;
                        
                    case 4: // Game systems
                        this.gameStateManager = new GameStateManager();
                        break;
                        
                    case 5: // Procedural content
                        // Simulate procedural generation
                        await this.delay(200);
                        break;
                        
                    case 6: // Object pools
                        // Object pools will be initialized by the game
                        break;
                        
                    case 7: // Save system
                        // Save system is part of GameStateManager
                        break;
                        
                    case 8: // Finalization
                        await this.delay(100);
                        break;
                }
            }

            async initializeGame() {
                this.loadingText.textContent = 'Starting game...';

                // Initialize the main game
                this.game = new UltimateGame(this.canvas, {
                    performanceMonitor: this.performanceMonitor,
                    audioSystem: this.audioSystem,
                    touchManager: this.touchManager,
                    virtualControls: this.virtualControls,
                    responsiveManager: this.responsiveManager,
                    gameStateManager: this.gameStateManager
                });

                await this.game.initialize();

                // Setup performance monitoring
                this.setupPerformanceDisplay();

                // Setup responsive behavior
                this.setupResponsiveBehavior();

                // Setup virtual controls for mobile
                this.setupVirtualControls();

                // Start the game loop
                this.game.start();

                this.errorHandler.info('Ultimate demo initialized successfully');
            }

            setupPerformanceDisplay() {
                const fpsCounter = document.getElementById('fpsCounter');
                const objectCounter = document.getElementById('objectCounter');
                const memoryCounter = document.getElementById('memoryCounter');
                
                setInterval(() => {
                    const metrics = this.performanceMonitor.getCurrentMetrics();
                    fpsCounter.textContent = Math.round(metrics.fps);
                    objectCounter.textContent = metrics.objectCount;
                    memoryCounter.textContent = metrics.memoryUsage.toFixed(1);
                }, 1000);
            }

            setupResponsiveBehavior() {
                this.responsiveManager.addResizeListener((screenInfo) => {
                    // Update virtual controls visibility based on device type
                    if (this.virtualControls) {
                        const showControls = screenInfo.isTouch;
                        this.virtualControls.updateConfig({ showControls });
                        
                        if (showControls) {
                            this.virtualControls.show();
                        } else {
                            this.virtualControls.hide();
                        }
                    }
                    
                    // Update controls hint
                    if (screenInfo.isTouch) {
                        this.controlsHint.innerHTML = 
                            '📱 Touch: Use virtual joystick to move, tap buttons to attack<br>' +
                            'Tap and hold for continuous actions • Pinch to zoom';
                    } else {
                        this.controlsHint.innerHTML = 
                            '🖱️ Mouse to aim • WASD to move • Space for auto-attack • 1-6 to switch weapons<br>' +
                            '📱 P to pause • I for inventory • K for skills • F for fullscreen';
                    }
                });
                
                this.responsiveManager.addOrientationListener((orientation) => {
                    this.errorHandler.info(`Orientation changed to ${orientation}`);
                });
            }

            setupVirtualControls() {
                if (!this.virtualControls) return;
                
                // Movement joystick
                this.virtualControls.onJoystickMove('movement', (value) => {
                    if (this.game) {
                        this.game.handleVirtualMovement(value);
                    }
                });
                
                // Attack button
                this.virtualControls.onButtonPress('attack', (pressed) => {
                    if (this.game) {
                        this.game.handleVirtualAttack(pressed);
                    }
                });
                
                // Special ability button
                this.virtualControls.onButtonPress('special', (pressed) => {
                    if (this.game) {
                        this.game.handleVirtualSpecial(pressed);
                    }
                });
                
                // Jump button
                this.virtualControls.onButtonPress('jump', (pressed) => {
                    if (this.game) {
                        this.game.handleVirtualJump(pressed);
                    }
                });
                
                // Menu buttons
                this.virtualControls.onButtonPress('pause', (pressed) => {
                    if (pressed && this.game) {
                        this.game.togglePause();
                    }
                });
                
                this.virtualControls.onButtonPress('inventory', (pressed) => {
                    if (pressed && this.game) {
                        this.game.toggleInventory();
                    }
                });
            }

            hideLoadingScreen() {
                this.loadingScreen.classList.add('hidden');
                setTimeout(() => {
                    this.loadingScreen.style.display = 'none';
                }, 500);
            }

            showError(message) {
                document.getElementById('errorMessage').textContent = message;
                this.errorDisplay.style.display = 'block';
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }

        // Start the ultimate demo when page loads
        window.addEventListener('load', () => {
            new UltimateDemo();
        });

        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (window.ultimateDemo && window.ultimateDemo.game) {
                if (document.hidden) {
                    window.ultimateDemo.game.pause();
                } else {
                    window.ultimateDemo.game.resume();
                }
            }
        });
    </script>
</body>
</html>
