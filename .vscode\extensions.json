{"recommendations": ["ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-vscode.vscode-json", "vitest.explorer", "ms-vscode.live-server", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-html-language-features", "ms-vscode.vscode-css-language-features", "ms-vscode.vscode-js-debug", "gruntfuggly.todo-tree", "streetsidesoftware.code-spell-checker", "ms-vscode.vscode-typescript-tslint-plugin"]}