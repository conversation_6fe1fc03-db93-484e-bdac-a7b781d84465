<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Bullet Hell Rogue-like RPG - A fast-paced dungeon crawler with bullet hell mechanics">
    <meta name="keywords" content="game, roguelike, bullet hell, rpg, dungeon crawler">
    <meta name="author" content="Game Developer">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="/src/main.ts" as="script" type="module">
    
    <!-- Favicon and app icons -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    
    <!-- Performance optimizations -->
    <meta name="theme-color" content="#1a1a1a">
    <meta name="color-scheme" content="dark">
    
    <title>Bullet Hell Rogue-like RPG</title>
    
    <style>
        /* Critical CSS for immediate rendering */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #1a1a1a;
            color: #ffffff;
            font-family: 'Courier New', monospace;
            overflow: hidden;
            user-select: none;
            -webkit-user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: radial-gradient(circle, #2a2a2a 0%, #1a1a1a 100%);
        }
        
        #gameCanvas {
            border: 2px solid #444;
            background: #000;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }
        
        #loadingScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #1a1a1a;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid #333;
            border-top: 3px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            font-size: 18px;
            color: #ccc;
        }
        
        /* Mobile optimizations */
        @media (max-width: 768px) {
            #gameCanvas {
                border: 1px solid #444;
            }
        }
        
        /* Touch controls will be positioned absolutely */
        .touch-controls {
            position: absolute;
            pointer-events: none;
            z-index: 100;
        }
        
        .touch-control {
            pointer-events: auto;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            touch-action: none;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
        
        <div id="loadingScreen">
            <div class="loading-spinner"></div>
            <div class="loading-text">Loading Bullet Hell RPG...</div>
        </div>
        
        <!-- Touch controls for mobile (will be shown/hidden dynamically) -->
        <div id="touchControls" class="touch-controls" style="display: none;">
            <!-- Movement joystick -->
            <div id="movementJoystick" class="touch-control" style="bottom: 20px; left: 20px; width: 100px; height: 100px;"></div>
            
            <!-- Action buttons -->
            <div id="attackButton" class="touch-control" style="bottom: 20px; right: 20px; width: 60px; height: 60px;"></div>
            <div id="skillButton" class="touch-control" style="bottom: 90px; right: 20px; width: 50px; height: 50px;"></div>
        </div>
    </div>
    
    <script type="module" src="/src/main.ts"></script>
</body>
</html>
