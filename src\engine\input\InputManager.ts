/**
 * Comprehensive input management system for keyboard, mouse, and touch
 */


export interface Vector2 {
  x: number;
  y: number;
}

export interface TouchData {
  id: number;
  position: Vector2;
  startPosition: Vector2;
  deltaPosition: Vector2;
  isActive: boolean;
}

export class InputManager {
  private canvas: HTMLCanvasElement;
  private shortcuts: KeyboardShortcuts;

  // Keyboard state
  private keysDown: Set<string> = new Set();
  private keysPressed: Set<string> = new Set();
  private keysReleased: Set<string> = new Set();

  // Mouse state
  private mousePosition: Vector2 = { x: 0, y: 0 };
  private mouseButtons: Set<number> = new Set();
  private mouseButtonsPressed: Set<number> = new Set();
  private mouseButtonsReleased: Set<number> = new Set();
  private mouseWheel: number = 0;

  // Touch state
  private touches: Map<number, TouchData> = new Map();
  private touchStarted: TouchData[] = [];
  private touchEnded: TouchData[] = [];

  // Input mapping
  private keyMap: Map<string, string> = new Map();

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.shortcuts = new KeyboardShortcuts(this);
    this.setupEventListeners();
    this.setupDefaultKeyMap();
  }

  private setupEventListeners(): void {
    // Keyboard events
    window.addEventListener('keydown', this.onKeyDown.bind(this));
    window.addEventListener('keyup', this.onKeyUp.bind(this));

    // Mouse events
    this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
    this.canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
    this.canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
    this.canvas.addEventListener('wheel', this.onMouseWheel.bind(this));
    this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());

    // Touch events
    this.canvas.addEventListener('touchstart', this.onTouchStart.bind(this), { passive: false });
    this.canvas.addEventListener('touchmove', this.onTouchMove.bind(this), { passive: false });
    this.canvas.addEventListener('touchend', this.onTouchEnd.bind(this), { passive: false });
    this.canvas.addEventListener('touchcancel', this.onTouchEnd.bind(this), { passive: false });
  }

  private setupDefaultKeyMap(): void {
    // Movement keys
    this.keyMap.set('KeyW', 'up');
    this.keyMap.set('KeyA', 'left');
    this.keyMap.set('KeyS', 'down');
    this.keyMap.set('KeyD', 'right');
    this.keyMap.set('ArrowUp', 'up');
    this.keyMap.set('ArrowLeft', 'left');
    this.keyMap.set('ArrowDown', 'down');
    this.keyMap.set('ArrowRight', 'right');

    // Action keys
    this.keyMap.set('Space', 'attack');
    this.keyMap.set('Enter', 'confirm');
    this.keyMap.set('Escape', 'menu');
    this.keyMap.set('ShiftLeft', 'run');
    this.keyMap.set('ControlLeft', 'skill');

    // Number keys for skills/items
    for (let i = 1; i <= 9; i++) {
      this.keyMap.set(`Digit${i}`, `skill${i}`);
    }
  }

  // Keyboard methods
  private onKeyDown(event: KeyboardEvent): void {
    event.preventDefault();
    const key = event.code;

    if (!this.keysDown.has(key)) {
      this.keysPressed.add(key);
    }
    this.keysDown.add(key);
  }

  private onKeyUp(event: KeyboardEvent): void {
    event.preventDefault();
    const key = event.code;

    this.keysDown.delete(key);
    this.keysReleased.add(key);
  }

  isKeyDown(key: string): boolean {
    return this.keysDown.has(key);
  }

  isKeyPressed(key: string): boolean {
    return this.keysPressed.has(key);
  }

  isKeyReleased(key: string): boolean {
    return this.keysReleased.has(key);
  }

  isActionDown(action: string): boolean {
    for (const [key, mappedAction] of this.keyMap) {
      if (mappedAction === action && this.isKeyDown(key)) {
        return true;
      }
    }
    return false;
  }

  isActionPressed(action: string): boolean {
    for (const [key, mappedAction] of this.keyMap) {
      if (mappedAction === action && this.isKeyPressed(key)) {
        return true;
      }
    }
    return false;
  }

  // Mouse methods
  private onMouseMove(event: MouseEvent): void {
    const rect = this.canvas.getBoundingClientRect();
    this.mousePosition.x = event.clientX - rect.left;
    this.mousePosition.y = event.clientY - rect.top;
  }

  private onMouseDown(event: MouseEvent): void {
    event.preventDefault();
    const button = event.button;

    if (!this.mouseButtons.has(button)) {
      this.mouseButtonsPressed.add(button);
    }
    this.mouseButtons.add(button);
  }

  private onMouseUp(event: MouseEvent): void {
    event.preventDefault();
    const button = event.button;

    this.mouseButtons.delete(button);
    this.mouseButtonsReleased.add(button);
  }

  private onMouseWheel(event: WheelEvent): void {
    event.preventDefault();
    this.mouseWheel = event.deltaY;
  }

  getMousePosition(): Vector2 {
    return { ...this.mousePosition };
  }

  isMouseButtonDown(button: number): boolean {
    return this.mouseButtons.has(button);
  }

  isMouseButtonPressed(button: number): boolean {
    return this.mouseButtonsPressed.has(button);
  }

  isMouseButtonReleased(button: number): boolean {
    return this.mouseButtonsReleased.has(button);
  }

  getMouseWheel(): number {
    return this.mouseWheel;
  }

  // Touch methods
  private onTouchStart(event: TouchEvent): void {
    event.preventDefault();

    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i];
      const rect = this.canvas.getBoundingClientRect();
      const position = {
        x: touch.clientX - rect.left,
        y: touch.clientY - rect.top
      };

      const touchData: TouchData = {
        id: touch.identifier,
        position,
        startPosition: { ...position },
        deltaPosition: { x: 0, y: 0 },
        isActive: true
      };

      this.touches.set(touch.identifier, touchData);
      this.touchStarted.push(touchData);
    }
  }

  private onTouchMove(event: TouchEvent): void {
    event.preventDefault();

    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i];
      const touchData = this.touches.get(touch.identifier);

      if (touchData) {
        const rect = this.canvas.getBoundingClientRect();
        const newPosition = {
          x: touch.clientX - rect.left,
          y: touch.clientY - rect.top
        };

        touchData.deltaPosition.x = newPosition.x - touchData.position.x;
        touchData.deltaPosition.y = newPosition.y - touchData.position.y;
        touchData.position = newPosition;
      }
    }
  }

  private onTouchEnd(event: TouchEvent): void {
    event.preventDefault();

    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i];
      const touchData = this.touches.get(touch.identifier);

      if (touchData) {
        touchData.isActive = false;
        this.touchEnded.push(touchData);
        this.touches.delete(touch.identifier);
      }
    }
  }

  getTouches(): TouchData[] {
    return Array.from(this.touches.values());
  }

  getTouchesStarted(): TouchData[] {
    return [...this.touchStarted];
  }

  getTouchesEnded(): TouchData[] {
    return [...this.touchEnded];
  }

  // Movement helper methods
  getMovementVector(): Vector2 {
    const movement = { x: 0, y: 0 };

    if (this.isActionDown('left')) movement.x -= 1;
    if (this.isActionDown('right')) movement.x += 1;
    if (this.isActionDown('up')) movement.y -= 1;
    if (this.isActionDown('down')) movement.y += 1;

    // Normalize diagonal movement
    if (movement.x !== 0 && movement.y !== 0) {
      const length = Math.sqrt(movement.x * movement.x + movement.y * movement.y);
      movement.x /= length;
      movement.y /= length;
    }

    return movement;
  }

  // Update method (call once per frame)
  update(): void {
    // Update keyboard shortcuts
    this.shortcuts.update();

    // Clear frame-specific input states
    this.keysPressed.clear();
    this.keysReleased.clear();
    this.mouseButtonsPressed.clear();
    this.mouseButtonsReleased.clear();
    this.mouseWheel = 0;
    this.touchStarted = [];
    this.touchEnded = [];

    // Update touch delta positions
    for (const touchData of this.touches.values()) {
      touchData.deltaPosition.x = 0;
      touchData.deltaPosition.y = 0;
    }
  }

  /**
   * Get the keyboard shortcuts manager
   */
  getShortcuts(): KeyboardShortcuts {
    return this.shortcuts;
  }

  destroy(): void {
    // Remove event listeners
    window.removeEventListener('keydown', this.onKeyDown.bind(this));
    window.removeEventListener('keyup', this.onKeyUp.bind(this));

    this.canvas.removeEventListener('mousemove', this.onMouseMove.bind(this));
    this.canvas.removeEventListener('mousedown', this.onMouseDown.bind(this));
    this.canvas.removeEventListener('mouseup', this.onMouseUp.bind(this));
    this.canvas.removeEventListener('wheel', this.onMouseWheel.bind(this));

    this.canvas.removeEventListener('touchstart', this.onTouchStart.bind(this));
    this.canvas.removeEventListener('touchmove', this.onTouchMove.bind(this));
    this.canvas.removeEventListener('touchend', this.onTouchEnd.bind(this));
    this.canvas.removeEventListener('touchcancel', this.onTouchEnd.bind(this));
  }
}
