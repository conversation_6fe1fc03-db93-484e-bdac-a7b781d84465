/**
 * Loading scene for asset loading and initialization
 */

import { BaseScene } from '@/engine/core/SceneManager';
import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';

export class LoadingScene extends BaseScene {
  private loadingProgress: number = 0;
  private loadingText: string = 'Loading...';
  private dots: string = '';
  private dotTimer: number = 0;

  initialize(): void {
    console.log('🔄 Loading scene initialized');
  }

  fixedUpdate(deltaTime: number): void {
    // Update loading animation
    this.dotTimer += deltaTime;
    if (this.dotTimer >= 0.5) {
      this.dotTimer = 0;
      this.dots = this.dots.length >= 3 ? '' : this.dots + '.';
    }
  }

  update(deltaTime: number): void {
    // Simulate loading progress
    this.loadingProgress += deltaTime * 50; // 50% per second
    if (this.loadingProgress >= 100) {
      this.loadingProgress = 100;
    }
  }

  render(renderer: CanvasRenderer, alpha: number): void {
    const { width, height } = renderer.getCanvasSize();
    
    // Clear with dark background
    renderer.clear('#1a1a1a');
    
    // Draw loading bar background
    const barWidth = 400;
    const barHeight = 20;
    const barX = (width - barWidth) / 2;
    const barY = height / 2;
    
    renderer.drawRect(barX, barY, barWidth, barHeight, '#333333');
    
    // Draw loading bar progress
    const progressWidth = (barWidth - 4) * (this.loadingProgress / 100);
    renderer.drawRect(barX + 2, barY + 2, progressWidth, barHeight - 4, '#4CAF50');
    
    // Draw loading text
    const text = `${this.loadingText}${this.dots}`;
    renderer.drawText(text, width / 2 - 60, barY - 40, '24px monospace', '#ffffff');
    
    // Draw percentage
    const percentage = `${Math.floor(this.loadingProgress)}%`;
    renderer.drawText(percentage, width / 2 - 20, barY + 40, '18px monospace', '#cccccc');
  }

  destroy(): void {
    console.log('🗑️ Loading scene destroyed');
  }
}
