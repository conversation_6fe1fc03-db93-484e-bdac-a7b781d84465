import { describe, it, expect } from 'vitest';
import { EntityUtils } from '@/utils/common/EntityUtils';
import { Vector2 } from '@/utils/math/Vector2';

describe('EntityUtils', () => {
  describe('applyDamage', () => {
    const baseStats = {
      health: 100,
      maxHealth: 100,
      damage: 10,
      speed: 100,
      defense: 20,
      criticalChance: 0.1,
      criticalMultiplier: 2.0
    };

    it('should apply basic damage', () => {
      const result = EntityUtils.applyDamage(baseStats, 30);
      
      expect(result.actualDamage).toBeGreaterThan(0);
      expect(result.newHealth).toBe(100 - result.actualDamage);
      expect(result.isDead).toBe(false);
    });

    it('should apply critical damage', () => {
      const result = EntityUtils.applyDamage(baseStats, 30, true);
      
      expect(result.actualDamage).toBeGreaterThan(30); // Should be multiplied
      expect(result.newHealth).toBe(100 - result.actualDamage);
    });

    it('should apply defense reduction', () => {
      const result = EntityUtils.applyDamage(baseStats, 30, false, 'physical');
      
      // With 20 defense, damage should be reduced
      expect(result.actualDamage).toBeLessThan(30);
      expect(result.actualDamage).toBeGreaterThanOrEqual(1); // Minimum damage
    });

    it('should not reduce magical damage with defense', () => {
      const result = EntityUtils.applyDamage(baseStats, 30, false, 'magical');
      
      expect(result.actualDamage).toBe(30); // No defense against magical
    });

    it('should mark as dead when health reaches zero', () => {
      const result = EntityUtils.applyDamage(baseStats, 200);
      
      expect(result.newHealth).toBe(0);
      expect(result.isDead).toBe(true);
    });

    it('should ensure minimum damage of 1', () => {
      const highDefenseStats = { ...baseStats, defense: 1000 };
      const result = EntityUtils.applyDamage(highDefenseStats, 10, false, 'physical');
      
      expect(result.actualDamage).toBe(1);
    });
  });

  describe('isCriticalHit', () => {
    it('should return true for 100% critical chance', () => {
      expect(EntityUtils.isCriticalHit(1.0)).toBe(true);
    });

    it('should return false for 0% critical chance', () => {
      expect(EntityUtils.isCriticalHit(0.0)).toBe(false);
    });

    it('should clamp critical chance to 0-1 range', () => {
      // These should not throw errors
      EntityUtils.isCriticalHit(-0.5);
      EntityUtils.isCriticalHit(1.5);
    });
  });

  describe('calculateExperienceGain', () => {
    it('should return base experience for same level', () => {
      const exp = EntityUtils.calculateExperienceGain(100, 5, 5);
      expect(exp).toBe(100);
    });

    it('should give bonus for higher level enemies', () => {
      const exp = EntityUtils.calculateExperienceGain(100, 5, 7);
      expect(exp).toBeGreaterThan(100);
    });

    it('should give penalty for much lower level enemies', () => {
      const exp = EntityUtils.calculateExperienceGain(100, 10, 3);
      expect(exp).toBeLessThan(100);
      expect(exp).toBeGreaterThanOrEqual(10); // Minimum 10% of base
    });

    it('should not penalize slightly lower level enemies', () => {
      const exp = EntityUtils.calculateExperienceGain(100, 5, 4);
      expect(exp).toBe(100);
    });
  });

  describe('updateMovement', () => {
    it('should update movement with physics', () => {
      const movement = {
        position: new Vector2(0, 0),
        velocity: new Vector2(0, 0),
        acceleration: new Vector2(0, 0),
        maxSpeed: 100,
        friction: 0.9
      };

      const inputDirection = new Vector2(1, 0);
      EntityUtils.updateMovement(movement, inputDirection, 0.016);

      expect(movement.velocity.x).toBeGreaterThan(0);
      expect(movement.position.x).toBeGreaterThan(0);
    });

    it('should apply friction', () => {
      const movement = {
        position: new Vector2(0, 0),
        velocity: new Vector2(50, 0),
        acceleration: new Vector2(0, 0),
        maxSpeed: 100,
        friction: 0.5
      };

      const inputDirection = new Vector2(0, 0);
      EntityUtils.updateMovement(movement, inputDirection, 0.016);

      expect(movement.velocity.x).toBeLessThan(50);
    });

    it('should clamp velocity to max speed', () => {
      const movement = {
        position: new Vector2(0, 0),
        velocity: new Vector2(0, 0),
        acceleration: new Vector2(0, 0),
        maxSpeed: 50,
        friction: 1.0
      };

      const inputDirection = new Vector2(1, 1); // Diagonal input
      EntityUtils.updateMovement(movement, inputDirection, 1.0); // Large delta time

      expect(movement.velocity.magnitude()).toBeLessThanOrEqual(50);
    });
  });

  describe('constrainToBounds', () => {
    it('should keep entity within bounds', () => {
      const position = new Vector2(150, 150);
      const bounds = { x: 0, y: 0, width: 100, height: 100 };
      
      EntityUtils.constrainToBounds(position, 10, bounds);
      
      expect(position.x).toBeLessThanOrEqual(90); // 100 - 10
      expect(position.y).toBeLessThanOrEqual(90);
    });

    it('should handle position outside bounds', () => {
      const position = new Vector2(-50, 200);
      const bounds = { x: 0, y: 0, width: 100, height: 100 };
      
      EntityUtils.constrainToBounds(position, 10, bounds);
      
      expect(position.x).toBe(10); // 0 + 10
      expect(position.y).toBe(90); // 100 - 10
    });
  });

  describe('seek', () => {
    it('should calculate seek steering force', () => {
      const current = new Vector2(0, 0);
      const velocity = new Vector2(0, 0);
      const target = new Vector2(100, 0);
      
      const force = EntityUtils.seek(current, velocity, target, 50, 10);
      
      expect(force.x).toBeGreaterThan(0);
      expect(force.magnitude()).toBeLessThanOrEqual(10);
    });
  });

  describe('flee', () => {
    it('should calculate flee steering force', () => {
      const current = new Vector2(0, 0);
      const velocity = new Vector2(0, 0);
      const target = new Vector2(100, 0);
      
      const force = EntityUtils.flee(current, velocity, target, 50, 10);
      
      expect(force.x).toBeLessThan(0); // Should point away from target
      expect(force.magnitude()).toBeLessThanOrEqual(10);
    });
  });

  describe('wander', () => {
    it('should calculate wander steering force', () => {
      const velocity = new Vector2(10, 0);
      const result = EntityUtils.wander(velocity, 0, 5, 10, 1, 0.016);
      
      expect(result.force).toBeDefined();
      expect(result.newWanderAngle).toBeDefined();
      expect(result.force.magnitude()).toBeLessThanOrEqual(1);
    });
  });

  describe('hasLineOfSight', () => {
    it('should return true for clear line of sight', () => {
      const from = new Vector2(0, 0);
      const to = new Vector2(100, 0);
      const obstacles: Array<{ position: Vector2; radius: number }> = [];
      
      const result = EntityUtils.hasLineOfSight(from, to, obstacles);
      expect(result).toBe(true);
    });

    it('should return false when obstacle blocks line of sight', () => {
      const from = new Vector2(0, 0);
      const to = new Vector2(100, 0);
      const obstacles = [{ position: new Vector2(50, 0), radius: 10 }];
      
      const result = EntityUtils.hasLineOfSight(from, to, obstacles);
      expect(result).toBe(false);
    });

    it('should respect max distance', () => {
      const from = new Vector2(0, 0);
      const to = new Vector2(200, 0);
      const obstacles: Array<{ position: Vector2; radius: number }> = [];
      
      const result = EntityUtils.hasLineOfSight(from, to, obstacles, 100);
      expect(result).toBe(false);
    });
  });

  describe('calculateAttackPosition', () => {
    it('should calculate optimal attack position', () => {
      const attacker = new Vector2(0, 0);
      const target = new Vector2(100, 0);
      
      const position = EntityUtils.calculateAttackPosition(attacker, target, 50);
      
      expect(position.distance(target)).toBeCloseTo(40, 1); // 80% of range
    });

    it('should use preferred angle', () => {
      const attacker = new Vector2(0, 0);
      const target = new Vector2(100, 0);
      
      const position = EntityUtils.calculateAttackPosition(attacker, target, 50, Math.PI / 2);
      
      expect(position.y).toBeCloseTo(40, 1); // Should be above target
    });
  });

  describe('predictTargetPosition', () => {
    it('should predict future position of moving target', () => {
      const position = new Vector2(0, 0);
      const velocity = new Vector2(10, 0);
      
      const predicted = EntityUtils.predictTargetPosition(position, velocity, 50, 2);
      
      expect(predicted.x).toBeGreaterThan(0);
    });

    it('should handle zero projectile speed', () => {
      const position = new Vector2(0, 0);
      const velocity = new Vector2(10, 0);
      
      const predicted = EntityUtils.predictTargetPosition(position, velocity, 0, 2);
      
      expect(predicted.equals(position)).toBe(true);
    });
  });

  describe('calculateKnockback', () => {
    it('should calculate knockback force', () => {
      const from = new Vector2(0, 0);
      const to = new Vector2(10, 0);
      
      const knockback = EntityUtils.calculateKnockback(from, to, 50);
      
      expect(knockback.x).toBeGreaterThan(0);
      expect(knockback.magnitude()).toBeCloseTo(50, 1);
    });

    it('should handle identical positions', () => {
      const from = new Vector2(0, 0);
      const to = new Vector2(0, 0);
      
      const knockback = EntityUtils.calculateKnockback(from, to, 50);
      
      expect(knockback.magnitude()).toBeCloseTo(50, 1);
    });
  });

  describe('isOnScreen', () => {
    it('should return true for entity on screen', () => {
      const entity = new Vector2(50, 50);
      const camera = new Vector2(0, 0);
      
      const result = EntityUtils.isOnScreen(entity, 10, camera, 100, 100);
      expect(result).toBe(true);
    });

    it('should return false for entity off screen', () => {
      const entity = new Vector2(200, 200);
      const camera = new Vector2(0, 0);
      
      const result = EntityUtils.isOnScreen(entity, 10, camera, 100, 100);
      expect(result).toBe(false);
    });

    it('should include margin in calculation', () => {
      const entity = new Vector2(-10, -10);
      const camera = new Vector2(0, 0);
      
      const result = EntityUtils.isOnScreen(entity, 5, camera, 100, 100, 20);
      expect(result).toBe(true); // Within margin
    });
  });

  describe('findSafeSpawnPosition', () => {
    it('should find safe spawn position', () => {
      const bounds = { x: 0, y: 0, width: 100, height: 100 };
      const entities = [{ position: new Vector2(50, 50), radius: 10 }];
      
      const position = EntityUtils.findSafeSpawnPosition(bounds, entities, 30);
      
      if (position) {
        expect(position.distance(entities[0].position)).toBeGreaterThan(30);
      }
    });

    it('should return null if no safe position found', () => {
      const bounds = { x: 0, y: 0, width: 10, height: 10 };
      const entities = [{ position: new Vector2(5, 5), radius: 20 }];
      
      const position = EntityUtils.findSafeSpawnPosition(bounds, entities, 30, 5);
      
      expect(position).toBeNull();
    });
  });

  describe('calculateLevel', () => {
    it('should calculate level from experience', () => {
      expect(EntityUtils.calculateLevel(0)).toBe(1);
      expect(EntityUtils.calculateLevel(100)).toBe(2);
      expect(EntityUtils.calculateLevel(400)).toBe(3);
    });
  });

  describe('experienceForLevel', () => {
    it('should calculate experience required for level', () => {
      expect(EntityUtils.experienceForLevel(1)).toBe(100);
      expect(EntityUtils.experienceForLevel(2)).toBe(400);
      expect(EntityUtils.experienceForLevel(3)).toBe(900);
    });
  });

  describe('scaleStatsForLevel', () => {
    it('should scale stats based on level', () => {
      const baseStats = {
        health: 100,
        maxHealth: 100,
        damage: 10,
        speed: 50
      };

      const scaled = EntityUtils.scaleStatsForLevel(baseStats, 5);

      expect(scaled.health).toBeGreaterThan(baseStats.health);
      expect(scaled.damage).toBeGreaterThan(baseStats.damage);
      expect(scaled.speed).toBeGreaterThan(baseStats.speed);
      expect(scaled.speed).toBeLessThanOrEqual(baseStats.speed * 1.5); // Speed cap
    });

    it('should handle optional stats', () => {
      const baseStats = {
        health: 100,
        maxHealth: 100,
        damage: 10,
        speed: 50,
        mana: 50,
        maxMana: 50,
        defense: 5
      };

      const scaled = EntityUtils.scaleStatsForLevel(baseStats, 3);

      expect(scaled.mana).toBeGreaterThan(baseStats.mana!);
      expect(scaled.defense).toBeGreaterThan(baseStats.defense!);
    });
  });
});
