/**
 * Game logic manager for handling game state updates
 * Separates game logic from rendering and input handling
 */

import { Player } from '@/game/entities/Player';
import { Enemy } from '@/game/entities/Enemy';
import { Bullet } from '@/game/entities/Bullet';
import { Item } from '@/game/systems/LootSystem';
import { ProgressionSystem, ProgressionData } from '@/game/systems/ProgressionSystem';
import { InventorySystem } from '@/game/systems/InventorySystem';
import { LootSystem } from '@/game/systems/LootSystem';
import { OptimizedCollisionSystem } from '@/engine/physics/OptimizedCollisionSystem';
import { Vector2 } from '@/utils/math/Vector2';

export interface GameEntities {
  player: Player;
  enemies: Enemy[];
  bullets: Bullet[];
  items: Item[];
}

export interface GameSystems {
  progressionSystem: ProgressionSystem;
  inventorySystem: InventorySystem;
  lootSystem: LootSystem;
  collisionSystem: OptimizedCollisionSystem;
}

export interface GameState {
  score: number;
  gameTime: number;
  enemySpawnTimer: number;
  enemySpawnInterval: number;
  playerProgression: ProgressionData;
}

export class GameLogicManager {
  private entities: GameEntities;
  private systems: GameSystems;
  private state: GameState;

  constructor(entities: GameEntities, systems: GameSystems, initialState: GameState) {
    this.entities = entities;
    this.systems = systems;
    this.state = initialState;
  }

  /**
   * Update all game logic for one frame
   */
  update(deltaTime: number): void {
    this.state.gameTime += deltaTime;
    
    this.updatePlayer(deltaTime);
    this.updateEnemies(deltaTime);
    this.updateBullets(deltaTime);
    this.updateItems(deltaTime);
    this.handleCollisions();
    this.updateEnemySpawning(deltaTime);
  }

  /**
   * Update player logic
   */
  private updatePlayer(deltaTime: number): void {
    if (this.entities.player.isAlive) {
      this.entities.player.update(deltaTime);
      
      // Apply progression stats to player
      this.applyProgressionToPlayer();
    }
  }

  /**
   * Update enemy logic
   */
  private updateEnemies(deltaTime: number): void {
    for (let i = this.entities.enemies.length - 1; i >= 0; i--) {
      const enemy = this.entities.enemies[i];

      if (!enemy.isAlive) {
        this.handleEnemyDeath(enemy);
        this.entities.enemies.splice(i, 1);
        continue;
      }

      // Update enemy AI and movement
      enemy.update(deltaTime, this.entities.player, null);
    }
  }

  /**
   * Update bullet logic
   */
  private updateBullets(deltaTime: number): void {
    for (let i = this.entities.bullets.length - 1; i >= 0; i--) {
      const bullet = this.entities.bullets[i];

      bullet.update(deltaTime);

      // Remove inactive bullets
      if (!bullet.isActive()) {
        this.entities.bullets.splice(i, 1);
      }
    }
  }

  /**
   * Update item logic
   */
  private updateItems(deltaTime: number): void {
    for (let i = this.entities.items.length - 1; i >= 0; i--) {
      const item = this.entities.items[i];

      // Check if player is close enough to pick up
      const distance = this.entities.player.position.distance(item.position);
      if (distance < 32) { // Pickup range
        if (this.systems.inventorySystem.addItem(item)) {
          this.entities.items.splice(i, 1);
          console.log(`📦 Picked up: ${item.name}`);
        }
      }
    }
  }

  /**
   * Handle all collision detection and response
   */
  private handleCollisions(): void {
    this.systems.collisionSystem.resetCollisionChecks();

    // Convert entities to spatial objects for collision detection
    const playerSpatial = {
      position: this.entities.player.position,
      collisionRadius: this.entities.player.config.collisionRadius,
      id: 'player'
    };

    const enemySpatials = this.entities.enemies
      .filter(enemy => enemy.isAlive)
      .map(enemy => ({
        position: enemy.position,
        collisionRadius: enemy.config.collisionRadius,
        id: enemy.id || 'enemy',
        enemy
      }));

    const bulletSpatials = this.entities.bullets
      .filter(bullet => bullet.isPlayerBullet)
      .map((bullet, index) => ({
        position: bullet.getPosition(),
        collisionRadius: bullet.getSize() / 2,
        id: `bullet_${index}`,
        bullet,
        index
      }));

    // Player vs enemies collision
    this.systems.collisionSystem.checkCollisions(
      [playerSpatial],
      enemySpatials,
      (player, enemyObj) => {
        this.entities.player.takeDamage(enemyObj.enemy.stats.damage);
      }
    );

    // Bullets vs enemies collision
    const bulletsToRemove: number[] = [];
    this.systems.collisionSystem.checkCollisions(
      bulletSpatials,
      enemySpatials,
      (bulletObj, enemyObj) => {
        enemyObj.enemy.takeDamage(bulletObj.bullet.getDamage());
        bulletsToRemove.push(bulletObj.index);
      }
    );

    // Remove bullets that hit enemies (in reverse order to maintain indices)
    bulletsToRemove.sort((a, b) => b - a);
    for (const index of bulletsToRemove) {
      if (index < this.entities.bullets.length) {
        this.entities.bullets.splice(index, 1);
      }
    }
  }

  /**
   * Handle enemy death and rewards
   */
  private handleEnemyDeath(enemy: Enemy): void {
    // Award experience
    const experience = enemy.getExperienceValue();
    this.state.score += experience;

    const leveledUp = this.systems.progressionSystem.gainExperience(
      this.state.playerProgression, 
      experience
    );
    
    if (leveledUp) {
      console.log(`🎉 Level up! Now level ${this.state.playerProgression.level}`);
      this.applyProgressionToPlayer();
    }

    // Generate loot
    const loot = this.systems.lootSystem.generateLoot(
      this.state.playerProgression.level, 
      enemy.config.aiType
    );
    
    if (loot) {
      loot.position = enemy.position.copy();
      this.entities.items.push(loot);
    }
  }

  /**
   * Apply progression stats to player
   */
  private applyProgressionToPlayer(): void {
    const progression = this.state.playerProgression;
    const player = this.entities.player;

    player.stats.health = progression.stats.health;
    player.stats.maxHealth = progression.stats.maxHealth;
    player.stats.mana = progression.stats.mana;
    player.stats.maxMana = progression.stats.maxMana;
    player.stats.damage = progression.stats.damage;
    player.stats.speed = progression.stats.speed;
    player.stats.defense = progression.stats.defense;
    player.stats.criticalChance = progression.stats.criticalChance;
    player.stats.criticalMultiplier = progression.stats.criticalMultiplier;
  }

  /**
   * Update enemy spawning logic
   */
  private updateEnemySpawning(deltaTime: number): void {
    this.state.enemySpawnTimer += deltaTime;

    if (this.state.enemySpawnTimer >= this.state.enemySpawnInterval) {
      this.spawnEnemy();
      this.state.enemySpawnTimer = 0;

      // Gradually increase spawn rate
      this.state.enemySpawnInterval = Math.max(0.5, this.state.enemySpawnInterval * 0.99);
    }
  }

  /**
   * Spawn a new enemy
   */
  private spawnEnemy(): void {
    // Spawn enemy at random position around the player
    const spawnDistance = 400;
    const angle = Math.random() * Math.PI * 2;
    const spawnX = this.entities.player.position.x + Math.cos(angle) * spawnDistance;
    const spawnY = this.entities.player.position.y + Math.sin(angle) * spawnDistance;

    const enemy = new Enemy(new Vector2(spawnX, spawnY), 'basic');
    this.entities.enemies.push(enemy);
  }

  /**
   * Add a bullet to the game
   */
  addBullet(bullet: Bullet): void {
    this.entities.bullets.push(bullet);
  }

  /**
   * Get current game state
   */
  getState(): GameState {
    return { ...this.state };
  }

  /**
   * Get entities (read-only access)
   */
  getEntities(): Readonly<GameEntities> {
    return this.entities;
  }

  /**
   * Update camera to follow player
   */
  updateCamera(camera: { x: number; y: number }, deltaTime: number): void {
    const targetX = this.entities.player.position.x - 512; // Center on screen
    const targetY = this.entities.player.position.y - 384;

    // Smooth camera movement
    const lerpFactor = 5 * deltaTime;
    camera.x += (targetX - camera.x) * lerpFactor;
    camera.y += (targetY - camera.y) * lerpFactor;
  }

  /**
   * Check if game is over
   */
  isGameOver(): boolean {
    return !this.entities.player.isAlive;
  }

  /**
   * Reset game state for new game
   */
  reset(): void {
    this.entities.enemies = [];
    this.entities.bullets = [];
    this.entities.items = [];
    
    this.state.score = 0;
    this.state.gameTime = 0;
    this.state.enemySpawnTimer = 0;
    this.state.enemySpawnInterval = 2.0;
    
    // Reset player
    this.entities.player.position.set(512, 384);
    this.entities.player.stats.health = this.entities.player.stats.maxHealth;
    
    // Reset progression
    this.state.playerProgression = this.systems.progressionSystem.createNewCharacter();
    this.applyProgressionToPlayer();
  }
}
