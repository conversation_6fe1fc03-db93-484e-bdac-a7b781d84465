import { describe, it, expect, beforeEach } from 'vitest';
import { CollisionSystem, Collider } from '@/engine/physics/CollisionSystem';
import { Vector2 } from '@/utils/math/Vector2';

describe('CollisionSystem', () => {
  describe('checkCircleCollision', () => {
    it('should detect collision between overlapping circles', () => {
      const circleA: Collider = {
        position: new Vector2(0, 0),
        collisionRadius: 10,
        type: 'circle'
      };

      const circleB: Collider = {
        position: new Vector2(15, 0),
        collisionRadius: 10,
        type: 'circle'
      };

      const result = CollisionSystem.checkCircleCollision(circleA, circleB);

      expect(result.hasCollision).toBe(true);
      expect(result.distance).toBe(15);
      expect(result.overlap).toBe(5); // 20 - 15
      expect(result.normal).toBeDefined();
      expect(result.point).toBeDefined();
    });

    it('should not detect collision between non-overlapping circles', () => {
      const circleA: Collider = {
        position: new Vector2(0, 0),
        collisionRadius: 10,
        type: 'circle'
      };

      const circleB: Collider = {
        position: new Vector2(25, 0),
        collisionRadius: 10,
        type: 'circle'
      };

      const result = CollisionSystem.checkCircleCollision(circleA, circleB);

      expect(result.hasCollision).toBe(false);
    });

    it('should handle touching circles', () => {
      const circleA: Collider = {
        position: new Vector2(0, 0),
        collisionRadius: 10,
        type: 'circle'
      };

      const circleB: Collider = {
        position: new Vector2(20, 0),
        collisionRadius: 10,
        type: 'circle'
      };

      const result = CollisionSystem.checkCircleCollision(circleA, circleB);

      expect(result.hasCollision).toBe(true);
      expect(result.overlap).toBe(0);
    });

    it('should calculate correct normal vector', () => {
      const circleA: Collider = {
        position: new Vector2(0, 0),
        collisionRadius: 10,
        type: 'circle'
      };

      const circleB: Collider = {
        position: new Vector2(10, 0),
        collisionRadius: 10,
        type: 'circle'
      };

      const result = CollisionSystem.checkCircleCollision(circleA, circleB);

      expect(result.hasCollision).toBe(true);
      expect(result.normal?.x).toBeCloseTo(1, 5);
      expect(result.normal?.y).toBeCloseTo(0, 5);
    });
  });

  describe('checkRectangleCollision', () => {
    it('should detect collision between overlapping rectangles', () => {
      const rectA: Collider = {
        position: new Vector2(0, 0),
        bounds: { x: 0, y: 0, width: 20, height: 20 },
        type: 'rectangle'
      };

      const rectB: Collider = {
        position: new Vector2(10, 10),
        bounds: { x: 10, y: 10, width: 20, height: 20 },
        type: 'rectangle'
      };

      const result = CollisionSystem.checkRectangleCollision(rectA, rectB);

      expect(result.hasCollision).toBe(true);
      expect(result.overlap).toBe(10); // Minimum overlap
      expect(result.normal).toBeDefined();
      expect(result.point).toBeDefined();
    });

    it('should not detect collision between non-overlapping rectangles', () => {
      const rectA: Collider = {
        position: new Vector2(0, 0),
        bounds: { x: 0, y: 0, width: 10, height: 10 },
        type: 'rectangle'
      };

      const rectB: Collider = {
        position: new Vector2(20, 20),
        bounds: { x: 20, y: 20, width: 10, height: 10 },
        type: 'rectangle'
      };

      const result = CollisionSystem.checkRectangleCollision(rectA, rectB);

      expect(result.hasCollision).toBe(false);
    });

    it('should handle rectangles without bounds', () => {
      const rectA: Collider = {
        position: new Vector2(0, 0),
        type: 'rectangle'
      };

      const rectB: Collider = {
        position: new Vector2(10, 10),
        bounds: { x: 10, y: 10, width: 20, height: 20 },
        type: 'rectangle'
      };

      const result = CollisionSystem.checkRectangleCollision(rectA, rectB);

      expect(result.hasCollision).toBe(false);
    });
  });

  describe('checkCircleRectangleCollision', () => {
    it('should detect collision between circle and rectangle', () => {
      const circle: Collider = {
        position: new Vector2(15, 15),
        collisionRadius: 10,
        type: 'circle'
      };

      const rect: Collider = {
        position: new Vector2(0, 0),
        bounds: { x: 0, y: 0, width: 20, height: 20 },
        type: 'rectangle'
      };

      const result = CollisionSystem.checkCircleRectangleCollision(circle, rect);

      expect(result.hasCollision).toBe(true);
    });

    it('should not detect collision when circle is far from rectangle', () => {
      const circle: Collider = {
        position: new Vector2(50, 50),
        collisionRadius: 10,
        type: 'circle'
      };

      const rect: Collider = {
        position: new Vector2(0, 0),
        bounds: { x: 0, y: 0, width: 20, height: 20 },
        type: 'rectangle'
      };

      const result = CollisionSystem.checkCircleRectangleCollision(circle, rect);

      expect(result.hasCollision).toBe(false);
    });

    it('should handle rectangle without bounds', () => {
      const circle: Collider = {
        position: new Vector2(15, 15),
        collisionRadius: 10,
        type: 'circle'
      };

      const rect: Collider = {
        position: new Vector2(0, 0),
        type: 'rectangle'
      };

      const result = CollisionSystem.checkCircleRectangleCollision(circle, rect);

      expect(result.hasCollision).toBe(false);
    });
  });

  describe('checkCollision (generic)', () => {
    it('should route to circle collision for two circles', () => {
      const circleA: Collider = {
        position: new Vector2(0, 0),
        collisionRadius: 10,
        type: 'circle'
      };

      const circleB: Collider = {
        position: new Vector2(15, 0),
        collisionRadius: 10,
        type: 'circle'
      };

      const result = CollisionSystem.checkCollision(circleA, circleB);

      expect(result.hasCollision).toBe(true);
    });

    it('should route to rectangle collision for two rectangles', () => {
      const rectA: Collider = {
        position: new Vector2(0, 0),
        bounds: { x: 0, y: 0, width: 20, height: 20 },
        type: 'rectangle'
      };

      const rectB: Collider = {
        position: new Vector2(10, 10),
        bounds: { x: 10, y: 10, width: 20, height: 20 },
        type: 'rectangle'
      };

      const result = CollisionSystem.checkCollision(rectA, rectB);

      expect(result.hasCollision).toBe(true);
    });

    it('should route to circle-rectangle collision', () => {
      const circle: Collider = {
        position: new Vector2(15, 15),
        collisionRadius: 10,
        type: 'circle'
      };

      const rect: Collider = {
        position: new Vector2(0, 0),
        bounds: { x: 0, y: 0, width: 20, height: 20 },
        type: 'rectangle'
      };

      const result = CollisionSystem.checkCollision(circle, rect);

      expect(result.hasCollision).toBe(true);
    });

    it('should flip normal for rectangle-circle collision', () => {
      const rect: Collider = {
        position: new Vector2(0, 0),
        bounds: { x: 0, y: 0, width: 20, height: 20 },
        type: 'rectangle'
      };

      const circle: Collider = {
        position: new Vector2(25, 10),
        collisionRadius: 10,
        type: 'circle'
      };

      const result = CollisionSystem.checkCollision(rect, circle);

      expect(result.hasCollision).toBe(true);
      // Normal should be flipped compared to circle-rectangle
    });

    it('should return no collision for unsupported types', () => {
      const objA: Collider = {
        position: new Vector2(0, 0),
        type: 'unsupported' as any
      };

      const objB: Collider = {
        position: new Vector2(10, 10),
        type: 'circle'
      };

      const result = CollisionSystem.checkCollision(objA, objB);

      expect(result.hasCollision).toBe(false);
    });
  });

  describe('spatialHash', () => {
    it('should group objects into spatial cells', () => {
      const objects = [
        { position: new Vector2(0, 0) },
        { position: new Vector2(10, 10) },
        { position: new Vector2(100, 100) },
        { position: new Vector2(105, 105) }
      ];

      const grid = CollisionSystem.spatialHash(objects, 64);

      expect(grid.size).toBeGreaterThan(0);
      
      // Objects at (100, 100) and (105, 105) should be in the same cell
      const cell1_1 = grid.get('1,1');
      expect(cell1_1).toBeDefined();
      expect(cell1_1?.length).toBe(2);
    });

    it('should handle empty object array', () => {
      const objects: { position: Vector2 }[] = [];
      const grid = CollisionSystem.spatialHash(objects, 64);

      expect(grid.size).toBe(0);
    });
  });

  describe('getPotentialCollisionPairs', () => {
    it('should return potential collision pairs', () => {
      const objects = [
        { position: new Vector2(0, 0), id: 'a' },
        { position: new Vector2(10, 10), id: 'b' },
        { position: new Vector2(100, 100), id: 'c' },
        { position: new Vector2(105, 105), id: 'd' }
      ];

      const pairs = CollisionSystem.getPotentialCollisionPairs(objects, 64);

      expect(pairs.length).toBeGreaterThan(0);
      
      // Should include pairs from the same spatial cell
      const hasCloseObjectPair = pairs.some(([a, b]) => 
        (a.id === 'c' && b.id === 'd') || (a.id === 'd' && b.id === 'c')
      );
      expect(hasCloseObjectPair).toBe(true);
    });

    it('should not return duplicate pairs', () => {
      const objects = [
        { position: new Vector2(0, 0), id: 'a' },
        { position: new Vector2(5, 5), id: 'b' },
        { position: new Vector2(10, 10), id: 'c' }
      ];

      const pairs = CollisionSystem.getPotentialCollisionPairs(objects, 64);

      // Check for duplicates
      const pairStrings = pairs.map(([a, b]) => `${a.id}-${b.id}`);
      const uniquePairStrings = [...new Set(pairStrings)];
      
      expect(pairStrings.length).toBe(uniquePairStrings.length);
    });

    it('should handle single object', () => {
      const objects = [{ position: new Vector2(0, 0), id: 'a' }];
      const pairs = CollisionSystem.getPotentialCollisionPairs(objects, 64);

      expect(pairs.length).toBe(0);
    });
  });
});
