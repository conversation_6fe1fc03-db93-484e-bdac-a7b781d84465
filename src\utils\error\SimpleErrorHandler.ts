/**
 * Simplified error handling system focused on performance and clarity
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface ErrorContext {
  component?: string;
  action?: string;
  data?: any;
}

export interface GameError extends Error {
  level: LogLevel;
  context?: ErrorContext;
  timestamp: number;
}

export class SimpleErrorHandler {
  private static instance: SimpleErrorHandler;
  private isProduction: boolean;
  private errorCount: number = 0;
  private lastErrors: GameError[] = [];
  private maxStoredErrors: number = 10; // Much smaller than original 100

  private constructor() {
    this.isProduction = !this.isDevelopment();
    this.setupGlobalErrorHandling();
  }

  static getInstance(): SimpleErrorHandler {
    if (!SimpleErrorHandler.instance) {
      SimpleErrorHandler.instance = new SimpleErrorHandler();
    }
    return SimpleErrorHandler.instance;
  }

  private isDevelopment(): boolean {
    return typeof window !== 'undefined' && 
           (window.location.hostname === 'localhost' || 
            window.location.hostname.includes('127.0.0.1'));
  }

  private setupGlobalErrorHandling(): void {
    // Only in browser environment
    if (typeof window === 'undefined') return;

    // Catch unhandled errors
    window.addEventListener('error', (event) => {
      this.error('Unhandled error', {
        component: 'global',
        action: 'unhandled_error',
        data: {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        }
      });
    });

    // Catch unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.error('Unhandled promise rejection', {
        component: 'global',
        action: 'unhandled_rejection',
        data: { reason: event.reason }
      });
    });
  }

  // Core logging methods
  debug(message: string, context?: ErrorContext): void {
    if (!this.isProduction) {
      this.log('debug', message, context);
    }
  }

  info(message: string, context?: ErrorContext): void {
    this.log('info', message, context);
  }

  warn(message: string, context?: ErrorContext): void {
    this.log('warn', message, context);
  }

  error(message: string, context?: ErrorContext): void {
    this.log('error', message, context);
    this.errorCount++;
  }

  private log(level: LogLevel, message: string, context?: ErrorContext): void {
    const gameError: GameError = Object.assign(new Error(message), {
      level,
      context,
      timestamp: Date.now()
    });

    // Store recent errors for debugging
    this.storeError(gameError);

    // Log to console with appropriate method
    this.logToConsole(gameError);

    // In production, could send to external service
    if (this.isProduction && level === 'error') {
      this.reportToExternalService(gameError);
    }
  }

  private storeError(error: GameError): void {
    this.lastErrors.push(error);
    
    // Keep only recent errors
    if (this.lastErrors.length > this.maxStoredErrors) {
      this.lastErrors.shift();
    }
  }

  private logToConsole(error: GameError): void {
    const prefix = error.context?.component ? `[${error.context.component}]` : '';
    const message = `${prefix} ${error.message}`;

    switch (error.level) {
      case 'debug':
        console.debug(message, error.context?.data);
        break;
      case 'info':
        console.info(message, error.context?.data);
        break;
      case 'warn':
        console.warn(message, error.context?.data);
        break;
      case 'error':
        console.error(message, error.context?.data);
        if (error.stack && !this.isProduction) {
          console.error(error.stack);
        }
        break;
    }
  }

  private reportToExternalService(error: GameError): void {
    // Placeholder for external error reporting
    // In a real application, this would send to services like Sentry, LogRocket, etc.
    if (!this.isProduction) return;

    try {
      // Example: send to analytics or error tracking service
      // fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     message: error.message,
      //     level: error.level,
      //     context: error.context,
      //     timestamp: error.timestamp,
      //     userAgent: navigator.userAgent,
      //     url: window.location.href
      //   })
      // });
    } catch (reportingError) {
      // Silently fail - don't let error reporting break the game
      console.warn('Failed to report error to external service');
    }
  }

  // Utility methods for common error patterns
  assertNotNull<T>(value: T | null | undefined, message: string, context?: ErrorContext): T {
    if (value == null) {
      this.error(`Assertion failed: ${message}`, context);
      throw new Error(message);
    }
    return value;
  }

  warnOnce(key: string, message: string, context?: ErrorContext): void {
    if (!this.warnOnceCache) {
      this.warnOnceCache = new Set();
    }
    
    if (!this.warnOnceCache.has(key)) {
      this.warnOnceCache.add(key);
      this.warn(message, context);
    }
  }

  private warnOnceCache?: Set<string>;

  // Performance-focused validation
  validateConfig<T>(config: T, validator: (config: T) => boolean, errorMessage: string): T {
    if (!this.isProduction && !validator(config)) {
      this.error(`Configuration validation failed: ${errorMessage}`, {
        component: 'config',
        data: config
      });
      throw new Error(errorMessage);
    }
    return config;
  }

  // Simple recovery for common issues
  tryRecover<T>(operation: () => T, fallback: T, context?: ErrorContext): T {
    try {
      return operation();
    } catch (error) {
      this.warn(`Operation failed, using fallback`, {
        ...context,
        data: { error: error instanceof Error ? error.message : error }
      });
      return fallback;
    }
  }

  // Debugging utilities
  getRecentErrors(): GameError[] {
    return [...this.lastErrors];
  }

  getErrorCount(): number {
    return this.errorCount;
  }

  clearErrors(): void {
    this.lastErrors = [];
    this.errorCount = 0;
  }

  // Performance monitoring
  time<T>(label: string, operation: () => T, context?: ErrorContext): T {
    if (this.isProduction) {
      return operation(); // Skip timing in production
    }

    const start = performance.now();
    try {
      const result = operation();
      const duration = performance.now() - start;
      
      if (duration > 16) { // Warn if operation takes more than one frame
        this.warn(`Slow operation: ${label} took ${duration.toFixed(2)}ms`, {
          ...context,
          component: 'performance'
        });
      }
      
      return result;
    } catch (error) {
      const duration = performance.now() - start;
      this.error(`Operation failed: ${label} (${duration.toFixed(2)}ms)`, {
        ...context,
        component: 'performance',
        data: { error: error instanceof Error ? error.message : error }
      });
      throw error;
    }
  }
}

// Convenience functions for global use
const errorHandler = SimpleErrorHandler.getInstance();

export const debug = (message: string, context?: ErrorContext) => errorHandler.debug(message, context);
export const info = (message: string, context?: ErrorContext) => errorHandler.info(message, context);
export const warn = (message: string, context?: ErrorContext) => errorHandler.warn(message, context);
export const error = (message: string, context?: ErrorContext) => errorHandler.error(message, context);
export const assertNotNull = <T>(value: T | null | undefined, message: string, context?: ErrorContext): T => 
  errorHandler.assertNotNull(value, message, context);
export const warnOnce = (key: string, message: string, context?: ErrorContext) => 
  errorHandler.warnOnce(key, message, context);
export const tryRecover = <T>(operation: () => T, fallback: T, context?: ErrorContext): T => 
  errorHandler.tryRecover(operation, fallback, context);
export const timeOperation = <T>(label: string, operation: () => T, context?: ErrorContext): T => 
  errorHandler.time(label, operation, context);

export default SimpleErrorHandler;
