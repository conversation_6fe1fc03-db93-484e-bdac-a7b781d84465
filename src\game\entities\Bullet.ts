/**
 * Bullet entity with object pooling support
 */

import { Poolable } from '@/engine/core/ObjectPool';
import { Vector2 } from '@/utils/math/Vector2';
import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';

export interface BulletConfig {
  damage: number;
  speed: number;
  size: number;
  color: string;
  lifetime: number;
  piercing: boolean;
  homing: boolean;
  homingStrength?: number;
}

export class Bullet implements Poolable {
  public position: Vector2 = new Vector2(0, 0);
  public velocity: Vector2 = new Vector2(0, 0);
  public config: BulletConfig = {
    damage: 10,
    speed: 300,
    size: 6,
    color: '#ffffff',
    lifetime: 3,
    piercing: false,
    homing: false
  };
  
  public isPlayerBullet: boolean = false;
  public currentLifetime: number = 0;
  public hitTargets: Set<any> = new Set();
  
  private active: boolean = false;

  constructor() {
    this.reset();
  }

  initialize(
    x: number,
    y: number,
    direction: Vector2,
    config: Partial<BulletConfig>,
    isPlayerBullet: boolean = false
  ): void {
    this.position.set(x, y);
    this.velocity = direction.normalize().multiply(config.speed || this.config.speed);
    
    // Merge config
    Object.assign(this.config, config);
    
    this.isPlayerBullet = isPlayerBullet;
    this.currentLifetime = this.config.lifetime;
    this.hitTargets.clear();
    this.active = true;
  }

  update(deltaTime: number, targets?: any[]): void {
    if (!this.active) return;

    // Update lifetime
    this.currentLifetime -= deltaTime;
    if (this.currentLifetime <= 0) {
      this.active = false;
      return;
    }

    // Homing behavior
    if (this.config.homing && targets && targets.length > 0) {
      this.updateHoming(targets, deltaTime);
    }

    // Update position
    this.position = this.position.add(this.velocity.multiply(deltaTime));

    // Check bounds
    if (this.position.x < -50 || this.position.x > 1074 ||
        this.position.y < -50 || this.position.y > 818) {
      this.active = false;
    }
  }

  private updateHoming(targets: any[], deltaTime: number): void {
    if (!this.config.homingStrength) return;

    // Find closest target
    let closestTarget = null;
    let closestDistance = Infinity;

    for (const target of targets) {
      if (this.hitTargets.has(target)) continue;
      
      const distance = this.position.distance(target.position);
      if (distance < closestDistance) {
        closestDistance = distance;
        closestTarget = target;
      }
    }

    if (closestTarget && closestDistance < 200) { // Homing range
      const targetDirection = new Vector2(
        closestTarget.position.x - this.position.x,
        closestTarget.position.y - this.position.y
      ).normalize();

      // Gradually turn towards target
      const currentDirection = this.velocity.normalize();
      const homingForce = targetDirection.subtract(currentDirection)
        .multiply(this.config.homingStrength * deltaTime);

      this.velocity = this.velocity.add(homingForce).normalize()
        .multiply(this.config.speed);
    }
  }

  checkCollision(target: any): boolean {
    if (!this.active || this.hitTargets.has(target)) return false;

    const distance = this.position.distance(target.position);
    const collisionDistance = (this.config.size + target.size) / 2;

    if (distance <= collisionDistance) {
      this.onHit(target);
      return true;
    }

    return false;
  }

  private onHit(target: any): void {
    this.hitTargets.add(target);

    // Apply damage
    if (target.takeDamage) {
      target.takeDamage(this.config.damage);
    }

    // Destroy bullet unless it's piercing
    if (!this.config.piercing) {
      this.active = false;
    }
  }

  render(renderer: CanvasRenderer): void {
    if (!this.active) return;

    // Calculate alpha based on remaining lifetime
    const alpha = Math.min(1, this.currentLifetime / Math.min(this.config.lifetime, 1));
    renderer.setGlobalAlpha(alpha);

    // Draw bullet
    renderer.drawCircle(
      this.position.x,
      this.position.y,
      this.config.size / 2,
      this.config.color
    );

    // Draw trail for fast bullets
    if (this.config.speed > 400) {
      const trailLength = Math.min(20, this.config.speed / 20);
      const trailDirection = this.velocity.normalize().multiply(-trailLength);
      const trailEnd = this.position.add(trailDirection);

      renderer.setGlobalAlpha(alpha * 0.5);
      renderer.drawLine(
        this.position.x,
        this.position.y,
        trailEnd.x,
        trailEnd.y,
        this.config.color,
        2
      );
    }

    renderer.setGlobalAlpha(1);
  }

  // Poolable interface
  reset(): void {
    this.position.set(0, 0);
    this.velocity.set(0, 0);
    this.currentLifetime = 0;
    this.hitTargets.clear();
    this.active = false;
    this.isPlayerBullet = false;
    
    // Reset config to defaults
    this.config = {
      damage: 10,
      speed: 300,
      size: 6,
      color: '#ffffff',
      lifetime: 3,
      piercing: false,
      homing: false
    };
  }

  isActive(): boolean {
    return this.active;
  }

  setActive(active: boolean): void {
    this.active = active;
  }

  // Utility methods
  getPosition(): Vector2 {
    return this.position.copy();
  }

  getVelocity(): Vector2 {
    return this.velocity.copy();
  }

  getDamage(): number {
    return this.config.damage;
  }

  getSize(): number {
    return this.config.size;
  }

  getRemainingLifetime(): number {
    return this.currentLifetime;
  }

  getLifetimePercent(): number {
    return this.currentLifetime / this.config.lifetime;
  }
}
