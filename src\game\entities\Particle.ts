/**
 * Particle system with object pooling for visual effects
 */

import { Poolable } from '@/engine/core/ObjectPool';
import { Vector2 } from '@/utils/math/Vector2';
import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';

export interface ParticleConfig {
  lifetime: number;
  startSize: number;
  endSize: number;
  startColor: string;
  endColor: string;
  startAlpha: number;
  endAlpha: number;
  gravity: Vector2;
  drag: number;
  rotation: number;
  rotationSpeed: number;
  shape: 'circle' | 'square' | 'triangle';
}

export class Particle implements Poolable {
  public position: Vector2 = new Vector2(0, 0);
  public velocity: Vector2 = new Vector2(0, 0);
  public config: ParticleConfig = {
    lifetime: 1,
    startSize: 4,
    endSize: 0,
    startColor: '#ffffff',
    endColor: '#ffffff',
    startAlpha: 1,
    endAlpha: 0,
    gravity: new Vector2(0, 0),
    drag: 0,
    rotation: 0,
    rotationSpeed: 0,
    shape: 'circle'
  };

  public currentLifetime: number = 0;
  public maxLifetime: number = 1;
  
  private active: boolean = false;

  constructor() {
    this.reset();
  }

  initialize(
    x: number,
    y: number,
    velocity: Vector2,
    config: Partial<ParticleConfig>
  ): void {
    this.position.set(x, y);
    this.velocity.setFrom(velocity);
    
    // Merge config
    Object.assign(this.config, config);
    
    this.maxLifetime = this.config.lifetime;
    this.currentLifetime = this.maxLifetime;
    this.active = true;
  }

  update(deltaTime: number): void {
    if (!this.active) return;

    // Update lifetime
    this.currentLifetime -= deltaTime;
    if (this.currentLifetime <= 0) {
      this.active = false;
      return;
    }

    // Apply gravity
    this.velocity = this.velocity.add(this.config.gravity.multiply(deltaTime));

    // Apply drag
    if (this.config.drag > 0) {
      const dragForce = this.velocity.multiply(-this.config.drag * deltaTime);
      this.velocity = this.velocity.add(dragForce);
    }

    // Update position
    this.position = this.position.add(this.velocity.multiply(deltaTime));

    // Update rotation
    this.config.rotation += this.config.rotationSpeed * deltaTime;
  }

  render(renderer: CanvasRenderer): void {
    if (!this.active) return;

    const t = 1 - (this.currentLifetime / this.maxLifetime); // 0 to 1 progress

    // Interpolate properties
    const size = this.lerp(this.config.startSize, this.config.endSize, t);
    const alpha = this.lerp(this.config.startAlpha, this.config.endAlpha, t);
    const color = this.interpolateColor(this.config.startColor, this.config.endColor, t);

    // Set alpha
    renderer.setGlobalAlpha(alpha);

    // Apply rotation if needed
    if (this.config.rotation !== 0) {
      renderer.pushTransform();
      renderer.translate(this.position.x, this.position.y);
      renderer.rotate(this.config.rotation);
      renderer.translate(-this.position.x, -this.position.y);
    }

    // Draw particle based on shape
    switch (this.config.shape) {
      case 'circle':
        renderer.drawCircle(this.position.x, this.position.y, size / 2, color);
        break;
      case 'square':
        renderer.drawRect(
          this.position.x - size / 2,
          this.position.y - size / 2,
          size,
          size,
          color
        );
        break;
      case 'triangle':
        this.drawTriangle(renderer, this.position.x, this.position.y, size, color);
        break;
    }

    // Restore transform
    if (this.config.rotation !== 0) {
      renderer.popTransform();
    }

    renderer.setGlobalAlpha(1);
  }

  private drawTriangle(renderer: CanvasRenderer, x: number, y: number, size: number, color: string): void {
    const ctx = renderer.getContext();
    const halfSize = size / 2;
    
    ctx.fillStyle = color;
    ctx.beginPath();
    ctx.moveTo(x, y - halfSize);
    ctx.lineTo(x - halfSize, y + halfSize);
    ctx.lineTo(x + halfSize, y + halfSize);
    ctx.closePath();
    ctx.fill();
  }

  private lerp(start: number, end: number, t: number): number {
    return start + (end - start) * t;
  }

  private interpolateColor(startColor: string, endColor: string, t: number): string {
    // Simple color interpolation (assumes hex colors)
    if (startColor === endColor) return startColor;
    
    // For now, return start color (can be enhanced with proper color interpolation)
    return t < 0.5 ? startColor : endColor;
  }

  // Poolable interface
  reset(): void {
    this.position.set(0, 0);
    this.velocity.set(0, 0);
    this.currentLifetime = 0;
    this.maxLifetime = 1;
    this.active = false;
    
    // Reset config to defaults
    this.config = {
      lifetime: 1,
      startSize: 4,
      endSize: 0,
      startColor: '#ffffff',
      endColor: '#ffffff',
      startAlpha: 1,
      endAlpha: 0,
      gravity: new Vector2(0, 0),
      drag: 0,
      rotation: 0,
      rotationSpeed: 0,
      shape: 'circle'
    };
  }

  isActive(): boolean {
    return this.active;
  }

  setActive(active: boolean): void {
    this.active = active;
  }

  // Utility methods
  getPosition(): Vector2 {
    return this.position.copy();
  }

  getLifetimePercent(): number {
    return 1 - (this.currentLifetime / this.maxLifetime);
  }

  getRemainingLifetime(): number {
    return this.currentLifetime;
  }
}

/**
 * Particle emitter for creating particle effects
 */
export class ParticleEmitter {
  private particles: Particle[] = [];
  private emissionRate: number = 10; // particles per second
  private emissionTimer: number = 0;
  private isEmitting: boolean = false;
  
  public position: Vector2 = new Vector2(0, 0);
  public emissionConfig: Partial<ParticleConfig> = {};
  public velocityRange: { min: Vector2; max: Vector2 } = {
    min: new Vector2(-50, -50),
    max: new Vector2(50, 50)
  };

  constructor(
    private particlePool: any, // ObjectPool<Particle>
    emissionRate: number = 10
  ) {
    this.emissionRate = emissionRate;
  }

  startEmission(): void {
    this.isEmitting = true;
  }

  stopEmission(): void {
    this.isEmitting = false;
  }

  setPosition(x: number, y: number): void {
    this.position.set(x, y);
  }

  setEmissionRate(rate: number): void {
    this.emissionRate = rate;
  }

  setVelocityRange(minVel: Vector2, maxVel: Vector2): void {
    this.velocityRange.min = minVel;
    this.velocityRange.max = maxVel;
  }

  update(deltaTime: number): void {
    // Update emission
    if (this.isEmitting && this.emissionRate > 0) {
      this.emissionTimer += deltaTime;
      const emissionInterval = 1 / this.emissionRate;
      
      while (this.emissionTimer >= emissionInterval) {
        this.emitParticle();
        this.emissionTimer -= emissionInterval;
      }
    }

    // Update particles
    for (let i = this.particles.length - 1; i >= 0; i--) {
      const particle = this.particles[i];
      particle.update(deltaTime);
      
      if (!particle.isActive()) {
        this.particlePool.release(particle);
        this.particles.splice(i, 1);
      }
    }
  }

  private emitParticle(): void {
    const particle = this.particlePool.acquire();

    // Object pool now guarantees a return value (graceful degradation)
    // Random velocity within range
    const velocity = new Vector2(
      this.lerp(this.velocityRange.min.x, this.velocityRange.max.x, Math.random()),
      this.lerp(this.velocityRange.min.y, this.velocityRange.max.y, Math.random())
    );

    particle.initialize(
      this.position.x,
      this.position.y,
      velocity,
      this.emissionConfig
    );

    this.particles.push(particle);
  }

  private lerp(start: number, end: number, t: number): number {
    return start + (end - start) * t;
  }

  render(renderer: CanvasRenderer): void {
    for (const particle of this.particles) {
      particle.render(renderer);
    }
  }

  getParticleCount(): number {
    return this.particles.length;
  }

  clear(): void {
    for (const particle of this.particles) {
      this.particlePool.release(particle);
    }
    this.particles = [];
  }
}
