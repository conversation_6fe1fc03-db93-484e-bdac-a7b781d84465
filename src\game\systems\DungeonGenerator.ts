/**
 * Procedural dungeon generation system
 */

import { Vector2 } from '@/utils/math/Vector2';

export interface Room {
  id: number;
  x: number;
  y: number;
  width: number;
  height: number;
  center: Vector2;
  type: 'normal' | 'start' | 'boss' | 'treasure' | 'shop';
  connections: number[];
  enemies: Vector2[];
  loot: Vector2[];
  visited: boolean;
}

export interface Corridor {
  start: Vector2;
  end: Vector2;
  width: number;
  points: Vector2[];
}

export interface DungeonTile {
  type: 'wall' | 'floor' | 'door' | 'void';
  roomId?: number;
  isCorridorTile?: boolean;
}

export interface DungeonConfig {
  width: number;
  height: number;
  roomCount: number;
  minRoomSize: number;
  maxRoomSize: number;
  corridorWidth: number;
  maxAttempts: number;
}

export class DungeonGenerator {
  private config: DungeonConfig;
  private rooms: Room[] = [];
  private corridors: Corridor[] = [];
  private tiles: DungeonTile[][] = [];
  private roomIdCounter: number = 0;

  constructor(config: Partial<DungeonConfig> = {}) {
    this.config = {
      width: 100,
      height: 100,
      roomCount: 8,
      minRoomSize: 8,
      maxRoomSize: 16,
      corridorWidth: 3,
      maxAttempts: 100,
      ...config
    };
  }

  generateDungeon(): { rooms: Room[]; corridors: Corridor[]; tiles: DungeonTile[][] } {
    this.reset();
    this.initializeTiles();
    this.generateRooms();
    this.connectRooms();
    this.generateCorridors();
    this.placeTiles();
    this.populateRooms();
    
    return {
      rooms: this.rooms,
      corridors: this.corridors,
      tiles: this.tiles
    };
  }

  private reset(): void {
    this.rooms = [];
    this.corridors = [];
    this.tiles = [];
    this.roomIdCounter = 0;
  }

  private initializeTiles(): void {
    this.tiles = [];
    for (let y = 0; y < this.config.height; y++) {
      this.tiles[y] = [];
      for (let x = 0; x < this.config.width; x++) {
        this.tiles[y][x] = { type: 'void' };
      }
    }
  }

  private generateRooms(): void {
    let attempts = 0;
    
    while (this.rooms.length < this.config.roomCount && attempts < this.config.maxAttempts) {
      const room = this.generateRandomRoom();
      
      if (this.isValidRoomPlacement(room)) {
        this.rooms.push(room);
      }
      
      attempts++;
    }

    // Ensure we have at least one room
    if (this.rooms.length === 0) {
      const centerRoom = this.createRoom(
        Math.floor(this.config.width / 2) - 5,
        Math.floor(this.config.height / 2) - 5,
        10,
        10,
        'start'
      );
      this.rooms.push(centerRoom);
    }

    // Assign room types
    this.assignRoomTypes();
  }

  private generateRandomRoom(): Room {
    const width = this.randomInt(this.config.minRoomSize, this.config.maxRoomSize);
    const height = this.randomInt(this.config.minRoomSize, this.config.maxRoomSize);
    const x = this.randomInt(1, this.config.width - width - 1);
    const y = this.randomInt(1, this.config.height - height - 1);
    
    return this.createRoom(x, y, width, height);
  }

  private createRoom(x: number, y: number, width: number, height: number, type: Room['type'] = 'normal'): Room {
    return {
      id: this.roomIdCounter++,
      x,
      y,
      width,
      height,
      center: new Vector2(x + width / 2, y + height / 2),
      type,
      connections: [],
      enemies: [],
      loot: [],
      visited: false
    };
  }

  private isValidRoomPlacement(room: Room): boolean {
    // Check bounds
    if (room.x < 1 || room.y < 1 || 
        room.x + room.width >= this.config.width - 1 || 
        room.y + room.height >= this.config.height - 1) {
      return false;
    }

    // Check overlap with existing rooms (with buffer)
    for (const existingRoom of this.rooms) {
      if (this.roomsOverlap(room, existingRoom, 2)) {
        return false;
      }
    }

    return true;
  }

  private roomsOverlap(room1: Room, room2: Room, buffer: number = 0): boolean {
    return !(room1.x + room1.width + buffer < room2.x ||
             room2.x + room2.width + buffer < room1.x ||
             room1.y + room1.height + buffer < room2.y ||
             room2.y + room2.height + buffer < room1.y);
  }

  private assignRoomTypes(): void {
    if (this.rooms.length === 0) return;

    // First room is always start
    this.rooms[0].type = 'start';

    // Last room is boss (if we have enough rooms)
    if (this.rooms.length > 1) {
      this.rooms[this.rooms.length - 1].type = 'boss';
    }

    // Assign special room types
    for (let i = 1; i < this.rooms.length - 1; i++) {
      const rand = Math.random();
      if (rand < 0.1) {
        this.rooms[i].type = 'treasure';
      } else if (rand < 0.2) {
        this.rooms[i].type = 'shop';
      }
    }
  }

  private connectRooms(): void {
    if (this.rooms.length < 2) return;

    // Create minimum spanning tree for room connections
    const connected = new Set<number>();
    const unconnected = new Set(this.rooms.map(room => room.id));
    
    // Start with the first room
    const startRoom = this.rooms[0];
    connected.add(startRoom.id);
    unconnected.delete(startRoom.id);

    while (unconnected.size > 0) {
      let closestDistance = Infinity;
      let closestPair: [Room, Room] | null = null;

      // Find closest unconnected room to any connected room
      for (const connectedId of connected) {
        const connectedRoom = this.rooms.find(r => r.id === connectedId)!;
        
        for (const unconnectedId of unconnected) {
          const unconnectedRoom = this.rooms.find(r => r.id === unconnectedId)!;
          const distance = connectedRoom.center.distance(unconnectedRoom.center);
          
          if (distance < closestDistance) {
            closestDistance = distance;
            closestPair = [connectedRoom, unconnectedRoom];
          }
        }
      }

      if (closestPair) {
        const [room1, room2] = closestPair;
        room1.connections.push(room2.id);
        room2.connections.push(room1.id);
        connected.add(room2.id);
        unconnected.delete(room2.id);
      }
    }

    // Add some extra connections for loops
    const extraConnections = Math.floor(this.rooms.length * 0.3);
    for (let i = 0; i < extraConnections; i++) {
      const room1 = this.rooms[this.randomInt(0, this.rooms.length - 1)];
      const room2 = this.rooms[this.randomInt(0, this.rooms.length - 1)];
      
      if (room1.id !== room2.id && !room1.connections.includes(room2.id)) {
        room1.connections.push(room2.id);
        room2.connections.push(room1.id);
      }
    }
  }

  private generateCorridors(): void {
    const processedConnections = new Set<string>();

    for (const room of this.rooms) {
      for (const connectionId of room.connections) {
        const connectionKey = [room.id, connectionId].sort().join('-');
        
        if (!processedConnections.has(connectionKey)) {
          const connectedRoom = this.rooms.find(r => r.id === connectionId)!;
          const corridor = this.createCorridor(room, connectedRoom);
          this.corridors.push(corridor);
          processedConnections.add(connectionKey);
        }
      }
    }
  }

  private createCorridor(room1: Room, room2: Room): Corridor {
    const start = room1.center.copy();
    const end = room2.center.copy();
    
    // Create L-shaped corridor
    const points: Vector2[] = [start];
    
    // Choose whether to go horizontal first or vertical first
    if (Math.random() < 0.5) {
      // Horizontal first
      points.push(new Vector2(end.x, start.y));
    } else {
      // Vertical first
      points.push(new Vector2(start.x, end.y));
    }
    
    points.push(end);

    return {
      start,
      end,
      width: this.config.corridorWidth,
      points
    };
  }

  private placeTiles(): void {
    // Place room tiles
    for (const room of this.rooms) {
      for (let y = room.y; y < room.y + room.height; y++) {
        for (let x = room.x; x < room.x + room.width; x++) {
          if (x === room.x || x === room.x + room.width - 1 ||
              y === room.y || y === room.y + room.height - 1) {
            this.tiles[y][x] = { type: 'wall', roomId: room.id };
          } else {
            this.tiles[y][x] = { type: 'floor', roomId: room.id };
          }
        }
      }
    }

    // Place corridor tiles
    for (const corridor of this.corridors) {
      for (let i = 0; i < corridor.points.length - 1; i++) {
        const start = corridor.points[i];
        const end = corridor.points[i + 1];
        this.drawCorridorSegment(start, end, corridor.width);
      }
    }

    // Place doors
    this.placeDoors();
  }

  private drawCorridorSegment(start: Vector2, end: Vector2, width: number): void {
    const halfWidth = Math.floor(width / 2);
    
    if (start.x === end.x) {
      // Vertical corridor
      const minY = Math.min(start.y, end.y);
      const maxY = Math.max(start.y, end.y);
      
      for (let y = minY; y <= maxY; y++) {
        for (let x = start.x - halfWidth; x <= start.x + halfWidth; x++) {
          if (this.isValidTilePosition(x, y)) {
            this.tiles[y][x] = { type: 'floor', isCorridorTile: true };
          }
        }
      }
    } else {
      // Horizontal corridor
      const minX = Math.min(start.x, end.x);
      const maxX = Math.max(start.x, end.x);
      
      for (let x = minX; x <= maxX; x++) {
        for (let y = start.y - halfWidth; y <= start.y + halfWidth; y++) {
          if (this.isValidTilePosition(x, y)) {
            this.tiles[y][x] = { type: 'floor', isCorridorTile: true };
          }
        }
      }
    }
  }

  private placeDoors(): void {
    // Find connections between rooms and corridors
    for (const room of this.rooms) {
      const roomBounds = {
        left: room.x,
        right: room.x + room.width - 1,
        top: room.y,
        bottom: room.y + room.height - 1
      };

      // Check room perimeter for corridor connections
      for (let x = roomBounds.left; x <= roomBounds.right; x++) {
        // Top wall
        if (this.isValidTilePosition(x, roomBounds.top - 1) && 
            this.tiles[roomBounds.top - 1][x]?.isCorridorTile) {
          this.tiles[roomBounds.top][x] = { type: 'door', roomId: room.id };
        }
        // Bottom wall
        if (this.isValidTilePosition(x, roomBounds.bottom + 1) && 
            this.tiles[roomBounds.bottom + 1][x]?.isCorridorTile) {
          this.tiles[roomBounds.bottom][x] = { type: 'door', roomId: room.id };
        }
      }

      for (let y = roomBounds.top; y <= roomBounds.bottom; y++) {
        // Left wall
        if (this.isValidTilePosition(roomBounds.left - 1, y) && 
            this.tiles[y][roomBounds.left - 1]?.isCorridorTile) {
          this.tiles[y][roomBounds.left] = { type: 'door', roomId: room.id };
        }
        // Right wall
        if (this.isValidTilePosition(roomBounds.right + 1, y) && 
            this.tiles[y][roomBounds.right + 1]?.isCorridorTile) {
          this.tiles[y][roomBounds.right] = { type: 'door', roomId: room.id };
        }
      }
    }
  }

  private populateRooms(): void {
    for (const room of this.rooms) {
      this.populateRoom(room);
    }
  }

  private populateRoom(room: Room): void {
    const floorTiles: Vector2[] = [];
    
    // Find all floor tiles in the room
    for (let y = room.y + 1; y < room.y + room.height - 1; y++) {
      for (let x = room.x + 1; x < room.x + room.width - 1; x++) {
        if (this.tiles[y][x].type === 'floor') {
          floorTiles.push(new Vector2(x, y));
        }
      }
    }

    // Place enemies based on room type
    if (room.type === 'normal') {
      const enemyCount = this.randomInt(2, 4);
      for (let i = 0; i < enemyCount && floorTiles.length > 0; i++) {
        const index = this.randomInt(0, floorTiles.length - 1);
        room.enemies.push(floorTiles.splice(index, 1)[0]);
      }
    } else if (room.type === 'boss') {
      // Boss room - place boss in center
      room.enemies.push(room.center.copy());
    }

    // Place loot
    if (room.type === 'treasure') {
      const lootCount = this.randomInt(2, 4);
      for (let i = 0; i < lootCount && floorTiles.length > 0; i++) {
        const index = this.randomInt(0, floorTiles.length - 1);
        room.loot.push(floorTiles.splice(index, 1)[0]);
      }
    } else if (room.type === 'normal' && Math.random() < 0.3) {
      // 30% chance for normal rooms to have loot
      room.loot.push(floorTiles[this.randomInt(0, floorTiles.length - 1)]);
    }
  }

  private isValidTilePosition(x: number, y: number): boolean {
    return x >= 0 && x < this.config.width && y >= 0 && y < this.config.height;
  }

  private randomInt(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  // Utility methods for game integration
  getRoomAt(x: number, y: number): Room | null {
    for (const room of this.rooms) {
      if (x >= room.x && x < room.x + room.width &&
          y >= room.y && y < room.y + room.height) {
        return room;
      }
    }
    return null;
  }

  getStartRoom(): Room | null {
    return this.rooms.find(room => room.type === 'start') || null;
  }

  getBossRoom(): Room | null {
    return this.rooms.find(room => room.type === 'boss') || null;
  }

  getTileAt(x: number, y: number): DungeonTile | null {
    if (!this.isValidTilePosition(x, y)) return null;
    return this.tiles[y][x];
  }

  isWalkable(x: number, y: number): boolean {
    const tile = this.getTileAt(x, y);
    return tile ? (tile.type === 'floor' || tile.type === 'door') : false;
  }
}
