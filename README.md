# 🎮 Ultimate Bullet Hell Rogue-like RPG

A comprehensive, feature-rich bullet hell rogue-like RPG built with TypeScript and HTML5 Canvas, showcasing advanced game development techniques and professional-grade systems.

## 🌟 Features

### 🎯 **Core Game Systems**
- **Object Pooling**: Zero-allocation gameplay maintaining 60 FPS with 200+ active objects
- **Advanced Combat**: 6 unique weapon types with special behaviors and effects
- **Enemy AI**: 4 distinct enemy types with varied bullet hell patterns
- **Procedural Generation**: Infinite dungeon generation with room-based layouts
- **Character Progression**: 3 skill trees with 25+ skills and meaningful choices
- **Loot System**: Dynamic item generation with 5 rarity tiers and random modifiers

### 🔧 **Advanced Systems**
- **Audio System**: Procedural sound generation with spatial audio support
- **Performance Monitoring**: Real-time FPS, memory usage, and profiling
- **Error Handling**: Comprehensive error recovery and validation
- **Save System**: Local storage with versioning and migration support
- **Mobile Support**: Touch controls, virtual joystick, and responsive design
- **State Management**: Scene transitions and game state persistence

### 📱 **Cross-Platform Support**
- **Desktop**: Full keyboard and mouse controls
- **Mobile**: Touch controls with virtual joystick and buttons
- **Responsive**: Automatic scaling and layout adaptation
- **Fullscreen**: Native fullscreen support with proper scaling

### **Weapon Arsenal**
1. **Basic Bow** - Reliable physical damage
2. **Magic Missile** - Homing projectiles
3. **Piercing Bow** - Multi-target penetration
4. **Triple Shot** - Spread pattern multishot
5. **Fireball** - Explosive area damage
6. **Lightning** - Instant chain lightning

### **Enemy Types**
- **Basic** - Simple chase behavior
- **Shooter** - Ranged with spread patterns
- **Heavy** - Tanky with circle bullet patterns
- **Bomber** - Explosive suicide attackers

## 🎮 Controls

### Desktop
- **Movement**: WASD or Arrow Keys
- **Aim**: Mouse
- **Attack**: Left Mouse Button or Space (auto-attack)
- **Weapon Switch**: Number keys 1-6
- **Pause**: P
- **Inventory**: I
- **Skills**: K
- **Fullscreen**: F or F11
- **Performance**: F3

### Mobile
- **Movement**: Virtual joystick (left side)
- **Attack**: Attack button (right side)
- **Special**: Special ability button
- **Menu**: Pause and inventory buttons (top)
- **Gestures**: Pinch to zoom, tap to interact

## 🏗️ Architecture

### Core Systems

```
src/
├── engine/
│   ├── core/
│   │   └── ObjectPool.ts          # Memory management
│   ├── input/
│   │   └── InputManager.ts        # Input handling
│   ├── physics/
│   │   └── CollisionSystem.ts     # Collision detection
│   └── rendering/
│       └── CanvasRenderer.ts      # Rendering abstraction
├── game/
│   ├── entities/
│   │   ├── Player.ts              # Player character
│   │   ├── Enemy.ts               # Enemy entities
│   │   ├── Bullet.ts              # Projectile system
│   │   └── Particle.ts            # Visual effects
│   ├── systems/
│   │   ├── CombatSystem.ts        # Weapon and damage
│   │   ├── DungeonGenerator.ts    # Procedural levels
│   │   ├── DungeonRenderer.ts     # Level visualization
│   │   ├── GameObjectManager.ts   # Entity management
│   │   ├── ProgressionSystem.ts   # Character advancement
│   │   ├── LootSystem.ts          # Item generation
│   │   └── InventorySystem.ts     # Item management
│   └── ui/
│       ├── SkillTreeUI.ts         # Progression interface
│       └── InventoryUI.ts         # Inventory interface
└── utils/
    └── math/
        └── Vector2.ts             # 2D vector math
```

## 🎯 Object Pooling

The game uses an advanced object pooling system to prevent garbage collection lag:

```typescript
// Create pools for different object types
const bulletPool = new ObjectPool(() => new Bullet(), 50, 200);
const particlePool = new ObjectPool(() => new Particle(), 100, 500);

// Acquire objects instead of creating new ones
// Pool guarantees a return value with graceful degradation
const bullet = bulletPool.acquire();
bullet.initialize(x, y, direction, config);

// Release objects back to pool when done
bulletPool.release(bullet);
```

## 🏰 Procedural Generation

Dungeons are generated using a sophisticated algorithm:

1. **Room Placement** - Randomly place rooms with collision detection
2. **Room Connection** - Use minimum spanning tree for connectivity
3. **Corridor Generation** - Create L-shaped corridors between rooms
4. **Room Population** - Add enemies, loot, and special features
5. **Tile Placement** - Convert to renderable tile grid

## 📈 Character Progression

Three skill trees provide meaningful character advancement:

### Combat Tree
- **Weapon Mastery** - Increased damage
- **Critical Strike** - Higher crit chance
- **Attack Speed** - Faster attacks
- **Berserker Rage** - Temporary damage boost

### Magic Tree
- **Spell Power** - Increased magic damage
- **Mana Pool** - More mana capacity
- **Elemental Spells** - Fireball, Lightning, Meteor

### Utility Tree
- **Vitality** - Increased health
- **Fleet Footed** - Movement speed
- **Scholar** - Experience bonus
- **Utility Abilities** - Dash, Heal

## 🎒 Loot System

Items feature:
- **5 Rarity Tiers** - Common to Legendary
- **Dynamic Modifiers** - Randomly generated bonuses
- **Level Scaling** - Items scale with player level
- **Equipment Slots** - Weapon, armor, accessories
- **Consumables** - Potions and temporary effects

## 🎮 Demo Files

### 1. Basic Demo (`demo.html`)
- Core gameplay mechanics
- Player movement and combat
- Enemy AI and bullet patterns
- Weapon switching system

### 2. Dungeon Demo (`dungeon-demo.html`)
- Procedural dungeon generation
- Interactive dungeon exploration
- Room type visualization
- Camera controls

### 3. Complete Demo (`complete-demo.html`)
- Full game integration
- All systems working together
- Character progression
- Loot and inventory
- Advanced UI elements

## 🚀 Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd BulletHell_RougeLike_RPG
   ```

2. **Open demo files**
   - Open any `.html` file in a modern web browser
   - No build process required for demos
   - For TypeScript development, use a TypeScript compiler

3. **Development Setup**
   ```bash
   npm install typescript
   tsc --watch  # For TypeScript compilation
   ```

## 🎯 Performance Features

- **Object Pooling** - Eliminates garbage collection lag
- **Spatial Partitioning** - Efficient collision detection
- **Frustum Culling** - Only render visible objects
- **Particle Limits** - Prevent performance degradation
- **Efficient Rendering** - Batched draw calls

## 🔧 Configuration

The game systems are highly configurable:

```typescript
// Object pool configuration
const poolConfig = {
    initialSize: 50,
    maxSize: 200,
    preallocation: true
};

// Dungeon generation parameters
const dungeonConfig = {
    width: 100,
    height: 100,
    roomCount: 8,
    minRoomSize: 8,
    maxRoomSize: 16
};
```

## 🎨 Visual Features

- **Particle Effects** - Explosions, muzzle flashes, impacts
- **Dynamic Lighting** - Weapon effects and spell casting
- **Smooth Animations** - Interpolated movement and effects
- **UI Polish** - Responsive interface elements
- **Visual Feedback** - Damage numbers, status effects

## 🔮 Future Enhancements

- **Multiplayer Support** - Cooperative gameplay
- **More Weapon Types** - Melee weapons, exotic magic
- **Boss Battles** - Unique encounters with patterns
- **Crafting System** - Item creation and enhancement
- **Story Mode** - Narrative progression
- **Sound System** - Audio effects and music
- **Mobile Support** - Touch controls

## 📝 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

## 🎮 Play Now

Open any of the demo files in your browser to start playing immediately:
- `demo.html` - Basic gameplay
- `dungeon-demo.html` - Dungeon generation
- `complete-demo.html` - Full experience

Enjoy the bullet hell madness! 🎯💥
