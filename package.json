{"name": "ultimate-bullet-hell-rpg", "version": "1.0.0", "description": "A comprehensive bullet hell rogue-like RPG built with TypeScript and HTML5 Canvas, featuring advanced game systems including object pooling, procedural generation, character progression, and mobile support.", "main": "index.html", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "preview": "vite preview", "serve": "vite preview", "serve-dist": "vite preview", "clean": "rm -rf dist", "start": "npm run dev", "deploy": "npm run build:prod && echo \"Ready for deployment\"", "lint": "eslint src --ext .ts,.js", "lint:fix": "eslint src --ext .ts,.js --fix", "format": "prettier --write \"src/**/*.{ts,js,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,js,json,md}\"", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "prepare": "husky install", "pre-commit": "lint-staged"}, "keywords": ["game", "bullet-hell", "rpg", "rogue-like", "typescript", "canvas", "html5", "procedural-generation", "object-pooling", "mobile-game", "web-game", "javascript-game", "2d-game", "indie-game", "browser-game"], "author": {"name": "Ultimate Game Developer", "email": "<EMAIL>", "url": "https://github.com/username"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/username/ultimate-bullet-hell-rpg.git"}, "bugs": {"url": "https://github.com/username/ultimate-bullet-hell-rpg/issues"}, "homepage": "https://username.github.io/ultimate-bullet-hell-rpg", "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "husky": "^8.0.0", "lint-staged": "^15.0.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vite-plugin-html": "^3.2.0", "terser": "^5.0.0", "serve": "^14.0.0", "vitest": "^1.0.0", "@vitest/ui": "^1.0.0", "@vitest/coverage-v8": "^1.0.0", "jsdom": "^23.0.0"}, "dependencies": {}, "lint-staged": {"*.{ts,js}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "gameFeatures": {"core": ["Object Pooling System", "Advanced Combat System", "Enemy AI & Bullet Hell", "Procedural Dungeon Generation", "Character Progression", "Loot & Equipment System"], "advanced": ["Audio System with Spatial Audio", "Performance Monitoring", "Error Handling & Validation", "Mobile Support & Touch Controls", "Responsive Design", "Game State Management"], "tools": ["Map Editor", "Performance Profiler", "Asset Optimizer", "Build System"]}, "demos": {"basic": "demo.html", "dungeon": "dungeon-demo.html", "complete": "complete-demo.html", "ultimate": "ultimate-demo.html", "editor": "map-editor.html"}}