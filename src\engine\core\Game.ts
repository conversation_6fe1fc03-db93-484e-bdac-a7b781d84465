/**
 * Core Game class that manages the main game loop and coordinates all systems
 *
 * The Game class serves as the central coordinator for all game systems including
 * rendering, input handling, audio, scene management, and asset loading. It manages
 * the game loop, handles system initialization, and provides a unified interface
 * for game state management.
 *
 * @example
 * ```typescript
 * const game = new Game({
 *   renderer: new CanvasRenderer(canvas, config),
 *   inputManager: new InputManager(canvas),
 *   audioManager: new AudioManager(),
 *   sceneManager: new SceneManager(),
 *   assetManager: new AssetManager(),
 *   config: gameConfig
 * });
 *
 * await game.initialize();
 * game.start();
 * ```
 *
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */

import { AudioManager } from '@/engine/audio/AudioManager';
import { AssetManager } from '@/engine/core/AssetManager';
import { PerformanceMonitor } from '@/engine/core/PerformanceMonitor';
import { SceneManager } from '@/engine/core/SceneManager';
import { Time } from '@/engine/core/Time';
import { InputManager } from '@/engine/input/InputManager';
import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';

/**
 * Configuration interface for the main game instance
 *
 * Defines all configurable aspects of the game including canvas settings,
 * debug options, and mobile-specific configurations.
 */
export interface GameConfig {
  /** Canvas and rendering configuration */
  canvas: {
    /** Canvas width in pixels */
    width: number;
    /** Canvas height in pixels */
    height: number;
    /** Target frames per second (30-120) */
    targetFPS: number;
    /** Device pixel ratio for high-DPI displays */
    pixelRatio: number;
  };
  /** Debug and development configuration */
  debug: {
    /** Whether to display FPS counter */
    showFPS: boolean;
    /** Whether to render collision boundaries */
    showColliders: boolean;
    /** Whether to log performance metrics */
    logPerformance: boolean;
  };
  /** Mobile device specific configuration */
  mobile: {
    /** Whether to enable touch-based controls */
    enableTouchControls: boolean;
    /** Whether to scale game to fit screen */
    scaleToFit: boolean;
  };
}

/**
 * Interface defining all core game systems
 *
 * Contains references to all major subsystems that the game requires
 * to function, including rendering, input, audio, scenes, and assets.
 */
export interface GameSystems {
  /** Rendering system for drawing to canvas */
  renderer: CanvasRenderer;
  /** Input handling system for keyboard/mouse/touch */
  inputManager: InputManager;
  /** Audio system for music and sound effects */
  audioManager: AudioManager;
  /** Scene management system for game states */
  sceneManager: SceneManager;
  /** Asset loading and management system */
  assetManager: AssetManager;
  /** Game configuration object */
  config: GameConfig;
}

/**
 * Main Game class that orchestrates all game systems and manages the game loop
 *
 * This class implements a fixed timestep game loop with variable rendering,
 * ensuring consistent physics and game logic regardless of frame rate.
 * It coordinates all major game systems and provides lifecycle management.
 */
export class Game {
  /** Core rendering system */
  private renderer: CanvasRenderer;
  /** Input handling system */
  private inputManager: InputManager;
  /** Audio management system */
  private audioManager: AudioManager;
  /** Scene management system */
  private sceneManager: SceneManager;
  /** Asset loading and management system */
  private assetManager: AssetManager;
  /** Game configuration */
  private config: GameConfig;

  /** Time management utility */
  private time: Time;
  /** Performance monitoring and profiling */
  private performanceMonitor: PerformanceMonitor;

  /** Whether the game loop is currently running */
  private isRunning: boolean = false;
  /** Whether the game is currently paused */
  private isPaused: boolean = false;
  /** Current animation frame request ID */
  private animationFrameId: number = 0;

  /** Timestamp of the last frame */
  private lastFrameTime: number = 0;
  /** Accumulated time for fixed timestep updates */
  private accumulator: number = 0;
  /** Fixed timestep for consistent physics (60 FPS) */
  private readonly fixedTimeStep: number = 1000 / 60;

  /**
   * Creates a new Game instance with the provided systems
   *
   * @param systems - Object containing all required game systems
   * @throws {Error} If any required system is missing or invalid
   */
  constructor(systems: GameSystems) {
    this.renderer = systems.renderer;
    this.inputManager = systems.inputManager;
    this.audioManager = systems.audioManager;
    this.sceneManager = systems.sceneManager;
    this.assetManager = systems.assetManager;
    this.config = systems.config;

    this.time = new Time();
    // Use lightweight mode in production or when performance logging is disabled
    const lightweightMode = !this.config.debug.logPerformance || !this.config.debug.showFPS;
    this.performanceMonitor = new PerformanceMonitor(this.config.debug.logPerformance, lightweightMode);

    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    // Handle window focus/blur for pause/resume
    window.addEventListener('focus', () => this.resume());
    window.addEventListener('blur', () => this.pause());

    // Handle visibility change
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.pause();
      } else {
        this.resume();
      }
    });
  }

  start(): void {
    if (this.isRunning) return;

    this.isRunning = true;
    this.isPaused = false;
    this.lastFrameTime = performance.now();

    console.log('🎮 Game started');
    this.gameLoop();
  }

  pause(): void {
    if (!this.isRunning || this.isPaused) return;

    this.isPaused = true;
    this.audioManager.pauseAll();
    console.log('⏸️ Game paused');
  }

  resume(): void {
    if (!this.isRunning || !this.isPaused) return;

    this.isPaused = false;
    this.lastFrameTime = performance.now();
    this.audioManager.resumeAll();
    console.log('▶️ Game resumed');
  }

  stop(): void {
    if (!this.isRunning) return;

    this.isRunning = false;
    this.isPaused = false;

    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = 0;
    }

    console.log('⏹️ Game stopped');
  }

  private gameLoop = (): void => {
    if (!this.isRunning) return;

    this.animationFrameId = requestAnimationFrame(this.gameLoop);

    if (this.isPaused) return;

    const currentTime = performance.now();
    const frameTime = currentTime - this.lastFrameTime;
    this.lastFrameTime = currentTime;

    // Performance monitoring
    this.performanceMonitor.startFrame();

    // Update time
    this.time.update(frameTime);

    // Fixed timestep with accumulator for consistent physics
    this.accumulator += frameTime;

    // Prevent spiral of death
    if (this.accumulator > 250) {
      this.accumulator = 250;
    }

    // Fixed timestep updates
    while (this.accumulator >= this.fixedTimeStep) {
      this.fixedUpdate(this.fixedTimeStep / 1000);
      this.accumulator -= this.fixedTimeStep;
    }

    // Variable timestep update
    this.update(frameTime / 1000);

    // Render with interpolation
    const alpha = this.accumulator / this.fixedTimeStep;
    this.render(alpha);

    // Performance monitoring
    this.performanceMonitor.endFrame();

    // Debug information
    if (this.config.debug.showFPS) {
      this.renderDebugInfo();
    }
  };

  private fixedUpdate(deltaTime: number): void {
    // Update input
    this.inputManager.update();

    // Update current scene with fixed timestep
    this.sceneManager.fixedUpdate(deltaTime);
  }

  private update(deltaTime: number): void {
    // Update current scene with variable timestep
    this.sceneManager.update(deltaTime);

    // Update audio
    this.audioManager.update(deltaTime);
  }

  private render(alpha: number): void {
    // Clear the canvas
    this.renderer.clear();

    // Render current scene
    this.sceneManager.render(this.renderer, alpha);

    // Present the frame
    this.renderer.present();
  }

  private renderDebugInfo(): void {
    const fps = this.performanceMonitor.getFPS();
    const frameTime = this.performanceMonitor.getAverageFrameTime();

    this.renderer.drawText(
      `FPS: ${fps.toFixed(1)} | Frame: ${frameTime.toFixed(2)}ms`,
      10, 30, '16px monospace', '#00ff00'
    );
  }

  // Getters for systems
  getRenderer(): CanvasRenderer { return this.renderer; }
  getInputManager(): InputManager { return this.inputManager; }
  getAudioManager(): AudioManager { return this.audioManager; }
  getSceneManager(): SceneManager { return this.sceneManager; }
  getAssetManager(): AssetManager { return this.assetManager; }
  getTime(): Time { return this.time; }
  getConfig(): GameConfig { return this.config; }

  destroy(): void {
    this.stop();
    this.inputManager.destroy();
    this.audioManager.destroy();
    this.renderer.destroy();
    console.log('🗑️ Game destroyed');
  }
}
