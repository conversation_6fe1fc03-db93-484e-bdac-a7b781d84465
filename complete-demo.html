<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bullet Hell Rogue-like RPG - Complete Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #1a1a1a;
            color: #ffffff;
            font-family: 'Courier New', monospace;
            overflow: hidden;
            user-select: none;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        
        #gameCanvas {
            border: 2px solid #444;
            background: #000;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }
        
        #ui {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 5px;
            font-size: 12px;
            line-height: 1.6;
            max-width: 300px;
        }
        
        .stat-bar {
            width: 200px;
            height: 8px;
            background: #333;
            margin: 2px 0;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .stat-fill {
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .health { background: #4CAF50; }
        .mana { background: #2196F3; }
        .exp { background: #FFD700; }
        
        #notifications {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 250px;
        }
        
        .notification {
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <canvas id="gameCanvas" width="1024" height="768"></canvas>
    
    <div id="ui">
        <div><strong>🎮 Bullet Hell RPG - Complete Demo</strong></div>
        <div>Level: <span id="level">1</span> | XP: <span id="experience">0</span>/<span id="expRequired">100</span></div>
        <div class="stat-bar"><div class="stat-fill exp" id="expBar" style="width: 0%"></div></div>
        
        <div>Health: <span id="health">100</span>/<span id="maxHealth">100</span></div>
        <div class="stat-bar"><div class="stat-fill health" id="healthBar" style="width: 100%"></div></div>
        
        <div>Mana: <span id="mana">50</span>/<span id="maxMana">50</span></div>
        <div class="stat-bar"><div class="stat-fill mana" id="manaBar" style="width: 100%"></div></div>
        
        <div>Gold: <span id="gold">0</span></div>
        <div>Weapon: <span id="currentWeapon">Basic Bow</span></div>
        <div>Enemies: <span id="enemyCount">0</span></div>
        <div id="fps">FPS: --</div>
    </div>
    
    <div id="notifications"></div>
    
    <div class="controls">
        <div><strong>Controls:</strong></div>
        <div>WASD: Move | Mouse: Aim | Click: Attack</div>
        <div>Space: Auto-attack | 1-6: Switch weapons</div>
        <div>I: Inventory | K: Skills | P: Pause</div>
        <div>E: Pickup items | R: Reload area</div>
    </div>

    <script>
        // Core game classes (simplified versions of our TypeScript systems)
        
        class Vector2 {
            constructor(x = 0, y = 0) {
                this.x = x;
                this.y = y;
            }
            
            add(other) {
                return new Vector2(this.x + other.x, this.y + other.y);
            }
            
            subtract(other) {
                return new Vector2(this.x - other.x, this.y - other.y);
            }
            
            multiply(scalar) {
                return new Vector2(this.x * scalar, this.y * scalar);
            }
            
            normalize() {
                const length = Math.sqrt(this.x * this.x + this.y * this.y);
                if (length === 0) return new Vector2(0, 0);
                return new Vector2(this.x / length, this.y / length);
            }
            
            distance(other) {
                const dx = this.x - other.x;
                const dy = this.y - other.y;
                return Math.sqrt(dx * dx + dy * dy);
            }
            
            copy() {
                return new Vector2(this.x, this.y);
            }
            
            rotate(angle) {
                const cos = Math.cos(angle);
                const sin = Math.sin(angle);
                return new Vector2(
                    this.x * cos - this.y * sin,
                    this.x * sin + this.y * cos
                );
            }
            
            magnitude() {
                return Math.sqrt(this.x * this.x + this.y * this.y);
            }
            
            isZero() {
                return this.x === 0 && this.y === 0;
            }
        }

        class GameObject {
            constructor(x, y) {
                this.position = new Vector2(x, y);
                this.velocity = new Vector2(0, 0);
                this.size = 16;
                this.active = true;
                this.health = 100;
                this.maxHealth = 100;
            }
            
            update(deltaTime) {
                this.position = this.position.add(this.velocity.multiply(deltaTime));
            }
            
            render(ctx) {
                // Override in subclasses
            }
            
            checkCollision(other) {
                return this.position.distance(other.position) < (this.size + other.size) / 2;
            }
            
            takeDamage(damage) {
                this.health -= damage;
                if (this.health <= 0) {
                    this.health = 0;
                    this.active = false;
                    return true; // Died
                }
                return false;
            }
        }

        class Player extends GameObject {
            constructor(x, y) {
                super(x, y);
                this.size = 32;
                this.health = 100;
                this.maxHealth = 100;
                this.mana = 50;
                this.maxMana = 50;
                this.speed = 300;
                this.shootCooldown = 0;
                this.autoAttack = false;
                this.level = 1;
                this.experience = 0;
                this.gold = 0;
                this.currentWeapon = 0;
                this.isInvulnerable = false;
                this.invulnerabilityTimer = 0;
                
                this.weapons = [
                    { name: 'Basic Bow', damage: 25, rate: 5, color: '#8B4513', speed: 600, size: 8, manaCost: 0 },
                    { name: 'Magic Missile', damage: 20, rate: 4, color: '#4A90E2', speed: 400, size: 10, homing: true, manaCost: 5 },
                    { name: 'Piercing Bow', damage: 35, rate: 3, color: '#FFD700', speed: 700, size: 10, piercing: true, manaCost: 0 },
                    { name: 'Triple Shot', damage: 30, rate: 2, color: '#8B0000', speed: 650, size: 9, multishot: 3, manaCost: 0 },
                    { name: 'Fireball', damage: 50, rate: 1.5, color: '#FF4500', speed: 300, size: 16, explosive: true, manaCost: 15 },
                    { name: 'Lightning', damage: 40, rate: 4, color: '#FFFF00', speed: 1200, size: 6, instant: true, manaCost: 10 }
                ];
                
                this.inventory = [];
                this.equipment = {
                    weapon: null,
                    armor: null,
                    accessory: null
                };
            }
            
            update(deltaTime, input, mousePos) {
                // Movement
                const movement = new Vector2(0, 0);
                if (input.keys.has('KeyW') || input.keys.has('ArrowUp')) movement.y -= 1;
                if (input.keys.has('KeyS') || input.keys.has('ArrowDown')) movement.y += 1;
                if (input.keys.has('KeyA') || input.keys.has('ArrowLeft')) movement.x -= 1;
                if (input.keys.has('KeyD') || input.keys.has('ArrowRight')) movement.x += 1;
                
                // Normalize diagonal movement
                if (movement.x !== 0 && movement.y !== 0) {
                    movement.x *= 0.707;
                    movement.y *= 0.707;
                }
                
                this.velocity = movement.multiply(this.speed);
                super.update(deltaTime);
                
                // Keep player in bounds
                this.position.x = Math.max(this.size/2, Math.min(1024 - this.size/2, this.position.x));
                this.position.y = Math.max(this.size/2, Math.min(768 - this.size/2, this.position.y));
                
                // Weapon switching
                for (let i = 1; i <= 6; i++) {
                    if (input.keysPressed.has(`Digit${i}`) && i <= this.weapons.length) {
                        this.currentWeapon = i - 1;
                        game.showNotification(`Switched to ${this.weapons[this.currentWeapon].name}`);
                    }
                }
                
                // Auto-attack toggle
                if (input.keysPressed.has('Space')) {
                    this.autoAttack = !this.autoAttack;
                    game.showNotification(`Auto-attack: ${this.autoAttack ? 'ON' : 'OFF'}`);
                }
                
                // Shooting
                this.shootCooldown = Math.max(0, this.shootCooldown - deltaTime);
                
                const weapon = this.weapons[this.currentWeapon];
                if ((input.mouseDown || this.autoAttack) && this.shootCooldown <= 0) {
                    if (this.mana >= weapon.manaCost) {
                        this.shoot(mousePos);
                        this.shootCooldown = 1 / weapon.rate;
                        this.mana = Math.max(0, this.mana - weapon.manaCost);
                    }
                }
                
                // Mana regeneration
                this.mana = Math.min(this.maxMana, this.mana + 5 * deltaTime);
                
                // Invulnerability
                if (this.isInvulnerable) {
                    this.invulnerabilityTimer -= deltaTime;
                    if (this.invulnerabilityTimer <= 0) {
                        this.isInvulnerable = false;
                    }
                }
            }
            
            shoot(targetPos) {
                const weapon = this.weapons[this.currentWeapon];
                const direction = new Vector2(
                    targetPos.x - this.position.x,
                    targetPos.y - this.position.y
                ).normalize();
                
                const shotCount = weapon.multishot || 1;
                for (let i = 0; i < shotCount; i++) {
                    let shootDirection = direction;
                    
                    // Apply spread for multishot
                    if (shotCount > 1) {
                        const spread = Math.PI / 8; // 22.5 degrees
                        const angle = (i - (shotCount - 1) / 2) * (spread / (shotCount - 1));
                        shootDirection = direction.rotate(angle);
                    }
                    
                    const bullet = new Bullet(
                        this.position.x,
                        this.position.y,
                        shootDirection.multiply(weapon.speed),
                        weapon.color,
                        true
                    );
                    
                    // Apply weapon properties
                    bullet.damage = weapon.damage;
                    bullet.size = weapon.size;
                    bullet.piercing = weapon.piercing || false;
                    bullet.homing = weapon.homing || false;
                    bullet.explosive = weapon.explosive || false;
                    bullet.instant = weapon.instant || false;
                    
                    game.addBullet(bullet);
                }
                
                // Create muzzle flash
                game.createMuzzleFlash(this.position.x, this.position.y);
            }
            
            takeDamage(damage) {
                if (this.isInvulnerable) return false;
                
                const actualDamage = Math.max(1, damage);
                this.health -= actualDamage;
                
                // Start invulnerability
                this.isInvulnerable = true;
                this.invulnerabilityTimer = 1.0;
                
                if (this.health <= 0) {
                    this.health = 0;
                    this.active = false;
                    return true;
                }
                
                return false;
            }
            
            gainExperience(amount) {
                this.experience += amount;
                const expRequired = this.level * 100;
                
                if (this.experience >= expRequired) {
                    this.levelUp();
                }
            }
            
            levelUp() {
                this.level++;
                this.experience = 0;
                
                // Increase stats
                this.maxHealth += 20;
                this.maxMana += 10;
                this.health = this.maxHealth;
                this.mana = this.maxMana;
                
                game.showNotification(`🎉 Level Up! Now level ${this.level}`, 'level');
            }
            
            addGold(amount) {
                this.gold += amount;
                game.showNotification(`+${amount} gold`, 'gold');
            }
            
            render(ctx) {
                // Flash when invulnerable
                let alpha = 1;
                if (this.isInvulnerable) {
                    alpha = 0.5 + 0.5 * Math.sin(Date.now() * 0.02);
                }
                
                ctx.globalAlpha = alpha;
                
                // Draw player
                ctx.fillStyle = '#4CAF50';
                ctx.fillRect(
                    this.position.x - this.size/2,
                    this.position.y - this.size/2,
                    this.size,
                    this.size
                );
                
                // Draw facing direction
                const mousePos = game.input.mousePos;
                const direction = new Vector2(
                    mousePos.x - this.position.x,
                    mousePos.y - this.position.y
                ).normalize();
                
                const indicatorEnd = this.position.add(direction.multiply(this.size/2 + 10));
                ctx.strokeStyle = '#ffffff';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(this.position.x, this.position.y);
                ctx.lineTo(indicatorEnd.x, indicatorEnd.y);
                ctx.stroke();
                
                ctx.globalAlpha = 1;
            }
        }

        class Enemy extends GameObject {
            constructor(x, y, type = 'basic') {
                super(x, y);
                this.type = type;
                this.shootCooldown = 0;
                this.aiTimer = 0;
                this.patternPhase = 0;
                this.hitFlashTimer = 0;
                
                this.initializeByType(type);
            }
            
            initializeByType(type) {
                switch (type) {
                    case 'basic':
                        this.size = 24;
                        this.health = 30;
                        this.maxHealth = 30;
                        this.speed = 100;
                        this.damage = 15;
                        this.shootInterval = 1.5;
                        this.color = '#ff6b6b';
                        this.experienceValue = 10;
                        this.goldValue = 5;
                        break;
                    case 'shooter':
                        this.size = 20;
                        this.health = 20;
                        this.maxHealth = 20;
                        this.speed = 80;
                        this.damage = 12;
                        this.shootInterval = 0.8;
                        this.color = '#ff9800';
                        this.experienceValue = 15;
                        this.goldValue = 8;
                        break;
                    case 'heavy':
                        this.size = 36;
                        this.health = 80;
                        this.maxHealth = 80;
                        this.speed = 60;
                        this.damage = 25;
                        this.shootInterval = 2;
                        this.color = '#9c27b0';
                        this.experienceValue = 30;
                        this.goldValue = 15;
                        break;
                }
            }
            
            update(deltaTime, player) {
                this.aiTimer += deltaTime;
                const distanceToPlayer = this.position.distance(player.position);
                
                // Simple AI: move towards player
                const direction = new Vector2(
                    player.position.x - this.position.x,
                    player.position.y - this.position.y
                ).normalize();
                
                this.velocity = direction.multiply(this.speed);
                super.update(deltaTime);
                
                // Shooting
                this.shootCooldown -= deltaTime;
                if (this.shootCooldown <= 0 && distanceToPlayer < 300) {
                    this.shoot(player);
                    this.shootCooldown = this.shootInterval;
                }
                
                // Update timers
                if (this.hitFlashTimer > 0) {
                    this.hitFlashTimer -= deltaTime;
                }
            }
            
            shoot(player) {
                const direction = new Vector2(
                    player.position.x - this.position.x,
                    player.position.y - this.position.y
                ).normalize();
                
                const bullet = new Bullet(
                    this.position.x,
                    this.position.y,
                    direction.multiply(200),
                    this.color,
                    false
                );
                bullet.damage = this.damage;
                bullet.size = 6;
                game.addBullet(bullet);
            }
            
            takeDamage(damage) {
                this.health -= damage;
                this.hitFlashTimer = 0.1;
                
                if (this.health <= 0) {
                    this.health = 0;
                    this.active = false;
                    
                    // Drop loot
                    game.player.gainExperience(this.experienceValue);
                    game.player.addGold(this.goldValue);
                    
                    // Chance to drop items
                    if (Math.random() < 0.3) {
                        game.dropRandomItem(this.position);
                    }
                    
                    return true;
                }
                
                return false;
            }
            
            render(ctx) {
                // Flash white when hit
                let color = this.color;
                if (this.hitFlashTimer > 0) {
                    color = '#ffffff';
                }
                
                ctx.fillStyle = color;
                
                // Draw different shapes based on type
                switch (this.type) {
                    case 'basic':
                        ctx.beginPath();
                        ctx.arc(this.position.x, this.position.y, this.size/2, 0, Math.PI * 2);
                        ctx.fill();
                        break;
                    case 'shooter':
                        ctx.fillRect(this.position.x - this.size/2, this.position.y - this.size/2, this.size, this.size);
                        break;
                    case 'heavy':
                        // Draw hexagon
                        ctx.beginPath();
                        for (let i = 0; i < 6; i++) {
                            const angle = (Math.PI * 2 * i) / 6;
                            const x = this.position.x + Math.cos(angle) * this.size/2;
                            const y = this.position.y + Math.sin(angle) * this.size/2;
                            if (i === 0) ctx.moveTo(x, y);
                            else ctx.lineTo(x, y);
                        }
                        ctx.closePath();
                        ctx.fill();
                        break;
                }
                
                // Health bar
                const barWidth = this.size + 5;
                const barHeight = 3;
                const barX = this.position.x - barWidth/2;
                const barY = this.position.y - this.size/2 - 8;
                
                ctx.fillStyle = '#333';
                ctx.fillRect(barX, barY, barWidth, barHeight);
                
                const healthPercent = this.health / this.maxHealth;
                ctx.fillStyle = this.color;
                ctx.fillRect(barX, barY, barWidth * healthPercent, barHeight);
            }
        }

        class Bullet extends GameObject {
            constructor(x, y, velocity, color, isPlayerBullet) {
                super(x, y);
                this.velocity = velocity;
                this.size = 6;
                this.color = color;
                this.isPlayerBullet = isPlayerBullet;
                this.lifetime = 3;
                this.damage = 10;
                this.piercing = false;
                this.homing = false;
                this.explosive = false;
                this.instant = false;
                this.hitTargets = new Set();
            }
            
            update(deltaTime) {
                // Homing behavior
                if (this.homing && this.isPlayerBullet) {
                    const closestEnemy = this.findClosestEnemy();
                    if (closestEnemy && this.position.distance(closestEnemy.position) < 200) {
                        const targetDirection = new Vector2(
                            closestEnemy.position.x - this.position.x,
                            closestEnemy.position.y - this.position.y
                        ).normalize();
                        
                        const currentDirection = this.velocity.normalize();
                        const homingForce = targetDirection.subtract(currentDirection).multiply(3 * deltaTime);
                        this.velocity = this.velocity.add(homingForce).normalize().multiply(this.velocity.magnitude());
                    }
                }
                
                super.update(deltaTime);
                this.lifetime -= deltaTime;
                
                if (this.lifetime <= 0) {
                    if (this.explosive) {
                        game.createExplosion(this.position.x, this.position.y);
                    }
                    this.active = false;
                } else if (this.position.x < -50 || this.position.x > 1074 ||
                          this.position.y < -50 || this.position.y > 818) {
                    this.active = false;
                }
            }
            
            findClosestEnemy() {
                let closest = null;
                let closestDistance = Infinity;
                
                for (const enemy of game.enemies) {
                    if (!enemy.active || this.hitTargets.has(enemy)) continue;
                    const distance = this.position.distance(enemy.position);
                    if (distance < closestDistance) {
                        closest = enemy;
                        closestDistance = distance;
                    }
                }
                
                return closest;
            }
            
            render(ctx) {
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.position.x, this.position.y, this.size/2, 0, Math.PI * 2);
                ctx.fill();
                
                // Draw trail for fast bullets
                if (this.velocity.magnitude() > 400) {
                    const trailLength = Math.min(20, this.velocity.magnitude() / 20);
                    const trailDirection = this.velocity.normalize().multiply(-trailLength);
                    const trailEnd = this.position.add(trailDirection);
                    
                    ctx.globalAlpha = 0.5;
                    ctx.strokeStyle = this.color;
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(this.position.x, this.position.y);
                    ctx.lineTo(trailEnd.x, trailEnd.y);
                    ctx.stroke();
                    ctx.globalAlpha = 1;
                }
            }
        }

        // Main Game Class
        class Game {
            constructor() {
                this.canvas = document.getElementById('gameCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.player = new Player(512, 384);
                this.enemies = [];
                this.bullets = [];
                this.particles = [];
                this.droppedItems = [];
                
                this.input = {
                    keys: new Set(),
                    keysPressed: new Set(),
                    mousePos: new Vector2(512, 384),
                    mouseDown: false
                };
                
                this.lastTime = 0;
                this.fps = 0;
                this.frameCount = 0;
                this.fpsTimer = 0;
                this.gameTime = 0;
                
                this.setupInput();
                this.spawnEnemies();
                this.updateUI();
            }
            
            setupInput() {
                window.addEventListener('keydown', (e) => {
                    if (!this.input.keys.has(e.code)) {
                        this.input.keysPressed.add(e.code);
                    }
                    this.input.keys.add(e.code);
                    e.preventDefault();
                });
                
                window.addEventListener('keyup', (e) => {
                    this.input.keys.delete(e.code);
                    e.preventDefault();
                });
                
                this.canvas.addEventListener('mousemove', (e) => {
                    const rect = this.canvas.getBoundingClientRect();
                    this.input.mousePos.x = e.clientX - rect.left;
                    this.input.mousePos.y = e.clientY - rect.top;
                });
                
                this.canvas.addEventListener('mousedown', (e) => {
                    this.input.mouseDown = true;
                    e.preventDefault();
                });
                
                this.canvas.addEventListener('mouseup', (e) => {
                    this.input.mouseDown = false;
                    e.preventDefault();
                });
            }
            
            spawnEnemies() {
                const enemyTypes = ['basic', 'shooter', 'heavy'];
                for (let i = 0; i < 5; i++) {
                    const edge = Math.floor(Math.random() * 4);
                    let x, y;
                    switch (edge) {
                        case 0: x = Math.random() * 1024; y = -50; break;
                        case 1: x = 1074; y = Math.random() * 768; break;
                        case 2: x = Math.random() * 1024; y = 818; break;
                        case 3: x = -50; y = Math.random() * 768; break;
                    }
                    
                    const type = enemyTypes[Math.floor(Math.random() * enemyTypes.length)];
                    this.enemies.push(new Enemy(x, y, type));
                }
            }
            
            addBullet(bullet) {
                this.bullets.push(bullet);
            }
            
            createMuzzleFlash(x, y) {
                for (let i = 0; i < 5; i++) {
                    const angle = Math.random() * Math.PI * 2;
                    const speed = 50 + Math.random() * 50;
                    const particle = {
                        x: x,
                        y: y,
                        vx: Math.cos(angle) * speed,
                        vy: Math.sin(angle) * speed,
                        life: 0.1 + Math.random() * 0.1,
                        maxLife: 0.1 + Math.random() * 0.1,
                        size: 2 + Math.random() * 2,
                        color: '#ffff00'
                    };
                    this.particles.push(particle);
                }
            }
            
            createExplosion(x, y) {
                for (let i = 0; i < 15; i++) {
                    const angle = Math.random() * Math.PI * 2;
                    const speed = 50 + Math.random() * 100;
                    const particle = {
                        x: x,
                        y: y,
                        vx: Math.cos(angle) * speed,
                        vy: Math.sin(angle) * speed,
                        life: 0.5 + Math.random() * 0.5,
                        maxLife: 0.5 + Math.random() * 0.5,
                        size: 2 + Math.random() * 4,
                        color: Math.random() > 0.5 ? '#FF4500' : '#FFD700'
                    };
                    this.particles.push(particle);
                }
                
                // Damage enemies in explosion radius
                const explosionRadius = 60;
                this.enemies.forEach(enemy => {
                    if (enemy.active) {
                        const distance = Math.sqrt((enemy.position.x - x) ** 2 + (enemy.position.y - y) ** 2);
                        if (distance <= explosionRadius) {
                            enemy.takeDamage(30);
                        }
                    }
                });
            }
            
            dropRandomItem(position) {
                const items = [
                    { name: 'Health Potion', icon: '🧪', color: '#ff6b6b', value: 25 },
                    { name: 'Mana Potion', icon: '💙', color: '#2196F3', value: 20 },
                    { name: 'Gold Coin', icon: '🪙', color: '#FFD700', value: 10 },
                    { name: 'Magic Scroll', icon: '📜', color: '#9c27b0', value: 50 }
                ];
                
                const item = items[Math.floor(Math.random() * items.length)];
                this.droppedItems.push({
                    ...item,
                    position: position.copy(),
                    lifetime: 30,
                    bobOffset: Math.random() * Math.PI * 2
                });
            }
            
            showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = 'notification';
                notification.textContent = message;
                
                if (type === 'level') {
                    notification.style.borderLeftColor = '#FFD700';
                } else if (type === 'gold') {
                    notification.style.borderLeftColor = '#FFD700';
                }
                
                document.getElementById('notifications').appendChild(notification);
                
                setTimeout(() => {
                    notification.remove();
                }, 3000);
            }
            
            update(deltaTime) {
                this.gameTime += deltaTime;
                
                // Clear frame-specific input
                this.input.keysPressed.clear();
                
                // Update player
                this.player.update(deltaTime, this.input, this.input.mousePos);
                
                // Update enemies
                this.enemies.forEach(enemy => {
                    if (enemy.active) {
                        enemy.update(deltaTime, this.player);
                    }
                });
                
                // Update bullets
                this.bullets.forEach(bullet => {
                    if (bullet.active) {
                        bullet.update(deltaTime);
                    }
                });
                
                // Update particles
                for (let i = this.particles.length - 1; i >= 0; i--) {
                    const particle = this.particles[i];
                    particle.life -= deltaTime;
                    particle.x += particle.vx * deltaTime;
                    particle.y += particle.vy * deltaTime;
                    particle.vy += 50 * deltaTime; // gravity
                    
                    if (particle.life <= 0) {
                        this.particles.splice(i, 1);
                    }
                }
                
                // Update dropped items
                for (let i = this.droppedItems.length - 1; i >= 0; i--) {
                    const item = this.droppedItems[i];
                    item.lifetime -= deltaTime;
                    item.bobOffset += deltaTime * 3;
                    
                    // Check pickup
                    if (this.player.position.distance(item.position) < 32) {
                        this.pickupItem(item);
                        this.droppedItems.splice(i, 1);
                    } else if (item.lifetime <= 0) {
                        this.droppedItems.splice(i, 1);
                    }
                }
                
                // Check collisions
                this.checkCollisions();
                
                // Remove inactive objects
                this.enemies = this.enemies.filter(enemy => enemy.active);
                this.bullets = this.bullets.filter(bullet => bullet.active);
                
                // Spawn new enemies
                const maxEnemies = Math.min(10, 3 + Math.floor(this.player.level / 2));
                if (this.enemies.length < maxEnemies) {
                    const edge = Math.floor(Math.random() * 4);
                    let x, y;
                    switch (edge) {
                        case 0: x = Math.random() * 1024; y = -50; break;
                        case 1: x = 1074; y = Math.random() * 768; break;
                        case 2: x = Math.random() * 1024; y = 818; break;
                        case 3: x = -50; y = Math.random() * 768; break;
                    }
                    
                    const enemyTypes = ['basic', 'shooter', 'heavy'];
                    let typeIndex = Math.floor(Math.random() * Math.min(enemyTypes.length, 1 + this.player.level / 3));
                    const type = enemyTypes[typeIndex];
                    
                    this.enemies.push(new Enemy(x, y, type));
                }
                
                // Update UI
                this.updateUI();
            }
            
            pickupItem(item) {
                switch (item.name) {
                    case 'Health Potion':
                        this.player.health = Math.min(this.player.maxHealth, this.player.health + 50);
                        this.showNotification('Health restored!');
                        break;
                    case 'Mana Potion':
                        this.player.mana = Math.min(this.player.maxMana, this.player.mana + 30);
                        this.showNotification('Mana restored!');
                        break;
                    case 'Gold Coin':
                        this.player.addGold(item.value);
                        break;
                    case 'Magic Scroll':
                        this.player.gainExperience(25);
                        this.showNotification('Gained experience!');
                        break;
                }
            }
            
            checkCollisions() {
                // Player bullets vs enemies
                this.bullets.forEach(bullet => {
                    if (bullet.isPlayerBullet && bullet.active) {
                        this.enemies.forEach(enemy => {
                            if (enemy.active && !bullet.hitTargets.has(enemy) && bullet.checkCollision(enemy)) {
                                enemy.takeDamage(bullet.damage);
                                bullet.hitTargets.add(enemy);
                                
                                if (bullet.explosive) {
                                    this.createExplosion(bullet.position.x, bullet.position.y);
                                }
                                
                                if (!bullet.piercing) {
                                    bullet.active = false;
                                }
                            }
                        });
                    }
                });
                
                // Enemy bullets vs player
                this.bullets.forEach(bullet => {
                    if (!bullet.isPlayerBullet && bullet.active) {
                        if (bullet.checkCollision(this.player)) {
                            this.player.takeDamage(bullet.damage);
                            bullet.active = false;
                            
                            if (!this.player.active) {
                                this.gameOver();
                            }
                        }
                    }
                });
            }
            
            updateUI() {
                document.getElementById('level').textContent = this.player.level;
                document.getElementById('experience').textContent = this.player.experience;
                document.getElementById('expRequired').textContent = this.player.level * 100;
                document.getElementById('health').textContent = Math.floor(this.player.health);
                document.getElementById('maxHealth').textContent = this.player.maxHealth;
                document.getElementById('mana').textContent = Math.floor(this.player.mana);
                document.getElementById('maxMana').textContent = this.player.maxMana;
                document.getElementById('gold').textContent = this.player.gold;
                document.getElementById('currentWeapon').textContent = this.player.weapons[this.player.currentWeapon].name;
                document.getElementById('enemyCount').textContent = this.enemies.length;
                
                // Update bars
                const healthPercent = (this.player.health / this.player.maxHealth) * 100;
                const manaPercent = (this.player.mana / this.player.maxMana) * 100;
                const expPercent = (this.player.experience / (this.player.level * 100)) * 100;
                
                document.getElementById('healthBar').style.width = healthPercent + '%';
                document.getElementById('manaBar').style.width = manaPercent + '%';
                document.getElementById('expBar').style.width = expPercent + '%';
            }
            
            gameOver() {
                alert(`Game Over! Final Level: ${this.player.level}\nGold Collected: ${this.player.gold}\nReloading...`);
                location.reload();
            }
            
            render() {
                // Clear canvas
                this.ctx.fillStyle = '#1a1a1a';
                this.ctx.fillRect(0, 0, 1024, 768);
                
                // Draw grid
                this.ctx.strokeStyle = '#333';
                this.ctx.lineWidth = 1;
                for (let x = 0; x < 1024; x += 64) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(x, 0);
                    this.ctx.lineTo(x, 768);
                    this.ctx.stroke();
                }
                for (let y = 0; y < 768; y += 64) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(0, y);
                    this.ctx.lineTo(1024, y);
                    this.ctx.stroke();
                }
                
                // Render particles (behind everything)
                this.particles.forEach(particle => {
                    const alpha = particle.life / particle.maxLife;
                    this.ctx.globalAlpha = alpha;
                    this.ctx.fillStyle = particle.color;
                    this.ctx.beginPath();
                    this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                    this.ctx.fill();
                    this.ctx.globalAlpha = 1;
                });
                
                // Render dropped items
                this.droppedItems.forEach(item => {
                    const bobY = item.position.y + Math.sin(item.bobOffset) * 3;
                    const alpha = Math.min(1, item.lifetime / 5); // Fade out in last 5 seconds
                    
                    this.ctx.globalAlpha = alpha;
                    this.ctx.font = '20px monospace';
                    this.ctx.fillText(item.icon, item.position.x - 10, bobY);
                    this.ctx.globalAlpha = 1;
                });
                
                // Render game objects
                this.bullets.forEach(bullet => bullet.render(this.ctx));
                this.enemies.forEach(enemy => enemy.render(this.ctx));
                this.player.render(this.ctx);
                
                // Draw crosshair
                const crosshairSize = 10;
                this.ctx.strokeStyle = '#ffffff';
                this.ctx.lineWidth = 2;
                this.ctx.beginPath();
                this.ctx.moveTo(this.input.mousePos.x - crosshairSize, this.input.mousePos.y);
                this.ctx.lineTo(this.input.mousePos.x + crosshairSize, this.input.mousePos.y);
                this.ctx.moveTo(this.input.mousePos.x, this.input.mousePos.y - crosshairSize);
                this.ctx.lineTo(this.input.mousePos.x, this.input.mousePos.y + crosshairSize);
                this.ctx.stroke();
            }
            
            gameLoop(currentTime) {
                const deltaTime = (currentTime - this.lastTime) / 1000;
                this.lastTime = currentTime;
                
                // Calculate FPS
                this.frameCount++;
                this.fpsTimer += deltaTime;
                if (this.fpsTimer >= 1) {
                    this.fps = this.frameCount;
                    this.frameCount = 0;
                    this.fpsTimer = 0;
                    document.getElementById('fps').textContent = `FPS: ${this.fps}`;
                }
                
                this.update(deltaTime);
                this.render();
                
                requestAnimationFrame((time) => this.gameLoop(time));
            }
            
            start() {
                this.lastTime = performance.now();
                this.gameLoop(this.lastTime);
            }
        }

        // Start the game
        const game = new Game();
        game.start();
    </script>
</body>
</html>
