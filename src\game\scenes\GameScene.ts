/**
 * Main game scene where the actual gameplay happens
 * Refactored to use component-based architecture for better maintainability
 */

import { BaseScene } from '@/engine/core/SceneManager';
import { InputManager } from '@/engine/input/InputManager';
import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';
import { Bullet } from '@/game/entities/Bullet';
import { Enemy } from '@/game/entities/Enemy';
import { Player } from '@/game/entities/Player';
import { InventorySystem } from '@/game/systems/InventorySystem';
import { LootSystem } from '@/game/systems/LootSystem';
import { ProgressionSystem } from '@/game/systems/ProgressionSystem';
import { Vector2 } from '@/utils/math/Vector2';

// Import new components
import { GameInputHandler, InputCallbacks } from './components/GameInputHandler';
import { GameEntities, GameLogicManager, GameState, GameSystems } from './components/GameLogicManager';
import { <PERSON><PERSON><PERSON><PERSON>, RenderableEntities, UIData } from './components/GameRenderer';

export class GameScene extends BaseScene {
  // Game systems (will be injected)
  private inputManager: InputManager | null = null;

  // Component-based architecture
  private gameLogicManager: GameLogicManager | null = null;
  private gameInputHandler: GameInputHandler | null = null;

  // Game state
  private camera: { x: number; y: number } = { x: 0, y: 0 };
  private showSkillTree: boolean = false;
  private showInventory: boolean = false;

  initialize(): void {
    console.log('🎮 Game scene initialized');
    this.setupComponents();
  }

  // Method to inject game systems
  setInputManager(inputManager: InputManager): void {
    this.inputManager = inputManager;
    this.setupComponents(); // Re-setup if input manager changes
  }

  private setupComponents(): void {
    if (!this.inputManager) return;

    // Initialize game systems
    const progressionSystem = new ProgressionSystem();
    const inventorySystem = new InventorySystem();
    const lootSystem = new LootSystem();
    const collisionSystem = new OptimizedCollisionSystem(64);

    // Initialize player
    const player = new Player(512, 384);

    // Create game entities
    const entities: GameEntities = {
      player,
      enemies: [],
      bullets: [],
      items: []
    };

    // Create game systems
    const systems: GameSystems = {
      progressionSystem,
      inventorySystem,
      lootSystem,
      collisionSystem
    };

    // Create initial game state
    const initialState: GameState = {
      score: 0,
      gameTime: 0,
      enemySpawnTimer: 0,
      enemySpawnInterval: 2.0,
      playerProgression: progressionSystem.createNewCharacter()
    };

    // Initialize game logic manager
    this.gameLogicManager = new GameLogicManager(entities, systems, initialState);

    // Setup input callbacks
    const inputCallbacks: InputCallbacks = {
      onBulletCreated: (bullet: Bullet) => this.gameLogicManager?.addBullet(bullet),
      onToggleSkillTree: () => this.showSkillTree = !this.showSkillTree,
      onToggleInventory: () => this.showInventory = !this.showInventory,
      onPause: () => console.log('Game paused') // TODO: Implement pause
    };

    // Initialize input handler
    this.gameInputHandler = new GameInputHandler(
      this.inputManager,
      player,
      this.camera,
      inputCallbacks
    );
  }

  fixedUpdate(deltaTime: number): void {
    if (!this.gameLogicManager || !this.gameInputHandler) return;

    // Process input
    const inputState = this.gameInputHandler.processInput(deltaTime);
    this.showSkillTree = inputState.showSkillTree;
    this.showInventory = inputState.showInventory;

    // Skip game logic updates if UI is open
    if (this.showSkillTree || this.showInventory) {
      return;
    }

    // Update game logic
    this.gameLogicManager.update(deltaTime);

    // Update camera
    this.gameLogicManager.updateCamera(this.camera, deltaTime);
    this.gameInputHandler.updateCamera(this.camera);
  }

  update(deltaTime: number): void {
    // Variable timestep updates for smooth animations
    if (this.gameInputHandler) {
      this.gameInputHandler.processSpecialAbilities();
    }
  }

  private updatePlayer(deltaTime: number): void {
    if (!this.inputManager || !this.player.isAlive) return;

    // Update player with input
    this.player.update(deltaTime, this.inputManager, null);

    // Handle shooting
    if (this.inputManager.isMouseButtonDown(0) || this.inputManager.isActionDown('attack')) {
      this.handlePlayerShooting();
    }

    // Update mouse world position for aiming
    const mousePos = this.inputManager.getMousePosition();
    this.mouseWorldPosition = new Vector2(
      mousePos.x + this.camera.x,
      mousePos.y + this.camera.y
    );
  }

  private handlePlayerShooting(): void {
    if (!this.player.isAlive) return;

    // Simple shooting cooldown
    const now = performance.now();
    if (now - this.player.lastAttackTime < 250) return; // 250ms cooldown

    this.player.lastAttackTime = now;

    // Create bullet
    const bullet = new Bullet();
    bullet.initialize(
      this.player.position.x,
      this.player.position.y,
      this.player.aimDirection,
      {
        damage: this.player.stats.damage,
        speed: 500,
        color: '#00ff00',
        size: 8
      },
      true // isPlayerBullet
    );

    this.bullets.push(bullet);
  }

  private handleUIInput(deltaTime: number): void {
    // Handle skill tree input
    if (this.showSkillTree) {
      // TODO: Implement skill tree navigation and selection
    }

    // Handle inventory input
    if (this.showInventory) {
      // TODO: Implement inventory navigation and item management
    }
  }

  private updateEnemies(deltaTime: number): void {
    for (let i = this.enemies.length - 1; i >= 0; i--) {
      const enemy = this.enemies[i];

      if (!enemy.isAlive) {
        // Handle enemy death
        this.handleEnemyDeath(enemy);
        this.enemies.splice(i, 1);
        continue;
      }

      // Update enemy AI and movement
      enemy.update(deltaTime, this.player, null);
    }
  }

  private handleEnemyDeath(enemy: Enemy): void {
    // Award experience
    const experience = enemy.getExperienceValue();
    this.score += experience;

    const leveledUp = this.progressionSystem.gainExperience(this.playerProgression, experience);
    if (leveledUp) {
      console.log(`🎉 Level up! Now level ${this.playerProgression.level}`);
      this.applyProgressionToPlayer();
    }

    // Generate loot
    const loot = this.lootSystem.generateLoot(this.playerProgression.level, enemy.config.aiType);
    if (loot) {
      loot.position = enemy.position.copy();
      this.items.push(loot);
    }
  }

  private updateItems(deltaTime: number): void {
    for (let i = this.items.length - 1; i >= 0; i--) {
      const item = this.items[i];

      // Check if player is close enough to pick up
      const distance = this.player.position.distance(item.position);
      if (distance < 32) { // Pickup range
        if (this.inventorySystem.addItem(item)) {
          this.items.splice(i, 1);
          console.log(`📦 Picked up: ${item.name}`);
        }
      }
    }
  }

  private updateBullets(deltaTime: number): void {
    for (let i = this.bullets.length - 1; i >= 0; i--) {
      const bullet = this.bullets[i];

      bullet.update(deltaTime);

      // Remove bullets that are inactive
      if (!bullet.isActive()) {
        this.bullets.splice(i, 1);
      }
    }
  }

  private handleCollisions(): void {
    this.collisionManager.resetCollisionChecks();

    // Reuse player spatial object to avoid allocation
    this.reusablePlayerSpatial.position = this.player.position;
    this.reusablePlayerSpatial.collisionRadius = this.player.config.collisionRadius;

    const enemySpatials: (SpatialObject & { enemy: Enemy })[] = this.enemies
      .filter(enemy => enemy.isAlive)
      .map(enemy => ({
        position: enemy.position,
        collisionRadius: enemy.config.collisionRadius,
        id: enemy.id || 'enemy',
        enemy
      }));

    const bulletSpatials: (SpatialObject & { bullet: Bullet, index: number })[] = this.bullets
      .filter(bullet => bullet.isPlayerBullet)
      .map((bullet, index) => ({
        position: bullet.getPosition(),
        collisionRadius: bullet.getSize() / 2,
        id: `bullet_${index}`,
        bullet,
        index
      }));

    // Player vs enemies collision
    this.collisionManager.checkCollisions(
      [this.reusablePlayerSpatial],
      enemySpatials,
      (player, enemyObj) => {
        this.player.takeDamage(enemyObj.enemy.stats.damage);
      }
    );

    // Bullets vs enemies collision
    const bulletsToRemove: number[] = [];
    this.collisionManager.checkCollisions(
      bulletSpatials,
      enemySpatials,
      (bulletObj, enemyObj) => {
        // Damage enemy
        enemyObj.enemy.takeDamage(bulletObj.bullet.getDamage());

        // Mark bullet for removal if not piercing
        if (!bulletObj.bullet.config.piercing) {
          bulletsToRemove.push(bulletObj.index);
        }
      }
    );

    // Remove bullets (in reverse order to maintain indices)
    bulletsToRemove.sort((a, b) => b - a);
    for (const index of bulletsToRemove) {
      this.bullets.splice(index, 1);
    }

    // Track collision performance
    this.lastCollisionChecks = this.collisionManager.getCollisionChecks();
  }

  private updateCamera(): void {
    // Simple camera follow with smoothing
    const targetX = this.player.position.x - 512;
    const targetY = this.player.position.y - 384;

    this.camera.x += (targetX - this.camera.x) * 0.1;
    this.camera.y += (targetY - this.camera.y) * 0.1;
  }

  update(deltaTime: number): void {
    this.gameTime += deltaTime;

    // Handle enemy spawning
    this.enemySpawnTimer += deltaTime;
    if (this.enemySpawnTimer >= this.enemySpawnInterval) {
      this.spawnRandomEnemy();
      this.enemySpawnTimer = 0;

      // Gradually increase spawn rate
      this.enemySpawnInterval = Math.max(0.5, this.enemySpawnInterval - 0.01);
    }
  }

  private spawnRandomEnemy(): void {
    // Spawn enemies around the player but not too close
    const angle = Math.random() * Math.PI * 2;
    const distance = 400 + Math.random() * 200;
    const x = this.player.position.x + Math.cos(angle) * distance;
    const y = this.player.position.y + Math.sin(angle) * distance;

    const types = ['basic', 'shooter', 'heavy'];
    const type = types[Math.floor(Math.random() * types.length)];

    this.spawnEnemy(x, y, type);
  }

  render(renderer: CanvasRenderer, alpha: number): void {
    if (!this.gameLogicManager) return;

    const entities = this.gameLogicManager.getEntities();
    const state = this.gameLogicManager.getState();

    // Prepare renderable entities
    const renderableEntities: RenderableEntities = {
      player: entities.player,
      enemies: entities.enemies,
      bullets: entities.bullets,
      items: entities.items
    };

    // Prepare UI data
    const uiData: UIData = {
      score: state.score,
      playerProgression: state.playerProgression,
      showSkillTree: this.showSkillTree,
      showInventory: this.showInventory,
      gameTime: state.gameTime
    };

    // Use the GameRenderer component
    GameRenderer.renderScene(renderer, renderableEntities, uiData, this.camera);
  }

  destroy(): void {
    console.log('🗑️ Game scene destroyed');
    this.gameLogicManager = null;
    this.gameInputHandler = null;
  }

  private renderDungeon(renderer: CanvasRenderer): void {
    // Draw a simple grid pattern for now
    const gridSize = 64;
    const startX = Math.floor(this.camera.x / gridSize) * gridSize;
    const startY = Math.floor(this.camera.y / gridSize) * gridSize;
    const endX = startX + 1024 + gridSize;
    const endY = startY + 768 + gridSize;

    renderer.setGlobalAlpha(0.1);
    for (let x = startX; x < endX; x += gridSize) {
      renderer.drawLine(x, startY, x, endY, '#666666', 1);
    }
    for (let y = startY; y < endY; y += gridSize) {
      renderer.drawLine(startX, y, endX, y, '#666666', 1);
    }
    renderer.setGlobalAlpha(1);
  }

  private renderPlayer(renderer: CanvasRenderer): void {
    if (this.player.isAlive) {
      this.player.render(renderer);
    }
  }

  private renderEnemies(renderer: CanvasRenderer): void {
    for (const enemy of this.enemies) {
      if (enemy.isAlive) {
        enemy.render(renderer);
      }
    }
  }

  private renderProjectiles(renderer: CanvasRenderer): void {
    for (const bullet of this.bullets) {
      if (bullet.isActive()) {
        bullet.render(renderer);
      }
    }
  }

  private renderItems(renderer: CanvasRenderer): void {
    for (const item of this.items) {
      // Draw item with glow effect based on rarity
      const glowColors = {
        'common': '#ffffff',
        'uncommon': '#00ff00',
        'rare': '#0080ff',
        'epic': '#8000ff',
        'legendary': '#ff8000'
      };

      const color = glowColors[item.rarity] || '#ffffff';

      // Draw glow
      renderer.setGlobalAlpha(0.5);
      renderer.drawCircle(item.position.x, item.position.y, 20, color);
      renderer.setGlobalAlpha(1);

      // Draw item
      renderer.drawRect(
        item.position.x - 8,
        item.position.y - 8,
        16,
        16,
        color
      );
    }
  }

  private renderParticles(renderer: CanvasRenderer): void {
    // TODO: Implement particle system rendering
  }

  private renderUI(renderer: CanvasRenderer): void {
    const { width, height } = renderer.getCanvasSize();

    // Health bar
    const healthBarWidth = 200;
    const healthBarHeight = 20;
    const healthBarX = 20;
    const healthBarY = 20;

    // Health bar background
    renderer.drawRect(healthBarX, healthBarY, healthBarWidth, healthBarHeight, '#333333');

    // Health bar fill
    const healthPercent = this.player.stats.health / this.player.stats.maxHealth;
    const healthFillWidth = (healthBarWidth - 4) * healthPercent;
    let healthColor = '#4CAF50';
    if (healthPercent < 0.3) healthColor = '#f44336';
    else if (healthPercent < 0.6) healthColor = '#ff9800';

    renderer.drawRect(healthBarX + 2, healthBarY + 2, healthFillWidth, healthBarHeight - 4, healthColor);

    // Health text
    renderer.drawText(
      `Health: ${this.player.stats.health}/${this.player.stats.maxHealth}`,
      healthBarX,
      healthBarY + healthBarHeight + 10,
      '14px monospace',
      '#ffffff'
    );

    // Score and level
    renderer.drawText(
      `Score: ${this.score}`,
      healthBarX,
      healthBarY + healthBarHeight + 30,
      '14px monospace',
      '#ffffff'
    );

    renderer.drawText(
      `Level: ${this.playerProgression.level}`,
      healthBarX,
      healthBarY + healthBarHeight + 50,
      '14px monospace',
      '#ffffff'
    );

    // Experience bar
    const expBarWidth = 200;
    const expBarHeight = 10;
    const expBarX = healthBarX;
    const expBarY = healthBarY + healthBarHeight + 70;

    renderer.drawRect(expBarX, expBarY, expBarWidth, expBarHeight, '#333333');

    const expPercent = this.playerProgression.experience / this.playerProgression.experienceToNext;
    const expFillWidth = (expBarWidth - 4) * expPercent;
    renderer.drawRect(expBarX + 2, expBarY + 2, expFillWidth, expBarHeight - 4, '#00ff00');

    // Enemy count
    renderer.drawText(
      `Enemies: ${this.enemies.length}`,
      healthBarX,
      healthBarY + healthBarHeight + 95,
      '14px monospace',
      '#ffffff'
    );

    // Controls
    renderer.drawText(
      'K: Skills | I: Inventory',
      healthBarX,
      healthBarY + healthBarHeight + 115,
      '12px monospace',
      '#cccccc'
    );

    // Performance info (debug)
    renderer.drawText(
      `Collision Checks: ${this.lastCollisionChecks}`,
      healthBarX,
      healthBarY + healthBarHeight + 135,
      '10px monospace',
      '#888888'
    );

    // Game time
    renderer.drawText(
      `Time: ${this.gameTime.toFixed(1)}s`,
      width - 120,
      20,
      '14px monospace',
      '#ffffff'
    );

    // Controls hint
    renderer.drawText(
      'WASD: Move • Mouse: Aim • Click: Attack • ESC: Menu',
      20,
      height - 30,
      '12px monospace',
      '#888888'
    );

    // Crosshair at mouse position
    if (this.inputManager) {
      const mousePos = this.inputManager.getMousePosition();
      const crosshairSize = 10;

      renderer.drawLine(
        mousePos.x - crosshairSize,
        mousePos.y,
        mousePos.x + crosshairSize,
        mousePos.y,
        '#ffffff',
        2
      );
      renderer.drawLine(
        mousePos.x,
        mousePos.y - crosshairSize,
        mousePos.x,
        mousePos.y + crosshairSize,
        '#ffffff',
        2
      );
    }

    // Game over screen
    if (!this.player.isAlive) {
      const gameOverText = 'GAME OVER';
      const restartText = 'Press R to restart';

      // Semi-transparent overlay
      renderer.setGlobalAlpha(0.7);
      renderer.drawRect(0, 0, width, height, '#000000');
      renderer.setGlobalAlpha(1);

      // Game over text
      renderer.drawText(
        gameOverText,
        width / 2 - 80,
        height / 2 - 20,
        '32px monospace',
        '#ff6b6b'
      );

      renderer.drawText(
        restartText,
        width / 2 - 100,
        height / 2 + 20,
        '16px monospace',
        '#ffffff'
      );

      renderer.drawText(
        `Final Score: ${this.score}`,
        width / 2 - 80,
        height / 2 + 50,
        '16px monospace',
        '#ffffff'
      );
    }
  }

  // Handle restart
  onEnter(): void {
    super.onEnter();

    // Handle restart if player is dead and R is pressed
    if (this.inputManager && !this.player.isAlive && this.inputManager.isKeyPressed('KeyR')) {
      this.restart();
    }
  }

  private restart(): void {
    this.setupSystems();
    this.setupPlayer();
    this.setupEnemies();
    this.bullets = [];
    this.items = [];
    this.score = 0;
    this.gameTime = 0;
    this.enemySpawnTimer = 0;
    this.enemySpawnInterval = 2.0;
    this.showSkillTree = false;
    this.showInventory = false;
  }

  private renderSkillTree(renderer: CanvasRenderer, width: number, height: number): void {
    // Semi-transparent overlay
    renderer.setGlobalAlpha(0.8);
    renderer.drawRect(0, 0, width, height, '#000000');
    renderer.setGlobalAlpha(1);

    // Title
    renderer.drawText(
      'Skill Tree (K to close)',
      width / 2 - 100,
      50,
      '24px monospace',
      '#ffffff'
    );

    // Player stats
    const stats = this.playerProgression.stats;
    const y = 100;
    const spacing = 25;

    renderer.drawText(`Level: ${this.playerProgression.level}`, 50, y, '16px monospace', '#ffffff');
    renderer.drawText(`Experience: ${this.playerProgression.experience}/${this.playerProgression.experienceToNext}`, 50, y + spacing, '16px monospace', '#ffffff');
    renderer.drawText(`Skill Points: ${this.playerProgression.skillPoints}`, 50, y + spacing * 2, '16px monospace', '#ffffff');

    renderer.drawText(`Health: ${stats.health}/${stats.maxHealth}`, 50, y + spacing * 4, '16px monospace', '#ffffff');
    renderer.drawText(`Mana: ${stats.mana}/${stats.maxMana}`, 50, y + spacing * 5, '16px monospace', '#ffffff');
    renderer.drawText(`Damage: ${stats.damage}`, 50, y + spacing * 6, '16px monospace', '#ffffff');
    renderer.drawText(`Speed: ${stats.speed}`, 50, y + spacing * 7, '16px monospace', '#ffffff');
    renderer.drawText(`Defense: ${stats.defense}`, 50, y + spacing * 8, '16px monospace', '#ffffff');
    renderer.drawText(`Critical Chance: ${(stats.criticalChance * 100).toFixed(1)}%`, 50, y + spacing * 9, '16px monospace', '#ffffff');
  }

  private renderInventory(renderer: CanvasRenderer, width: number, height: number): void {
    // Semi-transparent overlay
    renderer.setGlobalAlpha(0.8);
    renderer.drawRect(0, 0, width, height, '#000000');
    renderer.setGlobalAlpha(1);

    // Title
    renderer.drawText(
      'Inventory (I to close)',
      width / 2 - 100,
      50,
      '24px monospace',
      '#ffffff'
    );

    // Inventory grid
    const items = this.inventorySystem.getItems();
    const gridSize = 40;
    const startX = 50;
    const startY = 100;
    const cols = 10;

    for (let i = 0; i < 30; i++) { // 3 rows of 10
      const x = startX + (i % cols) * (gridSize + 5);
      const y = startY + Math.floor(i / cols) * (gridSize + 5);

      // Draw slot
      renderer.drawRect(x, y, gridSize, gridSize, '#333333');
      renderer.drawRect(x + 1, y + 1, gridSize - 2, gridSize - 2, '#222222');

      // Draw item if present
      if (i < items.length) {
        const item = items[i];
        const rarityColors = {
          'common': '#ffffff',
          'uncommon': '#00ff00',
          'rare': '#0080ff',
          'epic': '#8000ff',
          'legendary': '#ff8000'
        };

        const color = rarityColors[item.rarity] || '#ffffff';
        renderer.drawRect(x + 5, y + 5, gridSize - 10, gridSize - 10, color);

        // Item name below grid
        if (i === 0) { // Show first item details as example
          renderer.drawText(item.name, startX, startY + 200, '14px monospace', color);
          renderer.drawText(`Type: ${item.type}`, startX, startY + 220, '12px monospace', '#cccccc');
          renderer.drawText(`Rarity: ${item.rarity}`, startX, startY + 240, '12px monospace', '#cccccc');
        }
      }
    }
  }

  destroy(): void {
    console.log('🗑️ Game scene destroyed');
    this.enemies = [];
    this.bullets = [];
    this.items = [];
  }
}
