/**
 * Touch controls system for mobile devices
 * Provides virtual joystick, buttons, and gesture recognition
 */

import { Vector2 } from '@/utils/math/Vector2';

/**
 * Responsive design utility for mobile adaptation
 */
export class ResponsiveDesign {
  private static instance: ResponsiveDesign;

  private breakpoints = {
    mobile: 768,
    tablet: 1024,
    desktop: 1200
  };

  private currentBreakpoint: 'mobile' | 'tablet' | 'desktop' = 'desktop';
  private orientation: 'portrait' | 'landscape' = 'landscape';
  private pixelRatio: number = 1;

  private callbacks: Set<(info: DeviceInfo) => void> = new Set();

  private constructor() {
    this.updateDeviceInfo();
    this.setupEventListeners();
  }

  static getInstance(): ResponsiveDesign {
    if (!ResponsiveDesign.instance) {
      ResponsiveDesign.instance = new ResponsiveDesign();
    }
    return ResponsiveDesign.instance;
  }

  private setupEventListeners(): void {
    window.addEventListener('resize', () => this.updateDeviceInfo());
    window.addEventListener('orientationchange', () => {
      setTimeout(() => this.updateDeviceInfo(), 100);
    });
  }

  private updateDeviceInfo(): void {
    const width = window.innerWidth;
    const height = window.innerHeight;

    // Update breakpoint
    if (width <= this.breakpoints.mobile) {
      this.currentBreakpoint = 'mobile';
    } else if (width <= this.breakpoints.tablet) {
      this.currentBreakpoint = 'tablet';
    } else {
      this.currentBreakpoint = 'desktop';
    }

    // Update orientation
    this.orientation = width > height ? 'landscape' : 'portrait';

    // Update pixel ratio
    this.pixelRatio = window.devicePixelRatio || 1;

    // Notify callbacks
    const info = this.getDeviceInfo();
    for (const callback of this.callbacks) {
      callback(info);
    }
  }

  getDeviceInfo(): DeviceInfo {
    return {
      breakpoint: this.currentBreakpoint,
      orientation: this.orientation,
      pixelRatio: this.pixelRatio,
      width: window.innerWidth,
      height: window.innerHeight,
      isMobile: this.currentBreakpoint === 'mobile',
      isTablet: this.currentBreakpoint === 'tablet',
      isDesktop: this.currentBreakpoint === 'desktop',
      isTouchDevice: 'ontouchstart' in window,
      hasHover: window.matchMedia('(hover: hover)').matches
    };
  }

  onDeviceChange(callback: (info: DeviceInfo) => void): void {
    this.callbacks.add(callback);
  }

  offDeviceChange(callback: (info: DeviceInfo) => void): void {
    this.callbacks.delete(callback);
  }
}

export interface DeviceInfo {
  breakpoint: 'mobile' | 'tablet' | 'desktop';
  orientation: 'portrait' | 'landscape';
  pixelRatio: number;
  width: number;
  height: number;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isTouchDevice: boolean;
  hasHover: boolean;
}

export interface TouchControlConfig {
  joystick: {
    enabled: boolean;
    position: { x: number; y: number }; // Percentage of screen
    size: number; // Radius in pixels
    deadzone: number; // 0-1
    opacity: number; // 0-1
  };
  buttons: TouchButtonConfig[];
  gestures: {
    enabled: boolean;
    swipeThreshold: number;
    pinchThreshold: number;
    tapTimeout: number;
  };
}

export interface TouchButtonConfig {
  id: string;
  label: string;
  position: { x: number; y: number }; // Percentage of screen
  size: { width: number; height: number }; // Pixels
  action: string;
  opacity: number;
  visible: boolean;
}

export interface TouchInput {
  joystick: {
    active: boolean;
    direction: Vector2;
    magnitude: number;
  };
  buttons: Map<string, boolean>;
  gestures: {
    swipe: { active: boolean; direction: Vector2; velocity: number };
    pinch: { active: boolean; scale: number; center: Vector2 };
    tap: { active: boolean; position: Vector2; count: number };
  };
}

/**
 * Touch controls for mobile devices
 */
export class TouchControls {
  private canvas: HTMLCanvasElement;
  private config: TouchControlConfig;
  private input: TouchInput;

  private joystickElement: HTMLElement | null = null;
  private joystickKnob: HTMLElement | null = null;
  private buttonElements: Map<string, HTMLElement> = new Map();

  private activeTouches: Map<number, Touch> = new Map();
  private joystickTouch: number | null = null;
  private buttonTouches: Map<string, number> = new Map();

  // Gesture tracking
  private gestureStartTouches: Touch[] = [];
  private lastTapTime: number = 0;
  private tapCount: number = 0;

  private enabled: boolean = false;
  private visible: boolean = true;

  constructor(canvas: HTMLCanvasElement, config?: Partial<TouchControlConfig>) {
    this.canvas = canvas;
    this.config = this.mergeConfig(config);
    this.input = this.createDefaultInput();

    this.setupEventListeners();
    this.createControlElements();
    this.updateLayout();
  }

  /**
   * Merge user config with defaults
   */
  private mergeConfig(userConfig?: Partial<TouchControlConfig>): TouchControlConfig {
    const defaultConfig: TouchControlConfig = {
      joystick: {
        enabled: true,
        position: { x: 15, y: 75 }, // Bottom left
        size: 80,
        deadzone: 0.1,
        opacity: 0.7
      },
      buttons: [
        {
          id: 'shoot',
          label: '🔫',
          position: { x: 85, y: 75 },
          size: { width: 60, height: 60 },
          action: 'shoot',
          opacity: 0.7,
          visible: true
        },
        {
          id: 'special',
          label: '⚡',
          position: { x: 85, y: 60 },
          size: { width: 50, height: 50 },
          action: 'special',
          opacity: 0.7,
          visible: true
        },
        {
          id: 'pause',
          label: '⏸️',
          position: { x: 95, y: 5 },
          size: { width: 40, height: 40 },
          action: 'pause',
          opacity: 0.7,
          visible: true
        }
      ],
      gestures: {
        enabled: true,
        swipeThreshold: 50,
        pinchThreshold: 0.1,
        tapTimeout: 300
      }
    };

    return {
      joystick: { ...defaultConfig.joystick, ...userConfig?.joystick },
      buttons: userConfig?.buttons || defaultConfig.buttons,
      gestures: { ...defaultConfig.gestures, ...userConfig?.gestures }
    };
  }

  /**
   * Create default input state
   */
  private createDefaultInput(): TouchInput {
    return {
      joystick: {
        active: false,
        direction: new Vector2(0, 0),
        magnitude: 0
      },
      buttons: new Map(),
      gestures: {
        swipe: { active: false, direction: new Vector2(0, 0), velocity: 0 },
        pinch: { active: false, scale: 1, center: new Vector2(0, 0) },
        tap: { active: false, position: new Vector2(0, 0), count: 0 }
      }
    };
  }

  /**
   * Setup touch event listeners
   */
  private setupEventListeners(): void {
    this.canvas.addEventListener('touchstart', this.onTouchStart.bind(this), { passive: false });
    this.canvas.addEventListener('touchmove', this.onTouchMove.bind(this), { passive: false });
    this.canvas.addEventListener('touchend', this.onTouchEnd.bind(this), { passive: false });
    this.canvas.addEventListener('touchcancel', this.onTouchCancel.bind(this), { passive: false });

    // Prevent default touch behaviors
    this.canvas.addEventListener('touchstart', (e) => e.preventDefault());
    this.canvas.addEventListener('touchmove', (e) => e.preventDefault());

    // Handle orientation changes
    window.addEventListener('orientationchange', () => {
      setTimeout(() => this.updateLayout(), 100);
    });

    // Handle resize
    window.addEventListener('resize', () => this.updateLayout());
  }

  /**
   * Create control elements
   */
  private createControlElements(): void {
    // Create joystick
    if (this.config.joystick.enabled) {
      this.createJoystick();
    }

    // Create buttons
    for (const buttonConfig of this.config.buttons) {
      if (buttonConfig.visible) {
        this.createButton(buttonConfig);
      }
    }
  }

  /**
   * Create virtual joystick
   */
  private createJoystick(): void {
    // Joystick base
    this.joystickElement = document.createElement('div');
    this.joystickElement.style.cssText = `
      position: fixed;
      width: ${this.config.joystick.size * 2}px;
      height: ${this.config.joystick.size * 2}px;
      border: 2px solid rgba(255, 255, 255, 0.5);
      border-radius: 50%;
      background: rgba(0, 0, 0, 0.3);
      opacity: ${this.config.joystick.opacity};
      pointer-events: none;
      z-index: 1000;
      user-select: none;
    `;

    // Joystick knob
    this.joystickKnob = document.createElement('div');
    this.joystickKnob.style.cssText = `
      position: absolute;
      width: ${this.config.joystick.size}px;
      height: ${this.config.joystick.size}px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 50%;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      transition: all 0.1s ease;
    `;

    this.joystickElement.appendChild(this.joystickKnob);
    document.body.appendChild(this.joystickElement);
  }

  /**
   * Create touch button
   */
  private createButton(config: TouchButtonConfig): void {
    const button = document.createElement('div');
    button.style.cssText = `
      position: fixed;
      width: ${config.size.width}px;
      height: ${config.size.height}px;
      background: rgba(255, 255, 255, 0.2);
      border: 2px solid rgba(255, 255, 255, 0.5);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: ${Math.min(config.size.width, config.size.height) * 0.4}px;
      color: white;
      opacity: ${config.opacity};
      pointer-events: none;
      z-index: 1000;
      user-select: none;
      transition: all 0.1s ease;
    `;

    button.textContent = config.label;
    button.dataset.buttonId = config.id;

    this.buttonElements.set(config.id, button);
    document.body.appendChild(button);
  }

  /**
   * Update layout based on screen size
   */
  private updateLayout(): void {
    const rect = this.canvas.getBoundingClientRect();

    // Update joystick position
    if (this.joystickElement) {
      const x = rect.left + (rect.width * this.config.joystick.position.x / 100);
      const y = rect.top + (rect.height * this.config.joystick.position.y / 100);

      this.joystickElement.style.left = `${x - this.config.joystick.size}px`;
      this.joystickElement.style.top = `${y - this.config.joystick.size}px`;
    }

    // Update button positions
    for (const buttonConfig of this.config.buttons) {
      const button = this.buttonElements.get(buttonConfig.id);
      if (button) {
        const x = rect.left + (rect.width * buttonConfig.position.x / 100);
        const y = rect.top + (rect.height * buttonConfig.position.y / 100);

        button.style.left = `${x - buttonConfig.size.width / 2}px`;
        button.style.top = `${y - buttonConfig.size.height / 2}px`;
      }
    }
  }

  /**
   * Handle touch start
   */
  private onTouchStart(event: TouchEvent): void {
    if (!this.enabled) return;

    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i];
      this.activeTouches.set(touch.identifier, touch);

      const position = this.getTouchPosition(touch);

      // Check joystick
      if (this.config.joystick.enabled && this.joystickTouch === null) {
        if (this.isInJoystickArea(position)) {
          this.joystickTouch = touch.identifier;
          this.input.joystick.active = true;
          this.updateJoystick(position);
        }
      }

      // Check buttons
      for (const buttonConfig of this.config.buttons) {
        if (this.isInButtonArea(position, buttonConfig) && !this.buttonTouches.has(buttonConfig.id)) {
          this.buttonTouches.set(buttonConfig.id, touch.identifier);
          this.input.buttons.set(buttonConfig.id, true);
          this.highlightButton(buttonConfig.id, true);
        }
      }

      // Track for gestures
      if (this.config.gestures.enabled) {
        this.gestureStartTouches.push(touch);
        this.handleTapGesture(position);
      }
    }
  }

  /**
   * Handle touch move
   */
  private onTouchMove(event: TouchEvent): void {
    if (!this.enabled) return;

    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i];
      this.activeTouches.set(touch.identifier, touch);

      const position = this.getTouchPosition(touch);

      // Update joystick
      if (this.joystickTouch === touch.identifier) {
        this.updateJoystick(position);
      }
    }

    // Handle gestures
    if (this.config.gestures.enabled) {
      this.handleSwipeGesture();
      this.handlePinchGesture();
    }
  }

  /**
   * Handle touch end
   */
  private onTouchEnd(event: TouchEvent): void {
    if (!this.enabled) return;

    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i];
      this.activeTouches.delete(touch.identifier);

      // Release joystick
      if (this.joystickTouch === touch.identifier) {
        this.joystickTouch = null;
        this.input.joystick.active = false;
        this.input.joystick.direction.set(0, 0);
        this.input.joystick.magnitude = 0;
        this.resetJoystickKnob();
      }

      // Release buttons
      for (const [buttonId, touchId] of this.buttonTouches.entries()) {
        if (touchId === touch.identifier) {
          this.buttonTouches.delete(buttonId);
          this.input.buttons.set(buttonId, false);
          this.highlightButton(buttonId, false);
        }
      }
    }

    // Clear gesture tracking
    this.gestureStartTouches = this.gestureStartTouches.filter(
      t => Array.from(this.activeTouches.values()).some(at => at.identifier === t.identifier)
    );
  }

  /**
   * Handle touch cancel
   */
  private onTouchCancel(event: TouchEvent): void {
    this.onTouchEnd(event);
  }

  /**
   * Get touch position relative to canvas
   */
  private getTouchPosition(touch: Touch): Vector2 {
    const rect = this.canvas.getBoundingClientRect();
    return new Vector2(
      touch.clientX - rect.left,
      touch.clientY - rect.top
    );
  }

  /**
   * Check if position is in joystick area
   */
  private isInJoystickArea(position: Vector2): boolean {
    const rect = this.canvas.getBoundingClientRect();
    const joystickX = rect.width * this.config.joystick.position.x / 100;
    const joystickY = rect.height * this.config.joystick.position.y / 100;

    const distance = position.distance(new Vector2(joystickX, joystickY));
    return distance <= this.config.joystick.size * 1.5; // Larger touch area
  }

  /**
   * Check if position is in button area
   */
  private isInButtonArea(position: Vector2, buttonConfig: TouchButtonConfig): boolean {
    const rect = this.canvas.getBoundingClientRect();
    const buttonX = rect.width * buttonConfig.position.x / 100;
    const buttonY = rect.height * buttonConfig.position.y / 100;

    const halfWidth = buttonConfig.size.width / 2;
    const halfHeight = buttonConfig.size.height / 2;

    return position.x >= buttonX - halfWidth &&
      position.x <= buttonX + halfWidth &&
      position.y >= buttonY - halfHeight &&
      position.y <= buttonY + halfHeight;
  }

  /**
   * Update joystick state
   */
  private updateJoystick(touchPosition: Vector2): void {
    const rect = this.canvas.getBoundingClientRect();
    const joystickCenter = new Vector2(
      rect.width * this.config.joystick.position.x / 100,
      rect.height * this.config.joystick.position.y / 100
    );

    const offset = touchPosition.subtract(joystickCenter);
    const distance = offset.magnitude();
    const maxDistance = this.config.joystick.size;

    // Calculate direction and magnitude
    if (distance > this.config.joystick.deadzone * maxDistance) {
      const clampedDistance = Math.min(distance, maxDistance);
      this.input.joystick.direction = offset.normalize();
      this.input.joystick.magnitude = (clampedDistance - this.config.joystick.deadzone * maxDistance) /
        (maxDistance - this.config.joystick.deadzone * maxDistance);
    } else {
      this.input.joystick.direction.set(0, 0);
      this.input.joystick.magnitude = 0;
    }

    // Update visual knob position
    if (this.joystickKnob) {
      const clampedOffset = offset.magnitude() > maxDistance ?
        offset.normalize().multiply(maxDistance) : offset;

      this.joystickKnob.style.transform =
        `translate(calc(-50% + ${clampedOffset.x}px), calc(-50% + ${clampedOffset.y}px))`;
    }
  }

  /**
   * Reset joystick knob to center
   */
  private resetJoystickKnob(): void {
    if (this.joystickKnob) {
      this.joystickKnob.style.transform = 'translate(-50%, -50%)';
    }
  }

  /**
   * Highlight button
   */
  private highlightButton(buttonId: string, highlight: boolean): void {
    const button = this.buttonElements.get(buttonId);
    if (button) {
      button.style.background = highlight ?
        'rgba(255, 255, 255, 0.5)' :
        'rgba(255, 255, 255, 0.2)';
      button.style.transform = highlight ? 'scale(0.95)' : 'scale(1)';
    }
  }

  /**
   * Handle tap gesture
   */
  private handleTapGesture(position: Vector2): void {
    const now = Date.now();

    if (now - this.lastTapTime < this.config.gestures.tapTimeout) {
      this.tapCount++;
    } else {
      this.tapCount = 1;
    }

    this.lastTapTime = now;
    this.input.gestures.tap = {
      active: true,
      position: position.copy(),
      count: this.tapCount
    };

    // Clear tap after timeout
    setTimeout(() => {
      this.input.gestures.tap.active = false;
    }, this.config.gestures.tapTimeout);
  }

  /**
   * Handle swipe gesture
   */
  private handleSwipeGesture(): void {
    if (this.gestureStartTouches.length !== 1) return;

    const startTouch = this.gestureStartTouches[0];
    const currentTouch = this.activeTouches.get(startTouch.identifier);

    if (!currentTouch) return;

    const startPos = this.getTouchPosition(startTouch);
    const currentPos = this.getTouchPosition(currentTouch);
    const delta = currentPos.subtract(startPos);

    if (delta.magnitude() > this.config.gestures.swipeThreshold) {
      this.input.gestures.swipe = {
        active: true,
        direction: delta.normalize(),
        velocity: delta.magnitude()
      };
    }
  }

  /**
   * Handle pinch gesture
   */
  private handlePinchGesture(): void {
    if (this.gestureStartTouches.length !== 2) return;

    const touch1 = this.activeTouches.get(this.gestureStartTouches[0].identifier);
    const touch2 = this.activeTouches.get(this.gestureStartTouches[1].identifier);

    if (!touch1 || !touch2) return;

    const startPos1 = this.getTouchPosition(this.gestureStartTouches[0]);
    const startPos2 = this.getTouchPosition(this.gestureStartTouches[1]);
    const currentPos1 = this.getTouchPosition(touch1);
    const currentPos2 = this.getTouchPosition(touch2);

    const startDistance = startPos1.distance(startPos2);
    const currentDistance = currentPos1.distance(currentPos2);

    if (startDistance > 0) {
      const scale = currentDistance / startDistance;

      if (Math.abs(scale - 1) > this.config.gestures.pinchThreshold) {
        this.input.gestures.pinch = {
          active: true,
          scale,
          center: currentPos1.add(currentPos2).multiply(0.5)
        };
      }
    }
  }

  /**
   * Enable touch controls
   */
  enable(): void {
    this.enabled = true;
    this.setVisible(true);
  }

  /**
   * Disable touch controls
   */
  disable(): void {
    this.enabled = false;
    this.setVisible(false);
  }

  /**
   * Set visibility of controls
   */
  setVisible(visible: boolean): void {
    this.visible = visible;

    if (this.joystickElement) {
      this.joystickElement.style.display = visible ? 'block' : 'none';
    }

    for (const button of this.buttonElements.values()) {
      button.style.display = visible ? 'flex' : 'none';
    }
  }

  /**
   * Get current input state
   */
  getInput(): TouchInput {
    return this.input;
  }

  /**
   * Update (call once per frame)
   */
  update(): void {
    // Clear one-frame gestures
    this.input.gestures.swipe.active = false;
    this.input.gestures.pinch.active = false;
  }

  /**
   * Destroy touch controls
   */
  destroy(): void {
    // Remove event listeners
    this.canvas.removeEventListener('touchstart', this.onTouchStart.bind(this));
    this.canvas.removeEventListener('touchmove', this.onTouchMove.bind(this));
    this.canvas.removeEventListener('touchend', this.onTouchEnd.bind(this));
    this.canvas.removeEventListener('touchcancel', this.onTouchCancel.bind(this));

    // Remove DOM elements
    if (this.joystickElement) {
      document.body.removeChild(this.joystickElement);
    }

    for (const button of this.buttonElements.values()) {
      document.body.removeChild(button);
    }

    this.buttonElements.clear();
  }
}
