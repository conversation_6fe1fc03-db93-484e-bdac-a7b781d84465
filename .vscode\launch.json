{"version": "0.2.0", "configurations": [{"name": "Launch Chrome", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}", "sourceMaps": true, "sourceMapPathOverrides": {"webpack:///./src/*": "${webRoot}/src/*"}}, {"name": "Attach to Chrome", "type": "chrome", "request": "attach", "port": 9222, "webRoot": "${workspaceFolder}", "sourceMaps": true}, {"name": "Debug Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/vitest/vitest.mjs", "args": ["run", "--reporter=verbose"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "env": {"NODE_ENV": "test"}}, {"name": "Debug Current Test File", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/vitest/vitest.mjs", "args": ["run", "${relativeFile}", "--reporter=verbose"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "env": {"NODE_ENV": "test"}}]}