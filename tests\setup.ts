/**
 * Test setup file for Vitest
 * Configures global test environment and mocks
 */

import { vi } from 'vitest';

// Mock performance API for Node.js environment
Object.defineProperty(global, 'performance', {
  value: {
    now: vi.fn(() => Date.now()),
    mark: vi.fn(),
    measure: vi.fn(),
    getEntriesByType: vi.fn(() => []),
    getEntriesByName: vi.fn(() => []),
    clearMarks: vi.fn(),
    clearMeasures: vi.fn(),
    memory: {
      usedJSHeapSize: 1024 * 1024 * 10, // 10MB
      totalJSHeapSize: 1024 * 1024 * 50, // 50MB
      jsHeapSizeLimit: 1024 * 1024 * 100 // 100MB
    }
  },
  writable: true
});

// Mock requestAnimationFrame
Object.defineProperty(global, 'requestAnimationFrame', {
  value: vi.fn((callback: FrameRequestCallback) => {
    return setTimeout(() => callback(performance.now()), 16);
  }),
  writable: true
});

Object.defineProperty(global, 'cancelAnimationFrame', {
  value: vi.fn((id: number) => clearTimeout(id)),
  writable: true
});

// Mock Canvas API
const mockCanvas = {
  getContext: vi.fn(() => ({
    fillRect: vi.fn(),
    strokeRect: vi.fn(),
    fillText: vi.fn(),
    measureText: vi.fn(() => ({ width: 100 })),
    drawImage: vi.fn(),
    beginPath: vi.fn(),
    moveTo: vi.fn(),
    lineTo: vi.fn(),
    arc: vi.fn(),
    fill: vi.fn(),
    stroke: vi.fn(),
    save: vi.fn(),
    restore: vi.fn(),
    translate: vi.fn(),
    rotate: vi.fn(),
    scale: vi.fn(),
    resetTransform: vi.fn(),
    setTransform: vi.fn(),
    clearRect: vi.fn(),
    createImageData: vi.fn(),
    getImageData: vi.fn(),
    putImageData: vi.fn(),
    createLinearGradient: vi.fn(),
    createRadialGradient: vi.fn(),
    createPattern: vi.fn(),
    // Properties
    fillStyle: '#000000',
    strokeStyle: '#000000',
    lineWidth: 1,
    font: '16px Arial',
    textAlign: 'left',
    textBaseline: 'top',
    globalAlpha: 1,
    globalCompositeOperation: 'source-over',
    imageSmoothingEnabled: true,
    canvas: {
      width: 1024,
      height: 768
    }
  })),
  width: 1024,
  height: 768,
  style: {},
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  getBoundingClientRect: vi.fn(() => ({
    left: 0,
    top: 0,
    right: 1024,
    bottom: 768,
    width: 1024,
    height: 768,
    x: 0,
    y: 0
  }))
};

Object.defineProperty(global, 'HTMLCanvasElement', {
  value: vi.fn(() => mockCanvas),
  writable: true
});

// Mock Image
Object.defineProperty(global, 'Image', {
  value: vi.fn(() => ({
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    src: '',
    width: 100,
    height: 100,
    complete: true,
    naturalWidth: 100,
    naturalHeight: 100
  })),
  writable: true
});

// Mock Audio
Object.defineProperty(global, 'Audio', {
  value: vi.fn(() => ({
    play: vi.fn().mockResolvedValue(undefined),
    pause: vi.fn(),
    load: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    volume: 1,
    currentTime: 0,
    duration: 0,
    paused: true,
    ended: false,
    muted: false,
    src: ''
  })),
  writable: true
});

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn()
};

Object.defineProperty(global, 'localStorage', {
  value: localStorageMock,
  writable: true
});

// Mock window object
Object.defineProperty(global, 'window', {
  value: {
    ...global,
    location: {
      hostname: 'localhost',
      href: 'http://localhost:3000',
      origin: 'http://localhost:3000'
    },
    innerWidth: 1024,
    innerHeight: 768,
    devicePixelRatio: 1,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    requestAnimationFrame: global.requestAnimationFrame,
    cancelAnimationFrame: global.cancelAnimationFrame,
    localStorage: localStorageMock
  },
  writable: true
});

// Mock document
Object.defineProperty(global, 'document', {
  value: {
    createElement: vi.fn((tagName: string) => {
      if (tagName === 'canvas') {
        return mockCanvas;
      }
      return {
        style: {},
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        appendChild: vi.fn(),
        removeChild: vi.fn(),
        getAttribute: vi.fn(),
        setAttribute: vi.fn(),
        innerHTML: '',
        textContent: ''
      };
    }),
    getElementById: vi.fn(),
    querySelector: vi.fn(),
    querySelectorAll: vi.fn(() => []),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    body: {
      appendChild: vi.fn(),
      removeChild: vi.fn(),
      style: {}
    }
  },
  writable: true
});

// Console spy setup for testing
beforeEach(() => {
  vi.clearAllMocks();
});

// Global test utilities
export const createMockCanvas = () => mockCanvas;
export const createMockContext2D = () => mockCanvas.getContext('2d');

// Test helpers
export const waitForNextFrame = () => new Promise(resolve => {
  requestAnimationFrame(resolve);
});

export const waitForTime = (ms: number) => new Promise(resolve => {
  setTimeout(resolve, ms);
});
