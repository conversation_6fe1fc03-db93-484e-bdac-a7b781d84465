/**
 * Inventory and equipment management system
 */

import { Item, ItemStats, ItemType, WeaponType, ArmorType, AccessoryType } from './LootSystem';

export interface InventorySlot {
  item: Item | null;
  quantity: number;
}

export interface EquipmentSlots {
  weapon: Item | null;
  helmet: Item | null;
  chest: Item | null;
  legs: Item | null;
  boots: Item | null;
  gloves: Item | null;
  ring1: Item | null;
  ring2: Item | null;
  amulet: Item | null;
}

export interface InventoryData {
  slots: InventorySlot[];
  equipment: EquipmentSlots;
  gold: number;
  maxSlots: number;
}

export class InventorySystem {
  private inventory: InventoryData;

  constructor(maxSlots: number = 30) {
    this.inventory = {
      slots: Array(maxSlots).fill(null).map(() => ({ item: null, quantity: 0 })),
      equipment: {
        weapon: null,
        helmet: null,
        chest: null,
        legs: null,
        boots: null,
        gloves: null,
        ring1: null,
        ring2: null,
        amulet: null
      },
      gold: 0,
      maxSlots
    };
  }

  // Item management
  addItem(item: Item): boolean {
    // Try to stack with existing items first
    if (item.stackSize > 1) {
      for (const slot of this.inventory.slots) {
        if (slot.item && 
            slot.item.name === item.name && 
            slot.item.rarity === item.rarity &&
            slot.quantity < slot.item.stackSize) {
          
          const spaceAvailable = slot.item.stackSize - slot.quantity;
          const amountToAdd = Math.min(spaceAvailable, item.currentStack);
          
          slot.quantity += amountToAdd;
          item.currentStack -= amountToAdd;
          
          if (item.currentStack <= 0) {
            return true; // Item fully added
          }
        }
      }
    }

    // Find empty slot for remaining items
    const emptySlot = this.inventory.slots.find(slot => slot.item === null);
    if (emptySlot) {
      emptySlot.item = item;
      emptySlot.quantity = item.currentStack;
      return true;
    }

    return false; // Inventory full
  }

  removeItem(slotIndex: number, quantity: number = 1): Item | null {
    if (slotIndex < 0 || slotIndex >= this.inventory.slots.length) {
      return null;
    }

    const slot = this.inventory.slots[slotIndex];
    if (!slot.item || slot.quantity < quantity) {
      return null;
    }

    const removedItem = { ...slot.item };
    removedItem.currentStack = quantity;

    slot.quantity -= quantity;
    if (slot.quantity <= 0) {
      slot.item = null;
      slot.quantity = 0;
    }

    return removedItem;
  }

  moveItem(fromIndex: number, toIndex: number): boolean {
    if (fromIndex < 0 || fromIndex >= this.inventory.slots.length ||
        toIndex < 0 || toIndex >= this.inventory.slots.length ||
        fromIndex === toIndex) {
      return false;
    }

    const fromSlot = this.inventory.slots[fromIndex];
    const toSlot = this.inventory.slots[toIndex];

    if (!fromSlot.item) return false;

    // If target slot is empty, move item
    if (!toSlot.item) {
      toSlot.item = fromSlot.item;
      toSlot.quantity = fromSlot.quantity;
      fromSlot.item = null;
      fromSlot.quantity = 0;
      return true;
    }

    // If items are the same and stackable, try to stack
    if (toSlot.item.name === fromSlot.item.name &&
        toSlot.item.rarity === fromSlot.item.rarity &&
        toSlot.item.stackSize > 1) {
      
      const spaceAvailable = toSlot.item.stackSize - toSlot.quantity;
      const amountToMove = Math.min(spaceAvailable, fromSlot.quantity);
      
      toSlot.quantity += amountToMove;
      fromSlot.quantity -= amountToMove;
      
      if (fromSlot.quantity <= 0) {
        fromSlot.item = null;
        fromSlot.quantity = 0;
      }
      
      return true;
    }

    // Otherwise, swap items
    const tempItem = toSlot.item;
    const tempQuantity = toSlot.quantity;
    
    toSlot.item = fromSlot.item;
    toSlot.quantity = fromSlot.quantity;
    
    fromSlot.item = tempItem;
    fromSlot.quantity = tempQuantity;
    
    return true;
  }

  // Equipment management
  equipItem(slotIndex: number): boolean {
    const slot = this.inventory.slots[slotIndex];
    if (!slot.item || !this.canEquip(slot.item)) {
      return false;
    }

    const item = slot.item;
    const equipSlot = this.getEquipmentSlot(item);
    
    if (!equipSlot) return false;

    // Unequip current item if any
    const currentEquipped = this.inventory.equipment[equipSlot as keyof EquipmentSlots];
    if (currentEquipped) {
      if (!this.addItem(currentEquipped)) {
        return false; // Can't unequip if inventory is full
      }
    }

    // Equip new item
    (this.inventory.equipment as any)[equipSlot] = item;
    
    // Remove from inventory
    slot.item = null;
    slot.quantity = 0;

    return true;
  }

  unequipItem(equipSlot: keyof EquipmentSlots): boolean {
    const equippedItem = this.inventory.equipment[equipSlot];
    if (!equippedItem) return false;

    if (this.addItem(equippedItem)) {
      this.inventory.equipment[equipSlot] = null;
      return true;
    }

    return false; // Inventory full
  }

  private canEquip(item: Item): boolean {
    return item.type === 'weapon' || item.type === 'armor' || item.type === 'accessory';
  }

  private getEquipmentSlot(item: Item): string | null {
    if (item.type === 'weapon') {
      return 'weapon';
    } else if (item.type === 'armor') {
      return item.subType as string;
    } else if (item.type === 'accessory') {
      if (item.subType === 'ring') {
        // Try ring1 first, then ring2
        return this.inventory.equipment.ring1 ? 'ring2' : 'ring1';
      }
      return item.subType as string;
    }
    return null;
  }

  // Consumable usage
  useItem(slotIndex: number): boolean {
    const slot = this.inventory.slots[slotIndex];
    if (!slot.item || slot.item.type !== 'consumable') {
      return false;
    }

    // Remove one from stack
    slot.quantity -= 1;
    if (slot.quantity <= 0) {
      slot.item = null;
      slot.quantity = 0;
    }

    return true;
  }

  // Stats calculation
  calculateEquippedStats(): ItemStats {
    const totalStats: ItemStats = {};

    for (const item of Object.values(this.inventory.equipment)) {
      if (item) {
        const itemStats = this.calculateItemTotalStats(item);
        this.addStats(totalStats, itemStats);
      }
    }

    return totalStats;
  }

  private calculateItemTotalStats(item: Item): ItemStats {
    const stats: ItemStats = { ...item.baseStats };

    // Apply modifier stats
    for (const modifier of item.modifiers) {
      this.addStats(stats, modifier.stats);
    }

    return stats;
  }

  private addStats(target: ItemStats, source: ItemStats): void {
    for (const [stat, value] of Object.entries(source)) {
      if (value !== undefined) {
        const currentValue = (target as any)[stat] || 0;
        
        // Handle percentage vs flat modifiers
        if (value < 1 && value > -1 && value !== 0) {
          // Percentage modifier
          (target as any)[stat] = currentValue * (1 + value);
        } else {
          // Flat modifier
          (target as any)[stat] = currentValue + value;
        }
      }
    }
  }

  // Gold management
  addGold(amount: number): void {
    this.inventory.gold += amount;
  }

  removeGold(amount: number): boolean {
    if (this.inventory.gold >= amount) {
      this.inventory.gold -= amount;
      return true;
    }
    return false;
  }

  // Inventory queries
  getInventorySlots(): InventorySlot[] {
    return this.inventory.slots;
  }

  getEquipment(): EquipmentSlots {
    return this.inventory.equipment;
  }

  getGold(): number {
    return this.inventory.gold;
  }

  getUsedSlots(): number {
    return this.inventory.slots.filter(slot => slot.item !== null).length;
  }

  getMaxSlots(): number {
    return this.inventory.maxSlots;
  }

  isFull(): boolean {
    return this.getUsedSlots() >= this.inventory.maxSlots;
  }

  findItem(itemName: string): number {
    return this.inventory.slots.findIndex(slot => 
      slot.item && slot.item.name === itemName
    );
  }

  hasItem(itemName: string, quantity: number = 1): boolean {
    let totalQuantity = 0;
    for (const slot of this.inventory.slots) {
      if (slot.item && slot.item.name === itemName) {
        totalQuantity += slot.quantity;
        if (totalQuantity >= quantity) {
          return true;
        }
      }
    }
    return false;
  }

  // Sorting
  sortInventory(): void {
    // Get all non-empty slots
    const items = this.inventory.slots
      .filter(slot => slot.item !== null)
      .sort((a, b) => {
        if (!a.item || !b.item) return 0;
        
        // Sort by type first
        if (a.item.type !== b.item.type) {
          return a.item.type.localeCompare(b.item.type);
        }
        
        // Then by rarity
        const rarityOrder = { 'common': 0, 'uncommon': 1, 'rare': 2, 'epic': 3, 'legendary': 4 };
        const rarityA = rarityOrder[a.item.rarity] || 0;
        const rarityB = rarityOrder[b.item.rarity] || 0;
        
        if (rarityA !== rarityB) {
          return rarityB - rarityA; // Higher rarity first
        }
        
        // Finally by name
        return a.item.name.localeCompare(b.item.name);
      });

    // Clear all slots
    this.inventory.slots.forEach(slot => {
      slot.item = null;
      slot.quantity = 0;
    });

    // Place sorted items back
    items.forEach((item, index) => {
      if (index < this.inventory.maxSlots) {
        this.inventory.slots[index] = item;
      }
    });
  }

  // Save/Load
  saveInventoryData(): string {
    const saveData = {
      slots: this.inventory.slots.map(slot => ({
        item: slot.item,
        quantity: slot.quantity
      })),
      equipment: this.inventory.equipment,
      gold: this.inventory.gold,
      maxSlots: this.inventory.maxSlots
    };
    
    return JSON.stringify(saveData);
  }

  loadInventoryData(saveString: string): boolean {
    try {
      const saveData = JSON.parse(saveString);
      
      this.inventory = {
        slots: saveData.slots || [],
        equipment: saveData.equipment || {},
        gold: saveData.gold || 0,
        maxSlots: saveData.maxSlots || 30
      };
      
      // Ensure slots array is correct length
      while (this.inventory.slots.length < this.inventory.maxSlots) {
        this.inventory.slots.push({ item: null, quantity: 0 });
      }
      
      return true;
    } catch (error) {
      console.error('Failed to load inventory data:', error);
      return false;
    }
  }

  // Utility methods
  getItemAtSlot(slotIndex: number): Item | null {
    if (slotIndex < 0 || slotIndex >= this.inventory.slots.length) {
      return null;
    }
    return this.inventory.slots[slotIndex].item;
  }

  getQuantityAtSlot(slotIndex: number): number {
    if (slotIndex < 0 || slotIndex >= this.inventory.slots.length) {
      return 0;
    }
    return this.inventory.slots[slotIndex].quantity;
  }

  expandInventory(additionalSlots: number): void {
    const newMaxSlots = this.inventory.maxSlots + additionalSlots;
    
    // Add new empty slots
    for (let i = this.inventory.maxSlots; i < newMaxSlots; i++) {
      this.inventory.slots.push({ item: null, quantity: 0 });
    }
    
    this.inventory.maxSlots = newMaxSlots;
  }
}
