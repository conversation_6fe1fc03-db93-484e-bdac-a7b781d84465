/**
 * Main menu scene with game options and navigation
 */

import { BaseScene, SceneManager } from '@/engine/core/SceneManager';
import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';
import { InputManager } from '@/engine/input/InputManager';

interface MenuOption {
  text: string;
  action: () => void;
  enabled: boolean;
}

export class MenuScene extends BaseScene {
  private menuOptions: MenuOption[] = [];
  private selectedIndex: number = 0;
  private titleAnimation: number = 0;
  private backgroundStars: Array<{ x: number; y: number; speed: number; size: number }> = [];

  // Injected dependencies
  private sceneManager: SceneManager | null = null;
  private inputManager: InputManager | null = null;

  initialize(): void {
    this.setupMenuOptions();
    this.generateBackgroundStars();
    console.log('📋 Menu scene initialized');
  }

  // Method to inject dependencies
  setSceneManager(sceneManager: SceneManager): void {
    this.sceneManager = sceneManager;
  }

  setInputManager(inputManager: InputManager): void {
    this.inputManager = inputManager;
  }

  private setupMenuOptions(): void {
    this.menuOptions = [
      {
        text: 'Start Game',
        action: () => this.startGame(),
        enabled: true
      },
      {
        text: 'Continue',
        action: () => this.continueGame(),
        enabled: this.hasSaveGame()
      },
      {
        text: 'Settings',
        action: () => this.openSettings(),
        enabled: true
      },
      {
        text: 'Credits',
        action: () => this.showCredits(),
        enabled: true
      }
    ];
  }

  private generateBackgroundStars(): void {
    for (let i = 0; i < 100; i++) {
      this.backgroundStars.push({
        x: Math.random() * 1024,
        y: Math.random() * 768,
        speed: Math.random() * 20 + 10,
        size: Math.random() * 2 + 1
      });
    }
  }

  private hasSaveGame(): boolean {
    // Check if there's a saved game in localStorage
    return localStorage.getItem('bulletHellRPG_save') !== null;
  }

  private startGame(): void {
    console.log('🎮 Starting new game...');
    if (this.sceneManager) {
      this.sceneManager.switchTo('game');
    }
  }

  private continueGame(): void {
    console.log('📂 Loading saved game...');
    // TODO: Load game state from localStorage
    if (this.sceneManager) {
      this.sceneManager.switchTo('game');
    }
  }

  private openSettings(): void {
    console.log('⚙️ Opening settings...');
    // TODO: Implement settings scene
    // For now, just show a message
    alert('Settings menu not yet implemented');
  }

  private showCredits(): void {
    console.log('👥 Showing credits...');
    // TODO: Implement credits scene
    alert('Credits:\n\nBulletHell Rogue-like RPG\nBuilt with TypeScript & HTML5 Canvas\n\nDeveloped by: Game Developer\nVersion: 1.0.0');
  }

  fixedUpdate(deltaTime: number): void {
    if (!this.inputManager) return;

    // Handle menu navigation
    if (this.inputManager.isKeyPressed('ArrowUp') || this.inputManager.isKeyPressed('KeyW')) {
      this.selectedIndex = Math.max(0, this.selectedIndex - 1);
    }

    if (this.inputManager.isKeyPressed('ArrowDown') || this.inputManager.isKeyPressed('KeyS')) {
      this.selectedIndex = Math.min(this.menuOptions.length - 1, this.selectedIndex + 1);
    }

    // Handle selection
    if (this.inputManager.isKeyPressed('Enter') || this.inputManager.isKeyPressed('Space')) {
      const selectedOption = this.menuOptions[this.selectedIndex];
      if (selectedOption && selectedOption.enabled) {
        selectedOption.action();
      }
    }
  }

  update(deltaTime: number): void {
    // Update title animation
    this.titleAnimation += deltaTime * 2;
    
    // Update background stars
    for (const star of this.backgroundStars) {
      star.y += star.speed * deltaTime;
      if (star.y > 768) {
        star.y = -10;
        star.x = Math.random() * 1024;
      }
    }
  }

  render(renderer: CanvasRenderer, alpha: number): void {
    const { width, height } = renderer.getCanvasSize();
    
    // Clear with space background
    renderer.clear('#0a0a0a');
    
    // Draw background stars
    for (const star of this.backgroundStars) {
      const brightness = Math.sin(this.titleAnimation + star.x * 0.01) * 0.3 + 0.7;
      const alpha = Math.max(0.3, brightness);
      renderer.setGlobalAlpha(alpha);
      renderer.drawCircle(star.x, star.y, star.size, '#ffffff');
    }
    renderer.setGlobalAlpha(1);
    
    // Draw title with animation
    const titleY = 150 + Math.sin(this.titleAnimation) * 10;
    renderer.drawText(
      'BULLET HELL',
      width / 2 - 180,
      titleY,
      'bold 48px monospace',
      '#ff6b6b'
    );
    renderer.drawText(
      'ROGUE-LIKE RPG',
      width / 2 - 160,
      titleY + 60,
      'bold 32px monospace',
      '#4ecdc4'
    );
    
    // Draw subtitle
    renderer.drawText(
      'Survive the Dungeon • Collect Loot • Upgrade Your Skills',
      width / 2 - 200,
      titleY + 120,
      '16px monospace',
      '#888888'
    );
    
    // Draw menu options
    const menuStartY = 350;
    const menuSpacing = 50;
    
    for (let i = 0; i < this.menuOptions.length; i++) {
      const option = this.menuOptions[i];
      const y = menuStartY + i * menuSpacing;
      const isSelected = i === this.selectedIndex;
      const isEnabled = option.enabled;
      
      // Draw selection indicator
      if (isSelected) {
        renderer.drawText('>', width / 2 - 120, y, '24px monospace', '#ffff00');
      }
      
      // Draw option text
      let color = '#ffffff';
      if (!isEnabled) {
        color = '#666666';
      } else if (isSelected) {
        color = '#ffff00';
      }
      
      renderer.drawText(option.text, width / 2 - 100, y, '24px monospace', color);
    }
    
    // Draw controls hint
    renderer.drawText(
      'Use WASD or Arrow Keys to navigate • ENTER to select • ESC for menu',
      width / 2 - 250,
      height - 50,
      '14px monospace',
      '#666666'
    );
    
    // Draw version info
    renderer.drawText(
      'v1.0.0 - Built with TypeScript & Canvas 2D',
      10,
      height - 20,
      '12px monospace',
      '#444444'
    );
  }

  destroy(): void {
    console.log('🗑️ Menu scene destroyed');
  }
}
