/**
 * Game state management and scene transitions
 */

import { SaveSystem, GameState, GameSettings, GameStats } from './SaveSystem';
import { ProgressionSystem, ProgressionData } from './ProgressionSystem';
import { InventorySystem } from './InventorySystem';
import { Vector2 } from '@/utils/math/Vector2';

export type GameScene = 'menu' | 'game' | 'inventory' | 'skills' | 'settings' | 'pause' | 'gameOver' | 'victory';

export interface SceneTransition {
  from: GameScene;
  to: GameScene;
  duration: number;
  type: 'fade' | 'slide' | 'instant';
}

export class GameStateManager {
  private saveSystem: SaveSystem;
  private progressionSystem: ProgressionSystem;
  private inventorySystem: InventorySystem;
  
  private currentScene: GameScene = 'menu';
  private previousScene: GameScene = 'menu';
  private sceneTransition: SceneTransition | null = null;
  private transitionProgress: number = 0;
  
  private gameState: Partial<GameState> = {};
  private isPaused: boolean = false;
  private gameTime: number = 0;
  private sessionStartTime: number = 0;
  
  // Permadeath settings
  private permadeathMode: boolean = false;
  private hasUnsavedProgress: boolean = false;

  constructor() {
    this.saveSystem = new SaveSystem();
    this.progressionSystem = new ProgressionSystem();
    this.inventorySystem = new InventorySystem();
    
    this.sessionStartTime = Date.now();
    this.initializeGameState();
  }

  private initializeGameState(): void {
    // Try to load autosave
    const autosave = this.saveSystem.loadGame('autosave');
    if (autosave) {
      this.loadGameState(autosave);
    } else {
      this.createNewGame();
    }
  }

  // Scene Management
  changeScene(newScene: GameScene, transitionType: 'fade' | 'slide' | 'instant' = 'fade'): void {
    if (this.currentScene === newScene) return;
    
    this.previousScene = this.currentScene;
    
    this.sceneTransition = {
      from: this.currentScene,
      to: newScene,
      duration: transitionType === 'instant' ? 0 : 0.5,
      type: transitionType
    };
    
    this.transitionProgress = 0;
    
    // Handle scene-specific logic
    this.onSceneExit(this.currentScene);
    this.onSceneEnter(newScene);
    
    if (transitionType === 'instant') {
      this.currentScene = newScene;
      this.sceneTransition = null;
    }
  }

  private onSceneExit(scene: GameScene): void {
    switch (scene) {
      case 'game':
        // Auto-save when leaving game
        if (!this.permadeathMode || this.gameState.playerState?.isAlive) {
          this.autoSave();
        }
        break;
      case 'settings':
        // Save settings when leaving settings menu
        const settings = this.getSettings();
        if (settings) {
          this.saveSystem.saveSettings(settings);
        }
        break;
    }
  }

  private onSceneEnter(scene: GameScene): void {
    switch (scene) {
      case 'game':
        this.isPaused = false;
        break;
      case 'pause':
      case 'inventory':
      case 'skills':
      case 'settings':
        this.isPaused = true;
        break;
      case 'gameOver':
        this.handleGameOver();
        break;
      case 'victory':
        this.handleVictory();
        break;
    }
  }

  updateSceneTransition(deltaTime: number): void {
    if (!this.sceneTransition) return;
    
    this.transitionProgress += deltaTime / this.sceneTransition.duration;
    
    if (this.transitionProgress >= 1) {
      this.currentScene = this.sceneTransition.to;
      this.sceneTransition = null;
      this.transitionProgress = 0;
    }
  }

  // Game State Management
  createNewGame(permadeath: boolean = false): void {
    this.permadeathMode = permadeath;
    this.gameTime = 0;
    this.sessionStartTime = Date.now();
    
    const newCharacter = this.progressionSystem.createNewCharacter();
    
    this.gameState = {
      playerState: {
        position: new Vector2(512, 384),
        health: newCharacter.stats.health,
        mana: newCharacter.stats.mana,
        currentWeapon: 0,
        isAlive: true,
        lastSaveTime: Date.now()
      },
      progression: newCharacter,
      inventory: {
        slots: Array(30).fill(null).map(() => ({ item: null, quantity: 0 })),
        equipment: {
          weapon: null,
          helmet: null,
          chest: null,
          legs: null,
          boots: null,
          gloves: null,
          ring1: null,
          ring2: null,
          amulet: null
        },
        gold: 0,
        maxSlots: 30
      },
      currentLevel: 1,
      gameTime: 0,
      stats: this.saveSystem.getStats(),
      settings: this.saveSystem.getSettings()!,
      permadeath: permadeath
    };
    
    this.hasUnsavedProgress = false;
    console.log(`🎮 New game created (Permadeath: ${permadeath})`);
  }

  loadGameState(gameState: GameState): void {
    this.gameState = gameState;
    this.permadeathMode = gameState.permadeath;
    this.gameTime = gameState.gameTime;
    
    // Restore systems state
    if (gameState.progression) {
      // TODO: Load progression into ProgressionSystem
    }
    
    if (gameState.inventory) {
      // TODO: Load inventory into InventorySystem
    }
    
    console.log(`📁 Game state loaded (Level: ${gameState.progression.level})`);
  }

  // Save/Load Operations
  saveGame(slotId: string): boolean {
    if (!this.gameState.playerState?.isAlive && this.permadeathMode) {
      console.warn('⚠️ Cannot save in permadeath mode after death');
      return false;
    }
    
    this.updateGameStateFromSystems();
    const success = this.saveSystem.saveGame(this.gameState, slotId);
    
    if (success) {
      this.hasUnsavedProgress = false;
    }
    
    return success;
  }

  loadGame(slotId: string): boolean {
    const gameState = this.saveSystem.loadGame(slotId);
    if (!gameState) return false;
    
    this.loadGameState(gameState);
    this.changeScene('game', 'fade');
    return true;
  }

  autoSave(): void {
    if (this.hasUnsavedProgress && this.gameState.playerState?.isAlive) {
      this.updateGameStateFromSystems();
      this.saveSystem.updateAutoSave(0, this.gameState);
    }
  }

  private updateGameStateFromSystems(): void {
    // Update game time
    this.gameState.gameTime = this.gameTime;
    
    // Update player state
    if (this.gameState.playerState) {
      this.gameState.playerState.lastSaveTime = Date.now();
    }
    
    // TODO: Update from actual game systems
    // this.gameState.progression = this.progressionSystem.getCharacterData();
    // this.gameState.inventory = this.inventorySystem.getInventoryData();
  }

  // Game Events
  handlePlayerDeath(): void {
    if (!this.gameState.playerState) return;
    
    this.gameState.playerState.isAlive = false;
    this.hasUnsavedProgress = true;
    
    // Update death statistics
    const stats = this.saveSystem.getStats();
    stats.deathCount++;
    this.saveSystem.updateStats(stats);
    
    if (this.permadeathMode) {
      // In permadeath mode, delete the save
      this.saveSystem.deleteSave('autosave');
      this.changeScene('gameOver', 'fade');
    } else {
      // Regular mode - player can respawn
      this.changeScene('gameOver', 'fade');
    }
    
    console.log(`💀 Player died (Permadeath: ${this.permadeathMode})`);
  }

  handleLevelComplete(): void {
    if (!this.gameState.currentLevel) return;
    
    this.gameState.currentLevel++;
    this.hasUnsavedProgress = true;
    
    // Update statistics
    const stats = this.saveSystem.getStats();
    stats.levelsCompleted++;
    stats.highestLevel = Math.max(stats.highestLevel, this.gameState.currentLevel);
    this.saveSystem.updateStats(stats);
    
    // Auto-save progress
    this.autoSave();
    
    console.log(`🎉 Level ${this.gameState.currentLevel - 1} completed!`);
  }

  private handleGameOver(): void {
    // Final save of statistics
    const sessionTime = (Date.now() - this.sessionStartTime) / 1000;
    const stats = this.saveSystem.getStats();
    stats.totalPlayTime += sessionTime;
    this.saveSystem.updateStats(stats);
  }

  private handleVictory(): void {
    // Handle game completion
    const stats = this.saveSystem.getStats();
    stats.totalPlayTime += (Date.now() - this.sessionStartTime) / 1000;
    
    // Add victory achievement
    if (!stats.achievements.includes('game_complete')) {
      stats.achievements.push('game_complete');
    }
    
    this.saveSystem.updateStats(stats);
    this.autoSave();
  }

  // Game State Updates
  update(deltaTime: number): void {
    if (!this.isPaused) {
      this.gameTime += deltaTime;
    }
    
    this.updateSceneTransition(deltaTime);
    
    // Auto-save periodically
    this.saveSystem.updateAutoSave(deltaTime, this.gameState);
  }

  // Pause/Resume
  pauseGame(): void {
    if (this.currentScene === 'game') {
      this.changeScene('pause', 'instant');
    }
  }

  resumeGame(): void {
    if (this.currentScene === 'pause') {
      this.changeScene('game', 'instant');
    }
  }

  togglePause(): void {
    if (this.isPaused) {
      this.resumeGame();
    } else {
      this.pauseGame();
    }
  }

  // Settings Management
  updateSetting<K extends keyof GameSettings>(key: K, value: GameSettings[K]): void {
    if (!this.gameState.settings) {
      this.gameState.settings = this.saveSystem.getSettings()!;
    }
    
    this.gameState.settings[key] = value;
    this.saveSystem.saveSettings(this.gameState.settings);
  }

  getSettings(): GameSettings | null {
    return this.gameState.settings || this.saveSystem.getSettings();
  }

  // Statistics
  updateStatistic<K extends keyof GameStats>(key: K, value: number, operation: 'set' | 'add' = 'add'): void {
    const stats = this.saveSystem.getStats();
    
    if (operation === 'add') {
      (stats[key] as number) += value;
    } else {
      (stats[key] as number) = value;
    }
    
    this.saveSystem.updateStats({ [key]: stats[key] });
    this.hasUnsavedProgress = true;
  }

  getStatistics(): GameStats {
    return this.saveSystem.getStats();
  }

  // Getters
  getCurrentScene(): GameScene {
    return this.currentScene;
  }

  getPreviousScene(): GameScene {
    return this.previousScene;
  }

  getSceneTransition(): SceneTransition | null {
    return this.sceneTransition;
  }

  getTransitionProgress(): number {
    return this.transitionProgress;
  }

  isPaused(): boolean {
    return this.isPaused;
  }

  isPermadeathMode(): boolean {
    return this.permadeathMode;
  }

  hasUnsavedChanges(): boolean {
    return this.hasUnsavedProgress;
  }

  getGameTime(): number {
    return this.gameTime;
  }

  getGameState(): Partial<GameState> {
    return this.gameState;
  }

  // Cleanup
  destroy(): void {
    // Final auto-save
    this.autoSave();
    
    // Update session statistics
    const sessionTime = (Date.now() - this.sessionStartTime) / 1000;
    this.updateStatistic('totalPlayTime', sessionTime, 'add');
  }
}
