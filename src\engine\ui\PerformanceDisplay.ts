/**
 * Performance display overlay for real-time monitoring
 */

import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';
import { PerformanceMonitor, PerformanceMetrics, ProfilerEntry } from '@/engine/core/PerformanceMonitor';

export interface PerformanceDisplayConfig {
  position: { x: number; y: number };
  width: number;
  height: number;
  backgroundColor: string;
  textColor: string;
  graphColor: string;
  alertColor: string;
  fontSize: number;
  showGraph: boolean;
  showProfiler: boolean;
  showAlerts: boolean;
  updateInterval: number;
}

export class PerformanceDisplay {
  private config: PerformanceDisplayConfig;
  private performanceMonitor: PerformanceMonitor;
  private isVisible: boolean = false;
  private lastUpdate: number = 0;
  
  // Graph data
  private fpsHistory: number[] = [];
  private frameTimeHistory: number[] = [];
  private memoryHistory: number[] = [];
  private maxGraphPoints: number = 60;

  constructor(performanceMonitor: PerformanceMonitor, config: Partial<PerformanceDisplayConfig> = {}) {
    this.performanceMonitor = performanceMonitor;
    this.config = {
      position: { x: 10, y: 10 },
      width: 300,
      height: 200,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      textColor: '#ffffff',
      graphColor: '#4CAF50',
      alertColor: '#ff6b6b',
      fontSize: 12,
      showGraph: true,
      showProfiler: true,
      showAlerts: true,
      updateInterval: 100, // Update every 100ms
      ...config
    };
  }

  show(): void {
    this.isVisible = true;
  }

  hide(): void {
    this.isVisible = false;
  }

  toggle(): void {
    this.isVisible = !this.isVisible;
  }

  update(deltaTime: number): void {
    if (!this.isVisible) return;
    
    const currentTime = performance.now();
    if (currentTime - this.lastUpdate < this.config.updateInterval) return;
    
    this.updateGraphData();
    this.lastUpdate = currentTime;
  }

  private updateGraphData(): void {
    const metrics = this.performanceMonitor.getCurrentMetrics();
    
    // Update FPS history
    this.fpsHistory.push(metrics.fps);
    if (this.fpsHistory.length > this.maxGraphPoints) {
      this.fpsHistory.shift();
    }
    
    // Update frame time history
    this.frameTimeHistory.push(metrics.frameTime);
    if (this.frameTimeHistory.length > this.maxGraphPoints) {
      this.frameTimeHistory.shift();
    }
    
    // Update memory history
    this.memoryHistory.push(metrics.memoryUsage);
    if (this.memoryHistory.length > this.maxGraphPoints) {
      this.memoryHistory.shift();
    }
  }

  render(renderer: CanvasRenderer): void {
    if (!this.isVisible) return;
    
    const { x, y } = this.config.position;
    const { width, height } = this.config;
    
    // Draw background
    renderer.setGlobalAlpha(0.9);
    renderer.drawRect(x, y, width, height, this.config.backgroundColor);
    renderer.setGlobalAlpha(1);
    
    // Draw border
    renderer.drawRectOutline(x, y, width, height, this.config.textColor, 1);
    
    let currentY = y + 15;
    
    // Draw performance metrics
    currentY = this.renderMetrics(renderer, x + 10, currentY);
    
    // Draw graphs
    if (this.config.showGraph) {
      currentY = this.renderGraphs(renderer, x + 10, currentY);
    }
    
    // Draw profiler data
    if (this.config.showProfiler) {
      currentY = this.renderProfiler(renderer, x + 10, currentY);
    }
    
    // Draw alerts
    if (this.config.showAlerts) {
      this.renderAlerts(renderer, x + 10, currentY);
    }
  }

  private renderMetrics(renderer: CanvasRenderer, x: number, y: number): number {
    const metrics = this.performanceMonitor.getCurrentMetrics();
    const font = `${this.config.fontSize}px monospace`;
    
    // FPS
    const fpsColor = metrics.fps < 30 ? this.config.alertColor : this.config.textColor;
    renderer.drawText(`FPS: ${metrics.fps.toFixed(1)}`, x, y, font, fpsColor);
    y += this.config.fontSize + 2;
    
    // Frame time
    const frameTimeColor = metrics.frameTime > 33 ? this.config.alertColor : this.config.textColor;
    renderer.drawText(`Frame: ${metrics.frameTime.toFixed(2)}ms`, x, y, font, frameTimeColor);
    y += this.config.fontSize + 2;
    
    // Memory usage
    if (metrics.memoryUsage > 0) {
      const memoryColor = metrics.memoryUsage > 50 ? this.config.alertColor : this.config.textColor;
      renderer.drawText(`Memory: ${metrics.memoryUsage.toFixed(1)}MB`, x, y, font, memoryColor);
      y += this.config.fontSize + 2;
    }
    
    // Object count
    if (metrics.objectCount > 0) {
      renderer.drawText(`Objects: ${metrics.objectCount}`, x, y, font, this.config.textColor);
      y += this.config.fontSize + 2;
    }
    
    // Draw calls
    if (metrics.drawCalls > 0) {
      renderer.drawText(`Draw Calls: ${metrics.drawCalls}`, x, y, font, this.config.textColor);
      y += this.config.fontSize + 2;
    }
    
    return y + 5;
  }

  private renderGraphs(renderer: CanvasRenderer, x: number, y: number): number {
    const graphWidth = this.config.width - 20;
    const graphHeight = 40;
    
    // FPS Graph
    renderer.drawText('FPS Graph:', x, y, `${this.config.fontSize}px monospace`, this.config.textColor);
    y += this.config.fontSize + 2;
    
    this.drawGraph(renderer, x, y, graphWidth, graphHeight, this.fpsHistory, 0, 60, this.config.graphColor);
    y += graphHeight + 10;
    
    // Frame Time Graph
    renderer.drawText('Frame Time Graph:', x, y, `${this.config.fontSize}px monospace`, this.config.textColor);
    y += this.config.fontSize + 2;
    
    const maxFrameTime = Math.max(50, Math.max(...this.frameTimeHistory));
    this.drawGraph(renderer, x, y, graphWidth, graphHeight, this.frameTimeHistory, 0, maxFrameTime, this.config.graphColor);
    y += graphHeight + 10;
    
    return y;
  }

  private drawGraph(
    renderer: CanvasRenderer,
    x: number,
    y: number,
    width: number,
    height: number,
    data: number[],
    minValue: number,
    maxValue: number,
    color: string
  ): void {
    // Draw graph background
    renderer.drawRect(x, y, width, height, 'rgba(255, 255, 255, 0.1)');
    renderer.drawRectOutline(x, y, width, height, this.config.textColor, 1);
    
    if (data.length < 2) return;
    
    // Draw graph line
    renderer.setStrokeStyle(color);
    renderer.setLineWidth(2);
    
    const stepX = width / (this.maxGraphPoints - 1);
    const startIndex = Math.max(0, data.length - this.maxGraphPoints);
    
    for (let i = 1; i < data.length - startIndex; i++) {
      const prevValue = data[startIndex + i - 1];
      const currentValue = data[startIndex + i];
      
      const prevY = y + height - ((prevValue - minValue) / (maxValue - minValue)) * height;
      const currentY = y + height - ((currentValue - minValue) / (maxValue - minValue)) * height;
      
      renderer.drawLine(
        x + (i - 1) * stepX,
        Math.max(y, Math.min(y + height, prevY)),
        x + i * stepX,
        Math.max(y, Math.min(y + height, currentY)),
        color,
        2
      );
    }
  }

  private renderProfiler(renderer: CanvasRenderer, x: number, y: number): number {
    const profilerData = this.performanceMonitor.getProfilerData();
    const font = `${this.config.fontSize - 1}px monospace`;
    
    renderer.drawText('Profiler:', x, y, `${this.config.fontSize}px monospace`, this.config.textColor);
    y += this.config.fontSize + 2;
    
    // Sort by average time (descending)
    const sortedData = profilerData
      .sort((a, b) => b.averageTime - a.averageTime)
      .slice(0, 5); // Show top 5
    
    for (const entry of sortedData) {
      const text = `${entry.name}: ${entry.averageTime.toFixed(2)}ms (${entry.calls})`;
      const color = entry.averageTime > 5 ? this.config.alertColor : this.config.textColor;
      renderer.drawText(text, x, y, font, color);
      y += this.config.fontSize;
    }
    
    return y + 5;
  }

  private renderAlerts(renderer: CanvasRenderer, x: number, y: number): number {
    const alerts = this.performanceMonitor.getAlerts();
    const recentAlerts = alerts.filter(alert => 
      (performance.now() - alert.timestamp) < 5000
    ).slice(-3); // Show last 3 alerts
    
    if (recentAlerts.length === 0) return y;
    
    const font = `${this.config.fontSize - 1}px monospace`;
    
    renderer.drawText('Alerts:', x, y, `${this.config.fontSize}px monospace`, this.config.alertColor);
    y += this.config.fontSize + 2;
    
    for (const alert of recentAlerts) {
      const age = (performance.now() - alert.timestamp) / 1000;
      const alpha = Math.max(0.3, 1 - age / 5);
      
      renderer.setGlobalAlpha(alpha);
      renderer.drawText(alert.message, x, y, font, this.config.alertColor);
      renderer.setGlobalAlpha(1);
      
      y += this.config.fontSize;
    }
    
    return y;
  }

  // Configuration methods
  setPosition(x: number, y: number): void {
    this.config.position = { x, y };
  }

  setSize(width: number, height: number): void {
    this.config.width = width;
    this.config.height = height;
  }

  setShowGraph(show: boolean): void {
    this.config.showGraph = show;
  }

  setShowProfiler(show: boolean): void {
    this.config.showProfiler = show;
  }

  setShowAlerts(show: boolean): void {
    this.config.showAlerts = show;
  }

  // Getters
  isDisplayVisible(): boolean {
    return this.isVisible;
  }

  getConfig(): PerformanceDisplayConfig {
    return { ...this.config };
  }
}
