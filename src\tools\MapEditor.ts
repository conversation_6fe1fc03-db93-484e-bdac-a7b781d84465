/**
 * Map Editor for creating and editing custom levels
 */

import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';
import { InputManager } from '@/engine/input/InputManager';
import { Vector2 } from '@/utils/math/Vector2';
import { DungeonGenerator, Room, Corridor } from '@/game/systems/DungeonGenerator';
import { ErrorHandler } from '@/utils/error/ErrorHandler';

export interface MapTile {
  x: number;
  y: number;
  type: 'wall' | 'floor' | 'door' | 'spawn' | 'enemy' | 'treasure' | 'boss';
  properties?: { [key: string]: any };
}

export interface CustomMap {
  id: string;
  name: string;
  width: number;
  height: number;
  tiles: MapTile[][];
  metadata: {
    author: string;
    description: string;
    difficulty: number;
    created: number;
    modified: number;
  };
}

export interface EditorTool {
  id: string;
  name: string;
  icon: string;
  tileType: string;
  cursor: string;
}

export class MapEditor {
  private canvas: HTMLCanvasElement;
  private renderer: CanvasRenderer;
  private inputManager: InputManager;
  private errorHandler: ErrorHandler;
  
  // Map data
  private currentMap: CustomMap;
  private mapHistory: CustomMap[] = [];
  private historyIndex: number = -1;
  private maxHistorySize: number = 50;
  
  // Editor state
  private selectedTool: EditorTool;
  private isDrawing: boolean = false;
  private camera: Vector2 = new Vector2(0, 0);
  private zoom: number = 1.0;
  private gridSize: number = 32;
  private showGrid: boolean = true;
  
  // UI state
  private toolPalette: EditorTool[] = [];
  private isPlaying: boolean = false;
  private isDirty: boolean = false;
  
  // Tools
  private tools: EditorTool[] = [
    { id: 'wall', name: 'Wall', icon: '🧱', tileType: 'wall', cursor: 'crosshair' },
    { id: 'floor', name: 'Floor', icon: '⬜', tileType: 'floor', cursor: 'crosshair' },
    { id: 'door', name: 'Door', icon: '🚪', tileType: 'door', cursor: 'crosshair' },
    { id: 'spawn', name: 'Player Spawn', icon: '🟢', tileType: 'spawn', cursor: 'crosshair' },
    { id: 'enemy', name: 'Enemy Spawn', icon: '🔴', tileType: 'enemy', cursor: 'crosshair' },
    { id: 'treasure', name: 'Treasure', icon: '💎', tileType: 'treasure', cursor: 'crosshair' },
    { id: 'boss', name: 'Boss Area', icon: '👹', tileType: 'boss', cursor: 'crosshair' },
    { id: 'eraser', name: 'Eraser', icon: '🧽', tileType: 'floor', cursor: 'crosshair' }
  ];

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.renderer = new CanvasRenderer(canvas);
    this.inputManager = new InputManager(canvas);
    this.errorHandler = ErrorHandler.getInstance();
    
    this.selectedTool = this.tools[0];
    this.createNewMap();
    this.setupEventListeners();
    
    console.log('🛠️ Map Editor initialized');
  }

  private setupEventListeners(): void {
    // Mouse events for drawing
    this.inputManager.onMouseDown(0, (position) => this.startDrawing(position));
    this.inputManager.onMouseMove((position) => this.updateDrawing(position));
    this.inputManager.onMouseUp(0, () => this.stopDrawing());
    
    // Keyboard shortcuts
    this.inputManager.onKeyDown('KeyN', () => this.createNewMap());
    this.inputManager.onKeyDown('KeyS', () => this.saveMap());
    this.inputManager.onKeyDown('KeyL', () => this.loadMap());
    this.inputManager.onKeyDown('KeyZ', () => this.undo());
    this.inputManager.onKeyDown('KeyY', () => this.redo());
    this.inputManager.onKeyDown('KeyG', () => this.toggleGrid());
    this.inputManager.onKeyDown('Space', () => this.togglePlayMode());
    
    // Tool selection (1-8 keys)
    for (let i = 1; i <= 8; i++) {
      this.inputManager.onKeyDown(`Digit${i}`, () => this.selectTool(i - 1));
    }
    
    // Camera controls
    this.inputManager.onKeyDown('ArrowUp', () => this.moveCamera(0, -1));
    this.inputManager.onKeyDown('ArrowDown', () => this.moveCamera(0, 1));
    this.inputManager.onKeyDown('ArrowLeft', () => this.moveCamera(-1, 0));
    this.inputManager.onKeyDown('ArrowRight', () => this.moveCamera(1, 0));
    
    // Zoom controls
    this.canvas.addEventListener('wheel', (e) => {
      e.preventDefault();
      const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
      this.setZoom(this.zoom * zoomFactor);
    });
  }

  private createNewMap(): void {
    this.currentMap = {
      id: this.generateMapId(),
      name: 'Untitled Map',
      width: 50,
      height: 50,
      tiles: [],
      metadata: {
        author: 'Map Editor',
        description: 'A custom map created with the map editor',
        difficulty: 1,
        created: Date.now(),
        modified: Date.now()
      }
    };
    
    // Initialize tiles with walls on borders, floor inside
    this.currentMap.tiles = [];
    for (let y = 0; y < this.currentMap.height; y++) {
      this.currentMap.tiles[y] = [];
      for (let x = 0; x < this.currentMap.width; x++) {
        const isWall = x === 0 || x === this.currentMap.width - 1 || 
                      y === 0 || y === this.currentMap.height - 1;
        
        this.currentMap.tiles[y][x] = {
          x,
          y,
          type: isWall ? 'wall' : 'floor'
        };
      }
    }
    
    // Add player spawn in center
    const centerX = Math.floor(this.currentMap.width / 2);
    const centerY = Math.floor(this.currentMap.height / 2);
    this.currentMap.tiles[centerY][centerX].type = 'spawn';
    
    this.saveToHistory();
    this.isDirty = false;
    
    console.log('📝 New map created');
  }

  private startDrawing(position: Vector2): void {
    this.isDrawing = true;
    this.placeTile(position);
  }

  private updateDrawing(position: Vector2): void {
    if (this.isDrawing) {
      this.placeTile(position);
    }
  }

  private stopDrawing(): void {
    if (this.isDrawing) {
      this.isDrawing = false;
      this.saveToHistory();
    }
  }

  private placeTile(screenPosition: Vector2): void {
    const worldPosition = this.screenToWorld(screenPosition);
    const tileX = Math.floor(worldPosition.x / this.gridSize);
    const tileY = Math.floor(worldPosition.y / this.gridSize);
    
    if (this.isValidTilePosition(tileX, tileY)) {
      const currentTile = this.currentMap.tiles[tileY][tileX];
      
      // Don't place if it's the same type
      if (currentTile.type === this.selectedTool.tileType) return;
      
      // Special handling for spawn points (only one allowed)
      if (this.selectedTool.tileType === 'spawn') {
        this.clearExistingSpawns();
      }
      
      currentTile.type = this.selectedTool.tileType as any;
      this.isDirty = true;
    }
  }

  private clearExistingSpawns(): void {
    for (let y = 0; y < this.currentMap.height; y++) {
      for (let x = 0; x < this.currentMap.width; x++) {
        if (this.currentMap.tiles[y][x].type === 'spawn') {
          this.currentMap.tiles[y][x].type = 'floor';
        }
      }
    }
  }

  private isValidTilePosition(x: number, y: number): boolean {
    return x >= 0 && x < this.currentMap.width && 
           y >= 0 && y < this.currentMap.height;
  }

  private screenToWorld(screenPosition: Vector2): Vector2 {
    return new Vector2(
      (screenPosition.x / this.zoom) + this.camera.x,
      (screenPosition.y / this.zoom) + this.camera.y
    );
  }

  private worldToScreen(worldPosition: Vector2): Vector2 {
    return new Vector2(
      (worldPosition.x - this.camera.x) * this.zoom,
      (worldPosition.y - this.camera.y) * this.zoom
    );
  }

  // History management
  private saveToHistory(): void {
    // Remove any history after current index
    this.mapHistory = this.mapHistory.slice(0, this.historyIndex + 1);
    
    // Add current state
    this.mapHistory.push(JSON.parse(JSON.stringify(this.currentMap)));
    this.historyIndex++;
    
    // Limit history size
    if (this.mapHistory.length > this.maxHistorySize) {
      this.mapHistory.shift();
      this.historyIndex--;
    }
  }

  private undo(): void {
    if (this.historyIndex > 0) {
      this.historyIndex--;
      this.currentMap = JSON.parse(JSON.stringify(this.mapHistory[this.historyIndex]));
      this.isDirty = true;
      console.log('↶ Undo');
    }
  }

  private redo(): void {
    if (this.historyIndex < this.mapHistory.length - 1) {
      this.historyIndex++;
      this.currentMap = JSON.parse(JSON.stringify(this.mapHistory[this.historyIndex]));
      this.isDirty = true;
      console.log('↷ Redo');
    }
  }

  // Tool management
  private selectTool(index: number): void {
    if (index >= 0 && index < this.tools.length) {
      this.selectedTool = this.tools[index];
      this.canvas.style.cursor = this.selectedTool.cursor;
      console.log(`🔧 Selected tool: ${this.selectedTool.name}`);
    }
  }

  // Camera controls
  private moveCamera(deltaX: number, deltaY: number): void {
    const speed = 32 / this.zoom;
    this.camera.x += deltaX * speed;
    this.camera.y += deltaY * speed;
  }

  private setZoom(newZoom: number): void {
    this.zoom = Math.max(0.25, Math.min(4.0, newZoom));
  }

  private toggleGrid(): void {
    this.showGrid = !this.showGrid;
    console.log(`Grid: ${this.showGrid ? 'ON' : 'OFF'}`);
  }

  // Map operations
  private saveMap(): void {
    try {
      this.currentMap.metadata.modified = Date.now();
      const mapData = JSON.stringify(this.currentMap, null, 2);
      
      // Save to localStorage
      const mapKey = `custom_map_${this.currentMap.id}`;
      localStorage.setItem(mapKey, mapData);
      
      // Update map list
      const mapList = this.getMapList();
      if (!mapList.includes(this.currentMap.id)) {
        mapList.push(this.currentMap.id);
        localStorage.setItem('custom_map_list', JSON.stringify(mapList));
      }
      
      this.isDirty = false;
      console.log(`💾 Map saved: ${this.currentMap.name}`);
      
      // Also trigger download
      this.downloadMap();
      
    } catch (error) {
      this.errorHandler.error('Failed to save map', error, 'map_editor');
    }
  }

  private loadMap(): void {
    // For now, just load the first available map
    const mapList = this.getMapList();
    if (mapList.length > 0) {
      const mapKey = `custom_map_${mapList[0]}`;
      const mapData = localStorage.getItem(mapKey);
      
      if (mapData) {
        try {
          this.currentMap = JSON.parse(mapData);
          this.saveToHistory();
          this.isDirty = false;
          console.log(`📁 Map loaded: ${this.currentMap.name}`);
        } catch (error) {
          this.errorHandler.error('Failed to load map', error, 'map_editor');
        }
      }
    }
  }

  private downloadMap(): void {
    const mapData = JSON.stringify(this.currentMap, null, 2);
    const blob = new Blob([mapData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${this.currentMap.name.replace(/[^a-z0-9]/gi, '_')}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
  }

  private getMapList(): string[] {
    try {
      const listData = localStorage.getItem('custom_map_list');
      return listData ? JSON.parse(listData) : [];
    } catch {
      return [];
    }
  }

  private togglePlayMode(): void {
    this.isPlaying = !this.isPlaying;
    console.log(`🎮 Play mode: ${this.isPlaying ? 'ON' : 'OFF'}`);
  }

  // Rendering
  render(): void {
    this.renderer.clear('#1a1a2e');
    
    this.renderer.save();
    
    // Apply camera transform
    this.renderer.translate(-this.camera.x * this.zoom, -this.camera.y * this.zoom);
    this.renderer.scale(this.zoom, this.zoom);
    
    // Render grid
    if (this.showGrid) {
      this.renderGrid();
    }
    
    // Render map
    this.renderMap();
    
    this.renderer.restore();
    
    // Render UI
    this.renderUI();
  }

  private renderGrid(): void {
    const startX = Math.floor(this.camera.x / this.gridSize) * this.gridSize;
    const startY = Math.floor(this.camera.y / this.gridSize) * this.gridSize;
    const endX = startX + (this.canvas.width / this.zoom) + this.gridSize;
    const endY = startY + (this.canvas.height / this.zoom) + this.gridSize;
    
    this.renderer.setStrokeStyle('rgba(255, 255, 255, 0.1)');
    this.renderer.setLineWidth(1);
    
    // Vertical lines
    for (let x = startX; x <= endX; x += this.gridSize) {
      this.renderer.drawLine(x, startY, x, endY, 'rgba(255, 255, 255, 0.1)', 1);
    }
    
    // Horizontal lines
    for (let y = startY; y <= endY; y += this.gridSize) {
      this.renderer.drawLine(startX, y, endX, y, 'rgba(255, 255, 255, 0.1)', 1);
    }
  }

  private renderMap(): void {
    const startTileX = Math.max(0, Math.floor(this.camera.x / this.gridSize));
    const startTileY = Math.max(0, Math.floor(this.camera.y / this.gridSize));
    const endTileX = Math.min(this.currentMap.width, startTileX + Math.ceil(this.canvas.width / (this.gridSize * this.zoom)) + 1);
    const endTileY = Math.min(this.currentMap.height, startTileY + Math.ceil(this.canvas.height / (this.gridSize * this.zoom)) + 1);
    
    for (let y = startTileY; y < endTileY; y++) {
      for (let x = startTileX; x < endTileX; x++) {
        if (this.isValidTilePosition(x, y)) {
          this.renderTile(this.currentMap.tiles[y][x]);
        }
      }
    }
  }

  private renderTile(tile: MapTile): void {
    const x = tile.x * this.gridSize;
    const y = tile.y * this.gridSize;
    
    let color: string;
    let icon: string = '';
    
    switch (tile.type) {
      case 'wall':
        color = '#444444';
        break;
      case 'floor':
        color = '#888888';
        break;
      case 'door':
        color = '#8B4513';
        icon = '🚪';
        break;
      case 'spawn':
        color = '#00ff00';
        icon = '🟢';
        break;
      case 'enemy':
        color = '#ff0000';
        icon = '🔴';
        break;
      case 'treasure':
        color = '#ffff00';
        icon = '💎';
        break;
      case 'boss':
        color = '#800080';
        icon = '👹';
        break;
      default:
        color = '#888888';
    }
    
    this.renderer.drawRect(x, y, this.gridSize, this.gridSize, color);
    
    if (icon) {
      this.renderer.drawText(
        icon,
        x + this.gridSize / 2,
        y + this.gridSize / 2,
        `${this.gridSize * 0.6}px Arial`,
        '#ffffff',
        'center',
        'middle'
      );
    }
  }

  private renderUI(): void {
    // Tool palette
    this.renderToolPalette();
    
    // Status bar
    this.renderStatusBar();
    
    // Instructions
    this.renderInstructions();
  }

  private renderToolPalette(): void {
    const paletteX = 10;
    const paletteY = 10;
    const buttonSize = 40;
    const spacing = 5;
    
    for (let i = 0; i < this.tools.length; i++) {
      const tool = this.tools[i];
      const x = paletteX + i * (buttonSize + spacing);
      const y = paletteY;
      
      // Button background
      const isSelected = tool === this.selectedTool;
      const bgColor = isSelected ? 'rgba(0, 150, 255, 0.8)' : 'rgba(0, 0, 0, 0.7)';
      this.renderer.drawRect(x, y, buttonSize, buttonSize, bgColor);
      
      // Button border
      const borderColor = isSelected ? '#00aaff' : '#666666';
      this.renderer.drawRectOutline(x, y, buttonSize, buttonSize, borderColor, 2);
      
      // Tool icon
      this.renderer.drawText(
        tool.icon,
        x + buttonSize / 2,
        y + buttonSize / 2,
        '20px Arial',
        '#ffffff',
        'center',
        'middle'
      );
      
      // Tool number
      this.renderer.drawText(
        (i + 1).toString(),
        x + buttonSize - 5,
        y + 12,
        '10px Arial',
        '#ffffff',
        'right'
      );
    }
  }

  private renderStatusBar(): void {
    const statusY = this.canvas.height - 60;
    
    // Background
    this.renderer.drawRect(0, statusY, this.canvas.width, 60, 'rgba(0, 0, 0, 0.8)');
    
    // Status text
    const status = [
      `Map: ${this.currentMap.name} (${this.currentMap.width}x${this.currentMap.height})`,
      `Tool: ${this.selectedTool.name}`,
      `Zoom: ${(this.zoom * 100).toFixed(0)}%`,
      `Camera: (${Math.round(this.camera.x)}, ${Math.round(this.camera.y)})`,
      this.isDirty ? '● Unsaved' : '✓ Saved'
    ].join(' | ');
    
    this.renderer.drawText(status, 10, statusY + 20, '14px Arial', '#ffffff');
    
    // Mode indicator
    if (this.isPlaying) {
      this.renderer.drawText('🎮 PLAY MODE', 10, statusY + 40, '14px Arial', '#00ff00');
    }
  }

  private renderInstructions(): void {
    const instructions = [
      'N - New Map | S - Save | L - Load | Z - Undo | Y - Redo',
      'G - Toggle Grid | Space - Play Mode | Arrow Keys - Move Camera',
      '1-8 - Select Tools | Mouse Wheel - Zoom | Click & Drag - Paint'
    ];
    
    const startY = 70;
    for (let i = 0; i < instructions.length; i++) {
      this.renderer.drawText(
        instructions[i],
        10,
        startY + i * 20,
        '12px Arial',
        'rgba(255, 255, 255, 0.7)'
      );
    }
  }

  // Utility methods
  private generateMapId(): string {
    return `map_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Public API
  getCurrentMap(): CustomMap {
    return this.currentMap;
  }

  loadCustomMap(mapData: CustomMap): void {
    this.currentMap = mapData;
    this.saveToHistory();
    this.isDirty = false;
  }

  exportToGameFormat(): any {
    // Convert to format compatible with DungeonGenerator
    const rooms: Room[] = [];
    const corridors: Corridor[] = [];
    
    // Find connected floor areas as rooms
    // This is a simplified conversion - in a full implementation,
    // you'd want more sophisticated room detection
    
    return {
      width: this.currentMap.width,
      height: this.currentMap.height,
      rooms,
      corridors,
      tiles: this.currentMap.tiles
    };
  }

  update(deltaTime: number): void {
    this.inputManager.update();
  }

  destroy(): void {
    this.inputManager.destroy();
    this.renderer.destroy();
  }
}
