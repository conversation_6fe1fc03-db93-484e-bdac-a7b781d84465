/**
 * Game constants and configuration values
 * Centralizes magic numbers and commonly used values
 */

// Screen and Canvas
export const SCREEN = {
  WIDTH: 1024,
  HEIGHT: 768,
  TARGET_FPS: 60,
  FRAME_TIME_MS: 1000 / 60, // ~16.67ms
  PIXEL_RATIO: window.devicePixelRatio || 1
} as const;

// Performance
export const PERFORMANCE = {
  MAX_FRAME_TIME: 33, // 33ms = 30 FPS minimum
  COLLISION_TIME_BUDGET: 5, // 5ms per frame for collision detection
  MAX_OBJECTS_PER_FRAME: 1000,
  OBJECT_POOL_SIZES: {
    BULLETS: 200,
    PARTICLES: 500,
    ENEMIES: 100
  }
} as const;

// Physics
export const PHYSICS = {
  GRAVITY: 980, // pixels/second²
  FRICTION: 0.8,
  AIR_RESISTANCE: 0.99,
  COLLISION_EPSILON: 0.001,
  SPATIAL_GRID_CELL_SIZE: 64
} as const;

// Player
export const PLAYER = {
  DEFAULT_HEALTH: 100,
  DEFAULT_MANA: 50,
  DEFAULT_SPEED: 200, // pixels/second
  DEFAULT_DAMAGE: 25,
  DEFAULT_DEFENSE: 5,
  COLLISION_RADIUS: 16,
  INVULNERABILITY_TIME: 1.0, // seconds
  ATTACK_COOLDOWN: 0.25, // seconds
  REGENERATION_RATE: 5 // health per second
} as const;

// Enemies
export const ENEMIES = {
  SPAWN_INTERVAL: 2.0, // seconds
  MIN_SPAWN_INTERVAL: 0.5, // seconds
  SPAWN_DISTANCE_MIN: 400, // pixels from player
  SPAWN_DISTANCE_MAX: 600,
  BASIC: {
    HEALTH: 50,
    SPEED: 100,
    DAMAGE: 15,
    COLLISION_RADIUS: 12,
    EXPERIENCE_VALUE: 10
  },
  SHOOTER: {
    HEALTH: 30,
    SPEED: 80,
    DAMAGE: 20,
    COLLISION_RADIUS: 10,
    EXPERIENCE_VALUE: 15,
    ATTACK_RANGE: 300,
    ATTACK_COOLDOWN: 1.5
  },
  HEAVY: {
    HEALTH: 150,
    SPEED: 60,
    DAMAGE: 30,
    COLLISION_RADIUS: 20,
    EXPERIENCE_VALUE: 25
  }
} as const;

// Projectiles
export const PROJECTILES = {
  PLAYER_BULLET: {
    SPEED: 500, // pixels/second
    DAMAGE: 25,
    SIZE: 8,
    COLOR: '#00ff00',
    LIFETIME: 3.0 // seconds
  },
  ENEMY_BULLET: {
    SPEED: 300,
    DAMAGE: 15,
    SIZE: 6,
    COLOR: '#ff6666',
    LIFETIME: 5.0
  }
} as const;

// UI
export const UI = {
  HEALTH_BAR: {
    WIDTH: 200,
    HEIGHT: 20,
    X: 20,
    Y: 20
  },
  EXPERIENCE_BAR: {
    WIDTH: 200,
    HEIGHT: 10,
    X: 20,
    Y: 50
  },
  FONT_SIZES: {
    SMALL: '12px',
    MEDIUM: '16px',
    LARGE: '24px',
    TITLE: '32px'
  },
  COLORS: {
    HEALTH: '#4CAF50',
    HEALTH_LOW: '#f44336',
    HEALTH_MEDIUM: '#ff9800',
    MANA: '#2196F3',
    EXPERIENCE: '#00ff00',
    TEXT: '#ffffff',
    TEXT_SECONDARY: '#cccccc',
    BACKGROUND: '#000000'
  }
} as const;

// Progression
export const PROGRESSION = {
  BASE_EXPERIENCE_TO_LEVEL: 100,
  EXPERIENCE_SCALING: 1.5, // Multiplier per level
  MAX_LEVEL: 50,
  SKILL_POINTS_PER_LEVEL: 1,
  STAT_INCREASE_PER_LEVEL: {
    HEALTH: 10,
    MANA: 5,
    DAMAGE: 2,
    SPEED: 5,
    DEFENSE: 1
  }
} as const;

// Loot
export const LOOT = {
  DROP_RATES: {
    COMMON: 0.7,
    UNCOMMON: 0.2,
    RARE: 0.08,
    EPIC: 0.015,
    LEGENDARY: 0.005
  },
  PICKUP_RANGE: 32, // pixels
  DESPAWN_TIME: 30.0, // seconds
  COLORS: {
    COMMON: '#ffffff',
    UNCOMMON: '#00ff00',
    RARE: '#0080ff',
    EPIC: '#8000ff',
    LEGENDARY: '#ff8000'
  }
} as const;

// Audio
export const AUDIO = {
  MASTER_VOLUME: 0.7,
  SFX_VOLUME: 0.8,
  MUSIC_VOLUME: 0.6,
  FADE_TIME: 1.0, // seconds
  MAX_CONCURRENT_SOUNDS: 32
} as const;

// Input
export const INPUT = {
  DEADZONE: 0.1, // For gamepad analog sticks
  DOUBLE_TAP_TIME: 300, // milliseconds
  LONG_PRESS_TIME: 500, // milliseconds
  TOUCH_SENSITIVITY: 1.0
} as const;

// Particles
export const PARTICLES = {
  MAX_PARTICLES: 500,
  DEFAULT_LIFETIME: 2.0, // seconds
  GRAVITY_SCALE: 0.5,
  SIZE_VARIATION: 0.3,
  VELOCITY_VARIATION: 0.2
} as const;

// Debug
export const DEBUG = {
  COLLISION_COLOR: '#ff0000',
  VELOCITY_COLOR: '#00ff00',
  PATH_COLOR: '#0000ff',
  GRID_COLOR: '#333333',
  TEXT_COLOR: '#ffffff',
  BACKGROUND_ALPHA: 0.3
} as const;

// File paths
export const PATHS = {
  ASSETS: './assets/',
  IMAGES: './assets/images/',
  AUDIO: './assets/audio/',
  FONTS: './assets/fonts/',
  DATA: './assets/data/'
} as const;

// Validation
export const VALIDATION = {
  MIN_CANVAS_SIZE: 320,
  MAX_CANVAS_SIZE: 4096,
  MIN_FPS: 15,
  MAX_FPS: 144,
  MAX_STRING_LENGTH: 1000,
  MAX_ARRAY_LENGTH: 10000
} as const;

// Error handling
export const ERROR_HANDLING = {
  MAX_STORED_ERRORS: 10,
  ERROR_REPORT_INTERVAL: 5000, // milliseconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000 // milliseconds
} as const;

// Export all constants as a single object for convenience
export const GAME_CONSTANTS = {
  SCREEN,
  PERFORMANCE,
  PHYSICS,
  PLAYER,
  ENEMIES,
  PROJECTILES,
  UI,
  PROGRESSION,
  LOOT,
  AUDIO,
  INPUT,
  PARTICLES,
  DEBUG,
  PATHS,
  VALIDATION,
  ERROR_HANDLING
} as const;

export default GAME_CONSTANTS;
