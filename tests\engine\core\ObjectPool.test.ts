import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ObjectPool, PoolManager, Poolable } from '@/engine/core/ObjectPool';

// Mock poolable object for testing
class MockPoolableObject implements Poolable {
  private active: boolean = false;
  public resetCalled: boolean = false;
  public id: number;
  
  constructor(id: number = 0) {
    this.id = id;
  }

  reset(): void {
    this.resetCalled = true;
    this.id = 0;
  }

  isActive(): boolean {
    return this.active;
  }

  setActive(active: boolean): void {
    this.active = active;
  }
}

describe('ObjectPool', () => {
  let pool: ObjectPool<MockPoolableObject>;
  let createFunction: () => MockPoolableObject;
  let resetFunction: (obj: MockPoolableObject) => void;

  beforeEach(() => {
    let idCounter = 0;
    createFunction = vi.fn(() => new MockPoolableObject(++idCounter));
    resetFunction = vi.fn((obj: MockPoolableObject) => {
      obj.id = 999; // Custom reset behavior
    });
    
    pool = new ObjectPool(createFunction, 3, 10, resetFunction);
  });

  describe('initialization', () => {
    it('should preallocate initial objects', () => {
      expect(pool.getPoolSize()).toBe(3);
      expect(pool.getActiveCount()).toBe(0);
      expect(pool.getTotalObjects()).toBe(3);
    });

    it('should call create function for initial objects', () => {
      expect(createFunction).toHaveBeenCalledTimes(3);
    });
  });

  describe('acquire', () => {
    it('should return an object from the pool', () => {
      const obj = pool.acquire();
      
      expect(obj).toBeInstanceOf(MockPoolableObject);
      expect(obj.isActive()).toBe(true);
      expect(obj.resetCalled).toBe(true);
      expect(pool.getActiveCount()).toBe(1);
      expect(pool.getPoolSize()).toBe(2);
    });

    it('should call custom reset function if provided', () => {
      const obj = pool.acquire();
      
      expect(resetFunction).toHaveBeenCalledWith(obj);
      expect(obj.id).toBe(999); // Custom reset value
    });

    it('should create new objects when pool is empty but under max size', () => {
      // Acquire all preallocated objects
      const obj1 = pool.acquire();
      const obj2 = pool.acquire();
      const obj3 = pool.acquire();
      
      expect(pool.getPoolSize()).toBe(0);
      expect(pool.getActiveCount()).toBe(3);
      
      // This should create a new object
      const obj4 = pool.acquire();
      
      expect(obj4).toBeInstanceOf(MockPoolableObject);
      expect(pool.getActiveCount()).toBe(4);
      expect(createFunction).toHaveBeenCalledTimes(4); // 3 initial + 1 new
    });

    it('should implement graceful degradation when pool is full', () => {
      // Fill the pool to max capacity
      const objects = [];
      for (let i = 0; i < 10; i++) {
        objects.push(pool.acquire());
      }
      
      expect(pool.getActiveCount()).toBe(10);
      
      // This should trigger graceful degradation
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      const obj = pool.acquire();
      
      expect(obj).toBeInstanceOf(MockPoolableObject);
      expect(consoleSpy).toHaveBeenCalledWith('Object pool is full, reusing oldest active object');
      
      consoleSpy.mockRestore();
    });
  });

  describe('release', () => {
    it('should return object to pool', () => {
      const obj = pool.acquire();
      expect(pool.getActiveCount()).toBe(1);
      expect(pool.getPoolSize()).toBe(2);
      
      pool.release(obj);
      
      expect(obj.isActive()).toBe(false);
      expect(pool.getActiveCount()).toBe(0);
      expect(pool.getPoolSize()).toBe(3);
    });

    it('should warn when releasing object not in active list', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      const obj = new MockPoolableObject();
      
      pool.release(obj);
      
      expect(consoleSpy).toHaveBeenCalledWith('Trying to release object that is not in active list');
      consoleSpy.mockRestore();
    });
  });

  describe('releaseAll', () => {
    it('should release all active objects', () => {
      const obj1 = pool.acquire();
      const obj2 = pool.acquire();
      const obj3 = pool.acquire();
      
      expect(pool.getActiveCount()).toBe(3);
      expect(pool.getPoolSize()).toBe(0);
      
      pool.releaseAll();
      
      expect(pool.getActiveCount()).toBe(0);
      expect(pool.getPoolSize()).toBe(3);
      expect(obj1.isActive()).toBe(false);
      expect(obj2.isActive()).toBe(false);
      expect(obj3.isActive()).toBe(false);
    });
  });

  describe('update', () => {
    it('should automatically release inactive objects', () => {
      const obj1 = pool.acquire();
      const obj2 = pool.acquire();
      
      expect(pool.getActiveCount()).toBe(2);
      
      // Manually deactivate one object
      obj1.setActive(false);
      
      pool.update();
      
      expect(pool.getActiveCount()).toBe(1);
      expect(pool.getPoolSize()).toBe(2);
    });
  });

  describe('stats', () => {
    it('should return correct statistics', () => {
      const obj1 = pool.acquire();
      const obj2 = pool.acquire();
      
      const stats = pool.getStats();
      
      expect(stats.active).toBe(2);
      expect(stats.pooled).toBe(1);
      expect(stats.total).toBe(3);
      expect(stats.maxSize).toBe(10);
      expect(stats.utilization).toBe(0.3);
    });
  });

  describe('resize', () => {
    it('should adjust max size and remove excess objects', () => {
      // Fill pool with more objects
      for (let i = 0; i < 5; i++) {
        pool.acquire();
      }
      
      expect(pool.getTotalObjects()).toBe(5);
      
      // Resize to smaller capacity
      pool.resize(3);
      
      expect(pool.getTotalObjects()).toBeLessThanOrEqual(3);
    });
  });
});

describe('PoolManager', () => {
  let poolManager: PoolManager;

  beforeEach(() => {
    poolManager = new PoolManager();
  });

  describe('registerPool', () => {
    it('should register a new pool', () => {
      const createFn = () => new MockPoolableObject();
      const pool = poolManager.registerPool('test', createFn, 5, 20);
      
      expect(pool).toBeInstanceOf(ObjectPool);
      expect(pool.getPoolSize()).toBe(5);
    });
  });

  describe('getPool', () => {
    it('should return registered pool', () => {
      const createFn = () => new MockPoolableObject();
      const registeredPool = poolManager.registerPool('test', createFn);
      
      const retrievedPool = poolManager.getPool('test');
      
      expect(retrievedPool).toBe(registeredPool);
    });

    it('should return null for non-existent pool', () => {
      const pool = poolManager.getPool('nonexistent');
      expect(pool).toBeNull();
    });
  });

  describe('acquire', () => {
    it('should acquire object from named pool', () => {
      const createFn = () => new MockPoolableObject();
      poolManager.registerPool('test', createFn);
      
      const obj = poolManager.acquire('test');
      
      expect(obj).toBeInstanceOf(MockPoolableObject);
      expect(obj?.isActive()).toBe(true);
    });

    it('should return null for non-existent pool', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      const obj = poolManager.acquire('nonexistent');
      
      expect(obj).toBeNull();
      expect(consoleSpy).toHaveBeenCalledWith("Pool 'nonexistent' not found");
      
      consoleSpy.mockRestore();
    });
  });

  describe('release', () => {
    it('should release object to named pool', () => {
      const createFn = () => new MockPoolableObject();
      poolManager.registerPool('test', createFn);
      
      const obj = poolManager.acquire('test') as MockPoolableObject;
      expect(obj.isActive()).toBe(true);
      
      poolManager.release('test', obj);
      expect(obj.isActive()).toBe(false);
    });
  });

  describe('updateAll', () => {
    it('should update all registered pools', () => {
      const createFn = () => new MockPoolableObject();
      const pool1 = poolManager.registerPool('test1', createFn);
      const pool2 = poolManager.registerPool('test2', createFn);
      
      const updateSpy1 = vi.spyOn(pool1, 'update');
      const updateSpy2 = vi.spyOn(pool2, 'update');
      
      poolManager.updateAll();
      
      expect(updateSpy1).toHaveBeenCalled();
      expect(updateSpy2).toHaveBeenCalled();
    });
  });

  describe('getStats', () => {
    it('should return stats for all pools', () => {
      const createFn = () => new MockPoolableObject();
      poolManager.registerPool('test1', createFn);
      poolManager.registerPool('test2', createFn);
      
      const stats = poolManager.getStats();
      
      expect(stats.has('test1')).toBe(true);
      expect(stats.has('test2')).toBe(true);
    });
  });

  describe('clearAll and destroy', () => {
    it('should clear all pools', () => {
      const createFn = () => new MockPoolableObject();
      const pool = poolManager.registerPool('test', createFn);
      
      const clearSpy = vi.spyOn(pool, 'clear');
      
      poolManager.clearAll();
      
      expect(clearSpy).toHaveBeenCalled();
    });

    it('should destroy all pools', () => {
      const createFn = () => new MockPoolableObject();
      poolManager.registerPool('test', createFn);
      
      poolManager.destroy();
      
      const stats = poolManager.getStats();
      expect(stats.size).toBe(0);
    });
  });
});
