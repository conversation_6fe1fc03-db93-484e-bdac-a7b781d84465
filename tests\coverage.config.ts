/**
 * Test coverage configuration and reporting utilities
 */

export interface CoverageThresholds {
  statements: number;
  branches: number;
  functions: number;
  lines: number;
}

export interface CoverageReport {
  overall: CoverageThresholds;
  perFile: Map<string, CoverageThresholds>;
  timestamp: string;
  testCount: number;
  passedTests: number;
  failedTests: number;
}

export class CoverageReporter {
  private static readonly MINIMUM_THRESHOLDS: CoverageThresholds = {
    statements: 70,
    branches: 70,
    functions: 70,
    lines: 70
  };

  private static readonly CRITICAL_FILES_THRESHOLDS: CoverageThresholds = {
    statements: 85,
    branches: 80,
    functions: 85,
    lines: 85
  };

  private static readonly CRITICAL_FILES = [
    'src/engine/core/ObjectPool.ts',
    'src/engine/physics/CollisionSystem.ts',
    'src/engine/physics/SpatialGrid.ts',
    'src/utils/math/Vector2.ts',
    'src/game/scenes/GameScene.ts'
  ];

  static validateCoverage(report: CoverageReport): {
    passed: boolean;
    failures: string[];
    warnings: string[];
  } {
    const failures: string[] = [];
    const warnings: string[] = [];

    // Check overall coverage
    const overall = report.overall;
    if (overall.statements < this.MINIMUM_THRESHOLDS.statements) {
      failures.push(`Overall statement coverage ${overall.statements}% is below minimum ${this.MINIMUM_THRESHOLDS.statements}%`);
    }
    if (overall.branches < this.MINIMUM_THRESHOLDS.branches) {
      failures.push(`Overall branch coverage ${overall.branches}% is below minimum ${this.MINIMUM_THRESHOLDS.branches}%`);
    }
    if (overall.functions < this.MINIMUM_THRESHOLDS.functions) {
      failures.push(`Overall function coverage ${overall.functions}% is below minimum ${this.MINIMUM_THRESHOLDS.functions}%`);
    }
    if (overall.lines < this.MINIMUM_THRESHOLDS.lines) {
      failures.push(`Overall line coverage ${overall.lines}% is below minimum ${this.MINIMUM_THRESHOLDS.lines}%`);
    }

    // Check critical files
    for (const criticalFile of this.CRITICAL_FILES) {
      const fileCoverage = report.perFile.get(criticalFile);
      if (!fileCoverage) {
        warnings.push(`No coverage data found for critical file: ${criticalFile}`);
        continue;
      }

      if (fileCoverage.statements < this.CRITICAL_FILES_THRESHOLDS.statements) {
        failures.push(`${criticalFile} statement coverage ${fileCoverage.statements}% is below critical threshold ${this.CRITICAL_FILES_THRESHOLDS.statements}%`);
      }
      if (fileCoverage.branches < this.CRITICAL_FILES_THRESHOLDS.branches) {
        failures.push(`${criticalFile} branch coverage ${fileCoverage.branches}% is below critical threshold ${this.CRITICAL_FILES_THRESHOLDS.branches}%`);
      }
      if (fileCoverage.functions < this.CRITICAL_FILES_THRESHOLDS.functions) {
        failures.push(`${criticalFile} function coverage ${fileCoverage.functions}% is below critical threshold ${this.CRITICAL_FILES_THRESHOLDS.functions}%`);
      }
      if (fileCoverage.lines < this.CRITICAL_FILES_THRESHOLDS.lines) {
        failures.push(`${criticalFile} line coverage ${fileCoverage.lines}% is below critical threshold ${this.CRITICAL_FILES_THRESHOLDS.lines}%`);
      }
    }

    // Check for files with very low coverage
    for (const [file, coverage] of report.perFile) {
      if (coverage.statements < 50) {
        warnings.push(`${file} has very low statement coverage: ${coverage.statements}%`);
      }
    }

    return {
      passed: failures.length === 0,
      failures,
      warnings
    };
  }

  static generateReport(coverageData: any): CoverageReport {
    // This would typically parse coverage data from vitest/c8
    // For now, we'll create a mock structure
    const report: CoverageReport = {
      overall: {
        statements: 0,
        branches: 0,
        functions: 0,
        lines: 0
      },
      perFile: new Map(),
      timestamp: new Date().toISOString(),
      testCount: 0,
      passedTests: 0,
      failedTests: 0
    };

    // Parse coverage data (implementation would depend on actual coverage format)
    if (coverageData && coverageData.total) {
      const total = coverageData.total;
      report.overall = {
        statements: total.statements?.pct || 0,
        branches: total.branches?.pct || 0,
        functions: total.functions?.pct || 0,
        lines: total.lines?.pct || 0
      };
    }

    return report;
  }

  static printReport(report: CoverageReport): void {
    console.log('\n📊 Test Coverage Report');
    console.log('========================');
    console.log(`Timestamp: ${report.timestamp}`);
    console.log(`Tests: ${report.passedTests}/${report.testCount} passed`);
    console.log('');

    console.log('Overall Coverage:');
    console.log(`  Statements: ${report.overall.statements.toFixed(1)}%`);
    console.log(`  Branches:   ${report.overall.branches.toFixed(1)}%`);
    console.log(`  Functions:  ${report.overall.functions.toFixed(1)}%`);
    console.log(`  Lines:      ${report.overall.lines.toFixed(1)}%`);
    console.log('');

    const validation = this.validateCoverage(report);
    
    if (validation.warnings.length > 0) {
      console.log('⚠️  Warnings:');
      validation.warnings.forEach(warning => console.log(`  - ${warning}`));
      console.log('');
    }

    if (validation.failures.length > 0) {
      console.log('❌ Coverage Failures:');
      validation.failures.forEach(failure => console.log(`  - ${failure}`));
      console.log('');
    } else {
      console.log('✅ All coverage thresholds met!');
      console.log('');
    }

    // Print critical files coverage
    console.log('Critical Files Coverage:');
    for (const criticalFile of this.CRITICAL_FILES) {
      const fileCoverage = report.perFile.get(criticalFile);
      if (fileCoverage) {
        const status = this.isFileCoverageSufficient(fileCoverage, this.CRITICAL_FILES_THRESHOLDS) ? '✅' : '❌';
        console.log(`  ${status} ${criticalFile}: ${fileCoverage.statements.toFixed(1)}%`);
      } else {
        console.log(`  ⚠️  ${criticalFile}: No coverage data`);
      }
    }
  }

  private static isFileCoverageSufficient(coverage: CoverageThresholds, thresholds: CoverageThresholds): boolean {
    return coverage.statements >= thresholds.statements &&
           coverage.branches >= thresholds.branches &&
           coverage.functions >= thresholds.functions &&
           coverage.lines >= thresholds.lines;
  }

  static getRecommendations(report: CoverageReport): string[] {
    const recommendations: string[] = [];
    const validation = this.validateCoverage(report);

    if (validation.failures.length > 0) {
      recommendations.push('🎯 Priority Actions:');
      
      // Analyze which areas need most attention
      if (report.overall.statements < this.MINIMUM_THRESHOLDS.statements) {
        recommendations.push('  - Add more unit tests to increase statement coverage');
      }
      
      if (report.overall.branches < this.MINIMUM_THRESHOLDS.branches) {
        recommendations.push('  - Add tests for edge cases and error conditions to improve branch coverage');
      }
      
      if (report.overall.functions < this.MINIMUM_THRESHOLDS.functions) {
        recommendations.push('  - Ensure all public methods have corresponding tests');
      }

      // Check for untested critical files
      for (const criticalFile of this.CRITICAL_FILES) {
        const fileCoverage = report.perFile.get(criticalFile);
        if (!fileCoverage || fileCoverage.statements < this.CRITICAL_FILES_THRESHOLDS.statements) {
          recommendations.push(`  - Focus on testing ${criticalFile} (critical system)`);
        }
      }
    }

    if (validation.warnings.length > 0) {
      recommendations.push('💡 Suggestions:');
      recommendations.push('  - Review files with very low coverage for potential refactoring');
      recommendations.push('  - Consider adding integration tests for better overall coverage');
    }

    if (recommendations.length === 0) {
      recommendations.push('🎉 Excellent coverage! Consider:');
      recommendations.push('  - Adding more edge case tests');
      recommendations.push('  - Performance testing for critical paths');
      recommendations.push('  - Mutation testing to verify test quality');
    }

    return recommendations;
  }
}

// Export for use in test scripts
export default CoverageReporter;
