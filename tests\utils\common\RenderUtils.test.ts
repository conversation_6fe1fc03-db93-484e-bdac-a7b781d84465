import { describe, it, expect, vi, beforeEach } from 'vitest';
import { RenderUtils } from '@/utils/common/RenderUtils';

// Mock <PERSON>er
const mockRenderer = {
  setFillStyle: vi.fn(),
  setStrokeStyle: vi.fn(),
  setLineWidth: vi.fn(),
  setGlobalAlpha: vi.fn(),
  setShadow: vi.fn(),
  setFont: vi.fn(),
  setTextAlign: vi.fn(),
  setTextBaseline: vi.fn(),
  drawRect: vi.fn(),
  strokeRect: vi.fn(),
  drawText: vi.fn(),
  fillText: vi.fn(),
  strokeText: vi.fn(),
  drawCircle: vi.fn(),
  drawLine: vi.fn(),
  createRadialGradient: vi.fn(() => ({
    addColorStop: vi.fn()
  })),
  createLinearGradient: vi.fn(() => ({
    addColorStop: vi.fn()
  })),
  beginPath: vi.fn(),
  moveTo: vi.fn(),
  lineTo: vi.fn(),
  closePath: vi.fn(),
  fill: vi.fn(),
  stroke: vi.fn()
};

describe('RenderUtils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('applyStyle', () => {
    it('should apply render style to renderer', () => {
      const style = {
        fillColor: '#ff0000',
        strokeColor: '#00ff00',
        lineWidth: 2,
        alpha: 0.5
      };

      RenderUtils.applyStyle(mockRenderer as any, style);

      expect(mockRenderer.setFillStyle).toHaveBeenCalledWith('#ff0000');
      expect(mockRenderer.setStrokeStyle).toHaveBeenCalledWith('#00ff00');
      expect(mockRenderer.setLineWidth).toHaveBeenCalledWith(2);
      expect(mockRenderer.setGlobalAlpha).toHaveBeenCalledWith(0.5);
    });

    it('should apply shadow properties', () => {
      const style = {
        shadowColor: '#000000',
        shadowBlur: 5,
        shadowOffsetX: 2,
        shadowOffsetY: 3
      };

      RenderUtils.applyStyle(mockRenderer as any, style);

      expect(mockRenderer.setShadow).toHaveBeenCalledWith('#000000', 5, 2, 3);
    });

    it('should handle partial shadow properties', () => {
      const style = {
        shadowColor: '#000000'
      };

      RenderUtils.applyStyle(mockRenderer as any, style);

      expect(mockRenderer.setShadow).toHaveBeenCalledWith('#000000', 0, 0, 0);
    });
  });

  describe('applyTextStyle', () => {
    it('should apply text style to renderer', () => {
      const style = {
        font: '16px Arial',
        color: '#ffffff',
        align: 'center' as CanvasTextAlign,
        baseline: 'middle' as CanvasTextBaseline,
        alpha: 0.8
      };

      RenderUtils.applyTextStyle(mockRenderer as any, style);

      expect(mockRenderer.setFont).toHaveBeenCalledWith('16px Arial');
      expect(mockRenderer.setFillStyle).toHaveBeenCalledWith('#ffffff');
      expect(mockRenderer.setTextAlign).toHaveBeenCalledWith('center');
      expect(mockRenderer.setTextBaseline).toHaveBeenCalledWith('middle');
      expect(mockRenderer.setGlobalAlpha).toHaveBeenCalledWith(0.8);
    });
  });

  describe('drawHealthBar', () => {
    it('should draw health bar with correct colors', () => {
      RenderUtils.drawHealthBar(mockRenderer as any, 10, 20, 100, 20, 75, 100);

      // Should draw background
      expect(mockRenderer.drawRect).toHaveBeenCalledWith(10, 20, 100, 20, expect.any(String));
      
      // Should draw health fill (75% of width minus border)
      expect(mockRenderer.drawRect).toHaveBeenCalledWith(11, 21, 73.5, 18, expect.any(String));
    });

    it('should use critical health color for low health', () => {
      RenderUtils.drawHealthBar(mockRenderer as any, 10, 20, 100, 20, 20, 100, {
        criticalHealthColor: '#ff0000'
      });

      expect(mockRenderer.drawRect).toHaveBeenCalledWith(11, 21, 19.6, 18, '#ff0000');
    });

    it('should use low health color for medium health', () => {
      RenderUtils.drawHealthBar(mockRenderer as any, 10, 20, 100, 20, 50, 100, {
        lowHealthColor: '#ff9800'
      });

      expect(mockRenderer.drawRect).toHaveBeenCalledWith(11, 21, 49, 18, '#ff9800');
    });
  });

  describe('drawProgressBar', () => {
    it('should draw progress bar', () => {
      RenderUtils.drawProgressBar(mockRenderer as any, 10, 20, 100, 10, 0.6);

      // Should draw background
      expect(mockRenderer.drawRect).toHaveBeenCalledWith(10, 20, 100, 10, expect.any(String));
      
      // Should draw progress fill (60% of width minus border)
      expect(mockRenderer.drawRect).toHaveBeenCalledWith(11, 21, 58.8, 8, expect.any(String));
    });

    it('should clamp progress to 0-1 range', () => {
      RenderUtils.drawProgressBar(mockRenderer as any, 10, 20, 100, 10, 1.5);

      // Should draw full progress (100% of width minus border)
      expect(mockRenderer.drawRect).toHaveBeenCalledWith(11, 21, 98, 8, expect.any(String));
    });
  });

  describe('drawTextWithOutline', () => {
    it('should draw text with outline', () => {
      RenderUtils.drawTextWithOutline(mockRenderer as any, 'Hello', 50, 50);

      expect(mockRenderer.setStrokeStyle).toHaveBeenCalledWith('#000000');
      expect(mockRenderer.setLineWidth).toHaveBeenCalledWith(2);
      expect(mockRenderer.strokeText).toHaveBeenCalledWith('Hello', 50, 50);
      expect(mockRenderer.setFillStyle).toHaveBeenCalledWith('#ffffff');
      expect(mockRenderer.fillText).toHaveBeenCalledWith('Hello', 50, 50);
    });

    it('should use custom colors and outline width', () => {
      RenderUtils.drawTextWithOutline(mockRenderer as any, 'Hello', 50, 50, '#ff0000', '#0000ff', 3);

      expect(mockRenderer.setStrokeStyle).toHaveBeenCalledWith('#0000ff');
      expect(mockRenderer.setLineWidth).toHaveBeenCalledWith(3);
      expect(mockRenderer.setFillStyle).toHaveBeenCalledWith('#ff0000');
    });
  });

  describe('drawButton', () => {
    it('should draw normal button', () => {
      RenderUtils.drawButton(mockRenderer as any, 10, 20, 100, 30, 'Click Me');

      expect(mockRenderer.drawRect).toHaveBeenCalledWith(10, 20, 100, 30, expect.any(String));
      expect(mockRenderer.fillText).toHaveBeenCalledWith('Click Me', 60, 35);
    });

    it('should draw hovered button', () => {
      RenderUtils.drawButton(mockRenderer as any, 10, 20, 100, 30, 'Click Me', true, false);

      expect(mockRenderer.drawRect).toHaveBeenCalledWith(10, 20, 100, 30, expect.any(String));
    });

    it('should draw pressed button', () => {
      RenderUtils.drawButton(mockRenderer as any, 10, 20, 100, 30, 'Click Me', false, true);

      expect(mockRenderer.drawRect).toHaveBeenCalledWith(10, 20, 100, 30, expect.any(String));
    });
  });

  describe('drawGrid', () => {
    it('should draw grid lines', () => {
      RenderUtils.drawGrid(mockRenderer as any, 0, 0, 100, 100, 10);

      expect(mockRenderer.setStrokeStyle).toHaveBeenCalled();
      expect(mockRenderer.setLineWidth).toHaveBeenCalled();
      expect(mockRenderer.setGlobalAlpha).toHaveBeenCalledWith(0.3);
      expect(mockRenderer.drawLine).toHaveBeenCalled();
    });

    it('should use custom style', () => {
      const style = {
        color: '#ff0000',
        lineWidth: 2,
        alpha: 0.5
      };

      RenderUtils.drawGrid(mockRenderer as any, 0, 0, 100, 100, 10, style);

      expect(mockRenderer.setStrokeStyle).toHaveBeenCalledWith('#ff0000');
      expect(mockRenderer.setLineWidth).toHaveBeenCalledWith(2);
      expect(mockRenderer.setGlobalAlpha).toHaveBeenCalledWith(0.5);
    });
  });

  describe('drawGlow', () => {
    it('should draw glow effect', () => {
      const mockGradient = {
        addColorStop: vi.fn()
      };
      mockRenderer.createRadialGradient.mockReturnValue(mockGradient);

      RenderUtils.drawGlow(mockRenderer as any, 50, 50, 20, '#ff0000', 0.8);

      expect(mockRenderer.createRadialGradient).toHaveBeenCalledWith(50, 50, 0, 50, 50, 20);
      expect(mockGradient.addColorStop).toHaveBeenCalledWith(0, '#ff0000');
      expect(mockGradient.addColorStop).toHaveBeenCalledWith(1, 'transparent');
      expect(mockRenderer.setGlobalAlpha).toHaveBeenCalledWith(0.8);
      expect(mockRenderer.drawCircle).toHaveBeenCalledWith(50, 50, 20, mockGradient);
    });
  });

  describe('drawStar', () => {
    it('should draw star shape', () => {
      RenderUtils.drawStar(mockRenderer as any, 50, 50, 20, 10, 5);

      expect(mockRenderer.beginPath).toHaveBeenCalled();
      expect(mockRenderer.moveTo).toHaveBeenCalled();
      expect(mockRenderer.lineTo).toHaveBeenCalled();
      expect(mockRenderer.closePath).toHaveBeenCalled();
    });

    it('should apply style and fill/stroke', () => {
      const style = {
        fillColor: '#ff0000',
        strokeColor: '#000000'
      };

      RenderUtils.drawStar(mockRenderer as any, 50, 50, 20, 10, 5, style);

      expect(mockRenderer.fill).toHaveBeenCalled();
      expect(mockRenderer.stroke).toHaveBeenCalled();
    });
  });

  describe('drawArrow', () => {
    it('should draw arrow', () => {
      RenderUtils.drawArrow(mockRenderer as any, 0, 0, 50, 50);

      expect(mockRenderer.drawLine).toHaveBeenCalledWith(0, 0, 50, 50);
      expect(mockRenderer.beginPath).toHaveBeenCalled();
      expect(mockRenderer.moveTo).toHaveBeenCalled();
      expect(mockRenderer.lineTo).toHaveBeenCalled();
      expect(mockRenderer.closePath).toHaveBeenCalled();
    });

    it('should use custom head size', () => {
      RenderUtils.drawArrow(mockRenderer as any, 0, 0, 50, 50, 15);

      expect(mockRenderer.drawLine).toHaveBeenCalledWith(0, 0, 50, 50);
    });
  });

  describe('drawDashedLine', () => {
    it('should draw dashed line', () => {
      RenderUtils.drawDashedLine(mockRenderer as any, 0, 0, 100, 0, 5, 5);

      expect(mockRenderer.drawLine).toHaveBeenCalled();
    });

    it('should handle zero distance', () => {
      RenderUtils.drawDashedLine(mockRenderer as any, 0, 0, 0, 0, 5, 5);

      // Should not crash and should not draw any lines
      expect(mockRenderer.drawLine).not.toHaveBeenCalled();
    });
  });

  describe('colorWithAlpha', () => {
    it('should convert hex color to rgba', () => {
      const result = RenderUtils.colorWithAlpha('#ff0000', 0.5);
      expect(result).toBe('rgba(255, 0, 0, 0.5)');
    });

    it('should return original color if not hex', () => {
      const result = RenderUtils.colorWithAlpha('red', 0.5);
      expect(result).toBe('red');
    });
  });

  describe('lerpColor', () => {
    it('should interpolate between hex colors', () => {
      const result = RenderUtils.lerpColor('#000000', '#ffffff', 0.5);
      expect(result).toBe('#808080');
    });

    it('should return first color if not hex', () => {
      const result = RenderUtils.lerpColor('red', 'blue', 0.5);
      expect(result).toBe('red');
    });

    it('should handle edge cases', () => {
      expect(RenderUtils.lerpColor('#000000', '#ffffff', 0)).toBe('#000000');
      expect(RenderUtils.lerpColor('#000000', '#ffffff', 1)).toBe('#ffffff');
    });
  });

  describe('constants', () => {
    it('should have color constants', () => {
      expect(RenderUtils.COLORS.WHITE).toBe('#ffffff');
      expect(RenderUtils.COLORS.BLACK).toBe('#000000');
      expect(RenderUtils.COLORS.RED).toBe('#ff0000');
    });

    it('should have font constants', () => {
      expect(RenderUtils.FONTS.DEFAULT).toBe('16px Arial');
      expect(RenderUtils.FONTS.MONOSPACE).toBe('16px monospace');
    });
  });
});
