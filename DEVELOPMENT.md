# 🛠️ Development Guide - Ultimate Bullet Hell RPG

This comprehensive guide covers everything you need to know to develop, test, and deploy the Ultimate Bullet Hell RPG.

## 📋 Table of Contents

1. [Quick Start](#-quick-start)
2. [Project Structure](#-project-structure)
3. [Development Workflow](#-development-workflow)
4. [Testing Strategy](#-testing-strategy)
5. [Performance Optimization](#-performance-optimization)
6. [Code Quality](#-code-quality)
7. [Deployment](#-deployment)
8. [Troubleshooting](#-troubleshooting)

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm 8+
- Modern browser with ES2020 support

### Setup
```bash
# Clone the repository
git clone <repository-url>
cd BulletHell_RougeLike_RPG

# Install dependencies
npm install

# Start development server
npm run dev

# Open browser to http://localhost:3000
```

### Available Scripts
```bash
npm run dev          # Start development server with hot reload
npm run build        # Build for production
npm run preview      # Preview production build
npm run test         # Run all tests
npm run test:watch   # Run tests in watch mode
npm run lint         # Check code quality
npm run format       # Format code with Prettier
```

## 📁 Project Structure

```
src/
├── config/           # Game constants and configuration
│   └── GameConstants.ts
├── engine/           # Core game engine
│   ├── core/         # Core systems (Game, SceneManager, etc.)
│   ├── physics/      # Physics and collision detection
│   ├── rendering/    # Canvas rendering system
│   ├── input/        # Input handling
│   └── audio/        # Audio management
├── game/             # Game-specific code
│   ├── entities/     # Game objects (Player, Enemy, etc.)
│   ├── scenes/       # Game scenes (Menu, Game, etc.)
│   └── systems/      # Game systems (Progression, Inventory, etc.)
├── utils/            # Utility functions and helpers
│   ├── math/         # Mathematical utilities (Vector2, etc.)
│   └── error/        # Error handling
└── types/            # TypeScript type definitions

tests/
├── unit/             # Unit tests
├── integration/      # Integration tests
├── performance/      # Performance tests
└── setup.ts          # Test configuration

assets/               # Game assets (images, audio, etc.)
dist/                 # Built files (generated)
docs/                 # Documentation
```

## 🔄 Development Workflow

### 1. Feature Development
```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes
# Write tests
# Update documentation

# Run quality checks
npm run lint
npm run test
npm run type-check

# Commit changes
git commit -m "feat: add new feature"

# Push and create PR
git push origin feature/new-feature
```

### 2. Code Quality Checks
The project enforces code quality through:
- **ESLint**: Code linting and style enforcement
- **Prettier**: Code formatting
- **TypeScript**: Type checking
- **Husky**: Pre-commit hooks
- **Lint-staged**: Staged file linting

### 3. Automated Testing
```bash
# Run all tests
npm run test

# Run specific test suites
npm run test tests/unit/
npm run test tests/integration/
npm run test tests/performance/

# Generate coverage report
npm run test:coverage
```

## 🧪 Testing Strategy

### Unit Tests
- Test individual functions and classes
- Located in `tests/unit/`
- Use Vitest framework
- Aim for 70%+ coverage

### Integration Tests
- Test system interactions
- Located in `tests/integration/`
- Test game scenes and workflows
- Verify system integration

### Performance Tests
- Ensure 60 FPS target
- Test collision detection efficiency
- Monitor memory usage
- Located in `tests/performance/`

### Test Writing Guidelines
```typescript
describe('ComponentName', () => {
  describe('methodName', () => {
    it('should do something specific', () => {
      // Arrange
      const input = createTestInput();
      
      // Act
      const result = methodUnderTest(input);
      
      // Assert
      expect(result).toBe(expectedValue);
    });
  });
});
```

## ⚡ Performance Optimization

### Key Performance Areas

1. **Object Pooling**
   - Reuse objects instead of creating new ones
   - Implemented for bullets, particles, enemies
   - Prevents garbage collection lag

2. **Spatial Partitioning**
   - Optimized collision detection
   - O(n) instead of O(n²) complexity
   - Configurable grid cell size

3. **Memory Management**
   - Minimize allocations in hot paths
   - Reuse Vector2 objects
   - Monitor memory usage

4. **Rendering Optimization**
   - Batch draw calls
   - Cull off-screen objects
   - Optimize canvas operations

### Performance Monitoring
```typescript
import { timeOperation } from '@/utils/error/SimpleErrorHandler';

const result = timeOperation('expensive-operation', () => {
  // Your expensive operation here
  return processData();
});
```

## 🎯 Code Quality

### Style Guide
Follow the project's [STYLE_GUIDE.md](./STYLE_GUIDE.md) for:
- Naming conventions
- Code organization
- Error handling patterns
- Performance best practices

### Type Safety
- Use strict TypeScript configuration
- Avoid `any` types
- Provide explicit return types
- Use proper interfaces and types

### Error Handling
```typescript
import { error, warn, assertNotNull } from '@/utils/error/SimpleErrorHandler';

function safeOperation(data: unknown): ProcessedData {
  const validData = assertNotNull(data, 'Data cannot be null');
  
  try {
    return processData(validData);
  } catch (err) {
    error('Operation failed', {
      component: 'processor',
      action: 'process',
      data: { error: err instanceof Error ? err.message : err }
    });
    throw err;
  }
}
```

## 🚀 Deployment

### Build Process
```bash
# Development build
npm run build:dev

# Production build
npm run build:prod

# Preview build
npm run preview
```

### Build Optimization
- Code splitting by feature
- Tree shaking for unused code
- Asset optimization
- Source map generation (dev only)

### Deployment Targets
- **Development**: Local development server
- **Staging**: Preview environment for testing
- **Production**: Live game deployment

### CI/CD Pipeline
The project uses GitHub Actions for:
- Automated testing
- Code quality checks
- Security audits
- Performance monitoring
- Automated deployment

## 🔧 Troubleshooting

### Common Issues

#### Build Errors
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Clear build cache
rm -rf dist
npm run build
```

#### Test Failures
```bash
# Run tests in verbose mode
npm run test -- --reporter=verbose

# Run specific test file
npm run test -- path/to/test.test.ts

# Update snapshots
npm run test -- --update-snapshots
```

#### Performance Issues
```bash
# Run performance tests
npm run test tests/performance/

# Profile build bundle
npm run build:prod
npx vite-bundle-analyzer dist/

# Monitor runtime performance
# Use browser dev tools Performance tab
```

#### Type Errors
```bash
# Run type checking
npm run type-check

# Check specific file
npx tsc --noEmit src/path/to/file.ts
```

### Debug Configuration
Use VS Code debug configuration in `.vscode/launch.json`:
- **Launch Chrome**: Debug in browser
- **Debug Tests**: Debug test files
- **Attach to Chrome**: Attach to running instance

### Logging and Monitoring
```typescript
import { debug, info, warn, error } from '@/utils/error/SimpleErrorHandler';

// Development logging
debug('Debug information', { component: 'system' });

// Production logging
info('System started', { version: '1.0.0' });
warn('Performance warning', { frameTime: 20 });
error('Critical error', { component: 'renderer' });
```

## 📚 Additional Resources

- [STYLE_GUIDE.md](./STYLE_GUIDE.md) - Code style guidelines
- [README.md](./README.md) - Project overview
- [API Documentation](./docs/api/) - Generated API docs
- [Game Design Document](./docs/design/) - Game design specs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Follow the style guide
4. Write tests for new features
5. Ensure all checks pass
6. Submit a pull request

For detailed contribution guidelines, see [CONTRIBUTING.md](./CONTRIBUTING.md).

---

**Happy coding! 🎮**
