/**
 * Common rendering utility functions to reduce code duplication
 */

import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';
import { Vector2 } from '@/utils/math/Vector2';
import { MathUtils } from './MathUtils';

export interface RenderStyle {
  fillColor?: string;
  strokeColor?: string;
  lineWidth?: number;
  alpha?: number;
  shadowColor?: string;
  shadowBlur?: number;
  shadowOffsetX?: number;
  shadowOffsetY?: number;
}

export interface TextStyle {
  font?: string;
  color?: string;
  align?: CanvasTextAlign;
  baseline?: CanvasTextBaseline;
  maxWidth?: number;
  alpha?: number;
}

export class RenderUtils {
  // Common colors
  static readonly COLORS = {
    WHITE: '#ffffff',
    BLACK: '#000000',
    RED: '#ff0000',
    GREEN: '#00ff00',
    BLUE: '#0000ff',
    YELLOW: '#ffff00',
    CYAN: '#00ffff',
    MAGENTA: '#ff00ff',
    GRAY: '#808080',
    DARK_GRAY: '#404040',
    LIGHT_GRAY: '#c0c0c0',
    TRANSPARENT: 'transparent'
  };

  // Common fonts
  static readonly FONTS = {
    DEFAULT: '16px Arial',
    SMALL: '12px Arial',
    LARGE: '24px Arial',
    MONOSPACE: '16px monospace',
    BOLD: 'bold 16px Arial'
  };

  /**
   * Apply render style to renderer
   */
  static applyStyle(renderer: CanvasRenderer, style: RenderStyle): void {
    if (style.fillColor) renderer.setFillStyle(style.fillColor);
    if (style.strokeColor) renderer.setStrokeStyle(style.strokeColor);
    if (style.lineWidth !== undefined) renderer.setLineWidth(style.lineWidth);
    if (style.alpha !== undefined) renderer.setGlobalAlpha(style.alpha);
    
    if (style.shadowColor || style.shadowBlur || style.shadowOffsetX || style.shadowOffsetY) {
      renderer.setShadow(
        style.shadowColor || 'transparent',
        style.shadowBlur || 0,
        style.shadowOffsetX || 0,
        style.shadowOffsetY || 0
      );
    }
  }

  /**
   * Apply text style to renderer
   */
  static applyTextStyle(renderer: CanvasRenderer, style: TextStyle): void {
    if (style.font) renderer.setFont(style.font);
    if (style.color) renderer.setFillStyle(style.color);
    if (style.align) renderer.setTextAlign(style.align);
    if (style.baseline) renderer.setTextBaseline(style.baseline);
    if (style.alpha !== undefined) renderer.setGlobalAlpha(style.alpha);
  }

  /**
   * Draw a health bar
   */
  static drawHealthBar(
    renderer: CanvasRenderer,
    x: number,
    y: number,
    width: number,
    height: number,
    currentHealth: number,
    maxHealth: number,
    style?: {
      backgroundColor?: string;
      healthColor?: string;
      lowHealthColor?: string;
      criticalHealthColor?: string;
      borderColor?: string;
      borderWidth?: number;
    }
  ): void {
    const healthPercent = MathUtils.clamp(currentHealth / maxHealth, 0, 1);
    
    // Default colors
    const bgColor = style?.backgroundColor || this.COLORS.DARK_GRAY;
    const borderColor = style?.borderColor || this.COLORS.BLACK;
    const borderWidth = style?.borderWidth || 1;
    
    // Health color based on percentage
    let healthColor = style?.healthColor || this.COLORS.GREEN;
    if (healthPercent < 0.3) {
      healthColor = style?.criticalHealthColor || this.COLORS.RED;
    } else if (healthPercent < 0.6) {
      healthColor = style?.lowHealthColor || '#ff9800';
    }
    
    // Draw background
    renderer.drawRect(x, y, width, height, bgColor);
    
    // Draw health fill
    const fillWidth = (width - borderWidth * 2) * healthPercent;
    renderer.drawRect(x + borderWidth, y + borderWidth, fillWidth, height - borderWidth * 2, healthColor);
    
    // Draw border
    if (borderWidth > 0) {
      renderer.setStrokeStyle(borderColor);
      renderer.setLineWidth(borderWidth);
      renderer.strokeRect(x, y, width, height);
    }
  }

  /**
   * Draw a progress bar
   */
  static drawProgressBar(
    renderer: CanvasRenderer,
    x: number,
    y: number,
    width: number,
    height: number,
    progress: number,
    style?: {
      backgroundColor?: string;
      progressColor?: string;
      borderColor?: string;
      borderWidth?: number;
    }
  ): void {
    const progressPercent = MathUtils.clamp(progress, 0, 1);
    
    const bgColor = style?.backgroundColor || this.COLORS.DARK_GRAY;
    const progressColor = style?.progressColor || this.COLORS.BLUE;
    const borderColor = style?.borderColor || this.COLORS.BLACK;
    const borderWidth = style?.borderWidth || 1;
    
    // Draw background
    renderer.drawRect(x, y, width, height, bgColor);
    
    // Draw progress fill
    const fillWidth = (width - borderWidth * 2) * progressPercent;
    renderer.drawRect(x + borderWidth, y + borderWidth, fillWidth, height - borderWidth * 2, progressColor);
    
    // Draw border
    if (borderWidth > 0) {
      renderer.setStrokeStyle(borderColor);
      renderer.setLineWidth(borderWidth);
      renderer.strokeRect(x, y, width, height);
    }
  }

  /**
   * Draw text with outline
   */
  static drawTextWithOutline(
    renderer: CanvasRenderer,
    text: string,
    x: number,
    y: number,
    textColor: string = this.COLORS.WHITE,
    outlineColor: string = this.COLORS.BLACK,
    outlineWidth: number = 2
  ): void {
    // Draw outline
    renderer.setStrokeStyle(outlineColor);
    renderer.setLineWidth(outlineWidth);
    renderer.strokeText(text, x, y);
    
    // Draw text
    renderer.setFillStyle(textColor);
    renderer.fillText(text, x, y);
  }

  /**
   * Draw a button
   */
  static drawButton(
    renderer: CanvasRenderer,
    x: number,
    y: number,
    width: number,
    height: number,
    text: string,
    isHovered: boolean = false,
    isPressed: boolean = false,
    style?: {
      normalColor?: string;
      hoverColor?: string;
      pressedColor?: string;
      textColor?: string;
      borderColor?: string;
      borderWidth?: number;
      font?: string;
    }
  ): void {
    // Determine button color
    let buttonColor = style?.normalColor || this.COLORS.GRAY;
    if (isPressed) {
      buttonColor = style?.pressedColor || this.COLORS.DARK_GRAY;
    } else if (isHovered) {
      buttonColor = style?.hoverColor || this.COLORS.LIGHT_GRAY;
    }
    
    // Draw button background
    renderer.drawRect(x, y, width, height, buttonColor);
    
    // Draw border
    const borderWidth = style?.borderWidth || 2;
    if (borderWidth > 0) {
      const borderColor = style?.borderColor || this.COLORS.BLACK;
      renderer.setStrokeStyle(borderColor);
      renderer.setLineWidth(borderWidth);
      renderer.strokeRect(x, y, width, height);
    }
    
    // Draw text
    const textColor = style?.textColor || this.COLORS.BLACK;
    const font = style?.font || this.FONTS.DEFAULT;
    
    renderer.setFont(font);
    renderer.setFillStyle(textColor);
    renderer.setTextAlign('center');
    renderer.setTextBaseline('middle');
    renderer.fillText(text, x + width / 2, y + height / 2);
  }

  /**
   * Draw a grid
   */
  static drawGrid(
    renderer: CanvasRenderer,
    startX: number,
    startY: number,
    endX: number,
    endY: number,
    cellSize: number,
    style?: {
      color?: string;
      lineWidth?: number;
      alpha?: number;
    }
  ): void {
    const color = style?.color || this.COLORS.GRAY;
    const lineWidth = style?.lineWidth || 1;
    const alpha = style?.alpha || 0.3;
    
    renderer.setStrokeStyle(color);
    renderer.setLineWidth(lineWidth);
    renderer.setGlobalAlpha(alpha);
    
    // Vertical lines
    for (let x = startX; x <= endX; x += cellSize) {
      renderer.drawLine(x, startY, x, endY);
    }
    
    // Horizontal lines
    for (let y = startY; y <= endY; y += cellSize) {
      renderer.drawLine(startX, y, endX, y);
    }
    
    renderer.setGlobalAlpha(1);
  }

  /**
   * Draw a glow effect around a shape
   */
  static drawGlow(
    renderer: CanvasRenderer,
    x: number,
    y: number,
    radius: number,
    color: string,
    intensity: number = 1
  ): void {
    const gradient = renderer.createRadialGradient(x, y, 0, x, y, radius);
    gradient.addColorStop(0, color);
    gradient.addColorStop(1, 'transparent');
    
    renderer.setGlobalAlpha(intensity);
    renderer.drawCircle(x, y, radius, gradient);
    renderer.setGlobalAlpha(1);
  }

  /**
   * Draw a star shape
   */
  static drawStar(
    renderer: CanvasRenderer,
    x: number,
    y: number,
    outerRadius: number,
    innerRadius: number,
    points: number = 5,
    style?: RenderStyle
  ): void {
    if (style) this.applyStyle(renderer, style);
    
    const angleStep = MathUtils.TWO_PI / (points * 2);
    let angle = -MathUtils.HALF_PI;
    
    renderer.beginPath();
    
    for (let i = 0; i < points * 2; i++) {
      const radius = i % 2 === 0 ? outerRadius : innerRadius;
      const px = x + Math.cos(angle) * radius;
      const py = y + Math.sin(angle) * radius;
      
      if (i === 0) {
        renderer.moveTo(px, py);
      } else {
        renderer.lineTo(px, py);
      }
      
      angle += angleStep;
    }
    
    renderer.closePath();
    
    if (style?.fillColor) renderer.fill();
    if (style?.strokeColor) renderer.stroke();
  }

  /**
   * Draw an arrow
   */
  static drawArrow(
    renderer: CanvasRenderer,
    fromX: number,
    fromY: number,
    toX: number,
    toY: number,
    headSize: number = 10,
    style?: RenderStyle
  ): void {
    if (style) this.applyStyle(renderer, style);
    
    const angle = Math.atan2(toY - fromY, toX - fromX);
    
    // Draw line
    renderer.drawLine(fromX, fromY, toX, toY);
    
    // Draw arrowhead
    const headAngle1 = angle - Math.PI / 6;
    const headAngle2 = angle + Math.PI / 6;
    
    const head1X = toX - Math.cos(headAngle1) * headSize;
    const head1Y = toY - Math.sin(headAngle1) * headSize;
    const head2X = toX - Math.cos(headAngle2) * headSize;
    const head2Y = toY - Math.sin(headAngle2) * headSize;
    
    renderer.beginPath();
    renderer.moveTo(toX, toY);
    renderer.lineTo(head1X, head1Y);
    renderer.lineTo(head2X, head2Y);
    renderer.closePath();
    
    if (style?.fillColor) renderer.fill();
    if (style?.strokeColor) renderer.stroke();
  }

  /**
   * Draw a dashed line
   */
  static drawDashedLine(
    renderer: CanvasRenderer,
    fromX: number,
    fromY: number,
    toX: number,
    toY: number,
    dashLength: number = 5,
    gapLength: number = 5
  ): void {
    const distance = MathUtils.distance(fromX, fromY, toX, toY);
    const angle = Math.atan2(toY - fromY, toX - fromX);
    
    let currentDistance = 0;
    let isDash = true;
    
    while (currentDistance < distance) {
      const segmentLength = isDash ? dashLength : gapLength;
      const endDistance = Math.min(currentDistance + segmentLength, distance);
      
      const startX = fromX + Math.cos(angle) * currentDistance;
      const startY = fromY + Math.sin(angle) * currentDistance;
      const endX = fromX + Math.cos(angle) * endDistance;
      const endY = fromY + Math.sin(angle) * endDistance;
      
      if (isDash) {
        renderer.drawLine(startX, startY, endX, endY);
      }
      
      currentDistance = endDistance;
      isDash = !isDash;
    }
  }

  /**
   * Create a color with alpha
   */
  static colorWithAlpha(color: string, alpha: number): string {
    // Simple implementation for hex colors
    if (color.startsWith('#')) {
      const r = parseInt(color.slice(1, 3), 16);
      const g = parseInt(color.slice(3, 5), 16);
      const b = parseInt(color.slice(5, 7), 16);
      return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }
    return color;
  }

  /**
   * Interpolate between two colors
   */
  static lerpColor(color1: string, color2: string, t: number): string {
    // Simple implementation for hex colors
    if (color1.startsWith('#') && color2.startsWith('#')) {
      const r1 = parseInt(color1.slice(1, 3), 16);
      const g1 = parseInt(color1.slice(3, 5), 16);
      const b1 = parseInt(color1.slice(5, 7), 16);
      
      const r2 = parseInt(color2.slice(1, 3), 16);
      const g2 = parseInt(color2.slice(3, 5), 16);
      const b2 = parseInt(color2.slice(5, 7), 16);
      
      const r = Math.round(MathUtils.lerp(r1, r2, t));
      const g = Math.round(MathUtils.lerp(g1, g2, t));
      const b = Math.round(MathUtils.lerp(b1, b2, t));
      
      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    }
    return color1;
  }
}
