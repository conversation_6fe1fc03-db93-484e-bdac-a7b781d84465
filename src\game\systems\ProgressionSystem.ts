/**
 * Character progression system with skill trees and stat management
 */

export interface Skill {
  id: string;
  name: string;
  description: string;
  icon: string;
  maxLevel: number;
  currentLevel: number;
  prerequisites: string[];
  tree: string;
  position: { x: number; y: number };
  cost: (level: number) => number;
  effect: (level: number) => SkillEffect;
}

export interface SkillEffect {
  statModifiers: { [stat: string]: number };
  abilities: string[];
  passives: string[];
}

export interface SkillTree {
  id: string;
  name: string;
  description: string;
  color: string;
  skills: Skill[];
}

export interface CharacterStats {
  // Core stats
  health: number;
  maxHealth: number;
  mana: number;
  maxMana: number;
  
  // Combat stats
  damage: number;
  attackSpeed: number;
  criticalChance: number;
  criticalMultiplier: number;
  defense: number;
  
  // Movement stats
  speed: number;
  
  // Magic stats
  spellPower: number;
  manaRegeneration: number;
  
  // Utility stats
  luck: number;
  experienceMultiplier: number;
  goldMultiplier: number;
}

export interface ProgressionData {
  level: number;
  experience: number;
  skillPoints: number;
  totalSkillPoints: number;
  stats: CharacterStats;
  skills: Map<string, number>; // skill id -> level
  unlockedAbilities: Set<string>;
  unlockedPassives: Set<string>;
}

export class ProgressionSystem {
  private skillTrees: Map<string, SkillTree> = new Map();
  private baseStats: CharacterStats;
  private experienceTable: number[] = [];

  constructor() {
    this.initializeBaseStats();
    this.generateExperienceTable();
    this.initializeSkillTrees();
  }

  private initializeBaseStats(): void {
    this.baseStats = {
      health: 100,
      maxHealth: 100,
      mana: 50,
      maxMana: 50,
      damage: 25,
      attackSpeed: 1.0,
      criticalChance: 0.05,
      criticalMultiplier: 2.0,
      defense: 5,
      speed: 300,
      spellPower: 20,
      manaRegeneration: 5,
      luck: 1,
      experienceMultiplier: 1.0,
      goldMultiplier: 1.0
    };
  }

  private generateExperienceTable(): void {
    // Generate experience requirements for each level
    for (let level = 1; level <= 100; level++) {
      // Exponential growth: base * level^1.5
      const baseXP = 100;
      const xpRequired = Math.floor(baseXP * Math.pow(level, 1.5));
      this.experienceTable.push(xpRequired);
    }
  }

  private initializeSkillTrees(): void {
    this.createCombatTree();
    this.createMagicTree();
    this.createUtilityTree();
  }

  private createCombatTree(): void {
    const combatSkills: Skill[] = [
      {
        id: 'combat_damage',
        name: 'Weapon Mastery',
        description: 'Increases weapon damage by 10% per level',
        icon: '⚔️',
        maxLevel: 5,
        currentLevel: 0,
        prerequisites: [],
        tree: 'combat',
        position: { x: 1, y: 1 },
        cost: (level) => level * 2,
        effect: (level) => ({
          statModifiers: { damage: level * 0.1 },
          abilities: [],
          passives: []
        })
      },
      {
        id: 'combat_crit',
        name: 'Critical Strike',
        description: 'Increases critical chance by 2% per level',
        icon: '💥',
        maxLevel: 5,
        currentLevel: 0,
        prerequisites: ['combat_damage'],
        tree: 'combat',
        position: { x: 2, y: 1 },
        cost: (level) => level * 2,
        effect: (level) => ({
          statModifiers: { criticalChance: level * 0.02 },
          abilities: [],
          passives: []
        })
      },
      {
        id: 'combat_speed',
        name: 'Attack Speed',
        description: 'Increases attack speed by 15% per level',
        icon: '⚡',
        maxLevel: 3,
        currentLevel: 0,
        prerequisites: ['combat_damage'],
        tree: 'combat',
        position: { x: 1, y: 2 },
        cost: (level) => level * 3,
        effect: (level) => ({
          statModifiers: { attackSpeed: level * 0.15 },
          abilities: [],
          passives: []
        })
      },
      {
        id: 'combat_berserker',
        name: 'Berserker Rage',
        description: 'Unlocks berserker mode: +50% damage, -25% defense for 10 seconds',
        icon: '😡',
        maxLevel: 1,
        currentLevel: 0,
        prerequisites: ['combat_crit', 'combat_speed'],
        tree: 'combat',
        position: { x: 2, y: 2 },
        cost: (level) => 10,
        effect: (level) => ({
          statModifiers: {},
          abilities: ['berserker_rage'],
          passives: []
        })
      },
      {
        id: 'combat_defense',
        name: 'Armor Training',
        description: 'Increases defense by 3 points per level',
        icon: '🛡️',
        maxLevel: 5,
        currentLevel: 0,
        prerequisites: [],
        tree: 'combat',
        position: { x: 0, y: 1 },
        cost: (level) => level * 2,
        effect: (level) => ({
          statModifiers: { defense: level * 3 },
          abilities: [],
          passives: []
        })
      }
    ];

    this.skillTrees.set('combat', {
      id: 'combat',
      name: 'Combat',
      description: 'Physical combat and weapon skills',
      color: '#ff6b6b',
      skills: combatSkills
    });
  }

  private createMagicTree(): void {
    const magicSkills: Skill[] = [
      {
        id: 'magic_power',
        name: 'Spell Power',
        description: 'Increases spell damage by 15% per level',
        icon: '🔮',
        maxLevel: 5,
        currentLevel: 0,
        prerequisites: [],
        tree: 'magic',
        position: { x: 1, y: 1 },
        cost: (level) => level * 2,
        effect: (level) => ({
          statModifiers: { spellPower: level * 0.15 },
          abilities: [],
          passives: []
        })
      },
      {
        id: 'magic_mana',
        name: 'Mana Pool',
        description: 'Increases maximum mana by 20 per level',
        icon: '💙',
        maxLevel: 5,
        currentLevel: 0,
        prerequisites: [],
        tree: 'magic',
        position: { x: 0, y: 1 },
        cost: (level) => level * 2,
        effect: (level) => ({
          statModifiers: { maxMana: level * 20 },
          abilities: [],
          passives: []
        })
      },
      {
        id: 'magic_regen',
        name: 'Mana Regeneration',
        description: 'Increases mana regeneration by 3 per second per level',
        icon: '♻️',
        maxLevel: 3,
        currentLevel: 0,
        prerequisites: ['magic_mana'],
        tree: 'magic',
        position: { x: 0, y: 2 },
        cost: (level) => level * 3,
        effect: (level) => ({
          statModifiers: { manaRegeneration: level * 3 },
          abilities: [],
          passives: []
        })
      },
      {
        id: 'magic_fireball',
        name: 'Fireball',
        description: 'Unlocks fireball spell',
        icon: '🔥',
        maxLevel: 1,
        currentLevel: 0,
        prerequisites: ['magic_power'],
        tree: 'magic',
        position: { x: 1, y: 2 },
        cost: (level) => 5,
        effect: (level) => ({
          statModifiers: {},
          abilities: ['fireball'],
          passives: []
        })
      },
      {
        id: 'magic_lightning',
        name: 'Lightning Bolt',
        description: 'Unlocks lightning bolt spell',
        icon: '⚡',
        maxLevel: 1,
        currentLevel: 0,
        prerequisites: ['magic_power'],
        tree: 'magic',
        position: { x: 2, y: 2 },
        cost: (level) => 5,
        effect: (level) => ({
          statModifiers: {},
          abilities: ['lightning_bolt'],
          passives: []
        })
      },
      {
        id: 'magic_meteor',
        name: 'Meteor',
        description: 'Unlocks devastating meteor spell',
        icon: '☄️',
        maxLevel: 1,
        currentLevel: 0,
        prerequisites: ['magic_fireball', 'magic_lightning'],
        tree: 'magic',
        position: { x: 1, y: 3 },
        cost: (level) => 15,
        effect: (level) => ({
          statModifiers: {},
          abilities: ['meteor'],
          passives: []
        })
      }
    ];

    this.skillTrees.set('magic', {
      id: 'magic',
      name: 'Magic',
      description: 'Arcane spells and magical abilities',
      color: '#4ecdc4',
      skills: magicSkills
    });
  }

  private createUtilityTree(): void {
    const utilitySkills: Skill[] = [
      {
        id: 'utility_speed',
        name: 'Fleet Footed',
        description: 'Increases movement speed by 10% per level',
        icon: '👟',
        maxLevel: 3,
        currentLevel: 0,
        prerequisites: [],
        tree: 'utility',
        position: { x: 0, y: 1 },
        cost: (level) => level * 2,
        effect: (level) => ({
          statModifiers: { speed: level * 0.1 },
          abilities: [],
          passives: []
        })
      },
      {
        id: 'utility_health',
        name: 'Vitality',
        description: 'Increases maximum health by 25 per level',
        icon: '❤️',
        maxLevel: 5,
        currentLevel: 0,
        prerequisites: [],
        tree: 'utility',
        position: { x: 1, y: 1 },
        cost: (level) => level * 2,
        effect: (level) => ({
          statModifiers: { maxHealth: level * 25 },
          abilities: [],
          passives: []
        })
      },
      {
        id: 'utility_luck',
        name: 'Lucky',
        description: 'Increases luck by 1 per level (affects loot and critical hits)',
        icon: '🍀',
        maxLevel: 5,
        currentLevel: 0,
        prerequisites: [],
        tree: 'utility',
        position: { x: 2, y: 1 },
        cost: (level) => level * 3,
        effect: (level) => ({
          statModifiers: { luck: level },
          abilities: [],
          passives: []
        })
      },
      {
        id: 'utility_exp',
        name: 'Scholar',
        description: 'Increases experience gain by 20% per level',
        icon: '📚',
        maxLevel: 3,
        currentLevel: 0,
        prerequisites: ['utility_luck'],
        tree: 'utility',
        position: { x: 2, y: 2 },
        cost: (level) => level * 4,
        effect: (level) => ({
          statModifiers: { experienceMultiplier: level * 0.2 },
          abilities: [],
          passives: []
        })
      },
      {
        id: 'utility_dash',
        name: 'Dash',
        description: 'Unlocks dash ability for quick movement',
        icon: '💨',
        maxLevel: 1,
        currentLevel: 0,
        prerequisites: ['utility_speed'],
        tree: 'utility',
        position: { x: 0, y: 2 },
        cost: (level) => 8,
        effect: (level) => ({
          statModifiers: {},
          abilities: ['dash'],
          passives: []
        })
      },
      {
        id: 'utility_heal',
        name: 'Healing',
        description: 'Unlocks healing spell',
        icon: '💚',
        maxLevel: 1,
        currentLevel: 0,
        prerequisites: ['utility_health'],
        tree: 'utility',
        position: { x: 1, y: 2 },
        cost: (level) => 6,
        effect: (level) => ({
          statModifiers: {},
          abilities: ['heal'],
          passives: []
        })
      }
    ];

    this.skillTrees.set('utility', {
      id: 'utility',
      name: 'Utility',
      description: 'Support skills and quality of life improvements',
      color: '#95e1d3',
      skills: utilitySkills
    });
  }

  // Public API methods
  createNewCharacter(): ProgressionData {
    return {
      level: 1,
      experience: 0,
      skillPoints: 0,
      totalSkillPoints: 0,
      stats: { ...this.baseStats },
      skills: new Map(),
      unlockedAbilities: new Set(),
      unlockedPassives: new Set()
    };
  }

  gainExperience(character: ProgressionData, amount: number): boolean {
    const adjustedAmount = Math.floor(amount * character.stats.experienceMultiplier);
    character.experience += adjustedAmount;
    
    return this.checkLevelUp(character);
  }

  private checkLevelUp(character: ProgressionData): boolean {
    const requiredXP = this.getExperienceForLevel(character.level + 1);
    
    if (character.experience >= requiredXP && character.level < 100) {
      character.level++;
      character.experience -= requiredXP;
      character.skillPoints += this.getSkillPointsPerLevel(character.level);
      character.totalSkillPoints += this.getSkillPointsPerLevel(character.level);
      
      // Recalculate stats
      this.recalculateStats(character);
      
      return true;
    }
    
    return false;
  }

  private getSkillPointsPerLevel(level: number): number {
    // Give more skill points at certain milestone levels
    if (level % 10 === 0) return 3; // Every 10 levels
    if (level % 5 === 0) return 2;  // Every 5 levels
    return 1; // Normal levels
  }

  getExperienceForLevel(level: number): number {
    if (level <= 1) return 0;
    if (level > this.experienceTable.length) return this.experienceTable[this.experienceTable.length - 1];
    return this.experienceTable[level - 2];
  }

  canLearnSkill(character: ProgressionData, skillId: string): boolean {
    const skill = this.findSkill(skillId);
    if (!skill) return false;
    
    // Check if already at max level
    const currentLevel = character.skills.get(skillId) || 0;
    if (currentLevel >= skill.maxLevel) return false;
    
    // Check skill points
    const cost = skill.cost(currentLevel + 1);
    if (character.skillPoints < cost) return false;
    
    // Check prerequisites
    for (const prereqId of skill.prerequisites) {
      const prereqLevel = character.skills.get(prereqId) || 0;
      if (prereqLevel === 0) return false;
    }
    
    return true;
  }

  learnSkill(character: ProgressionData, skillId: string): boolean {
    if (!this.canLearnSkill(character, skillId)) return false;
    
    const skill = this.findSkill(skillId);
    if (!skill) return false;
    
    const currentLevel = character.skills.get(skillId) || 0;
    const newLevel = currentLevel + 1;
    const cost = skill.cost(newLevel);
    
    // Spend skill points
    character.skillPoints -= cost;
    
    // Update skill level
    character.skills.set(skillId, newLevel);
    
    // Apply skill effects
    const effect = skill.effect(newLevel);
    
    // Add abilities and passives
    for (const ability of effect.abilities) {
      character.unlockedAbilities.add(ability);
    }
    for (const passive of effect.passives) {
      character.unlockedPassives.add(passive);
    }
    
    // Recalculate stats
    this.recalculateStats(character);
    
    return true;
  }

  private recalculateStats(character: ProgressionData): void {
    // Start with base stats
    character.stats = { ...this.baseStats };
    
    // Apply skill modifiers
    for (const [skillId, level] of character.skills) {
      const skill = this.findSkill(skillId);
      if (skill && level > 0) {
        const effect = skill.effect(level);
        
        for (const [stat, modifier] of Object.entries(effect.statModifiers)) {
          if (stat in character.stats) {
            const currentValue = (character.stats as any)[stat];
            
            // Handle percentage vs flat modifiers
            if (modifier < 1 && modifier > -1) {
              // Percentage modifier
              (character.stats as any)[stat] = currentValue * (1 + modifier);
            } else {
              // Flat modifier
              (character.stats as any)[stat] = currentValue + modifier;
            }
          }
        }
      }
    }
    
    // Ensure health doesn't exceed max health
    character.stats.health = Math.min(character.stats.health, character.stats.maxHealth);
    character.stats.mana = Math.min(character.stats.mana, character.stats.maxMana);
  }

  private findSkill(skillId: string): Skill | null {
    for (const tree of this.skillTrees.values()) {
      const skill = tree.skills.find(s => s.id === skillId);
      if (skill) return skill;
    }
    return null;
  }

  // Getters
  getSkillTrees(): SkillTree[] {
    return Array.from(this.skillTrees.values());
  }

  getSkillTree(treeId: string): SkillTree | null {
    return this.skillTrees.get(treeId) || null;
  }

  getSkill(skillId: string): Skill | null {
    return this.findSkill(skillId);
  }

  getSkillLevel(character: ProgressionData, skillId: string): number {
    return character.skills.get(skillId) || 0;
  }

  hasAbility(character: ProgressionData, abilityId: string): boolean {
    return character.unlockedAbilities.has(abilityId);
  }

  hasPassive(character: ProgressionData, passiveId: string): boolean {
    return character.unlockedPassives.has(passiveId);
  }

  getProgressToNextLevel(character: ProgressionData): number {
    const requiredXP = this.getExperienceForLevel(character.level + 1);
    return character.experience / requiredXP;
  }

  // Save/Load functionality
  saveCharacterData(character: ProgressionData): string {
    const saveData = {
      level: character.level,
      experience: character.experience,
      skillPoints: character.skillPoints,
      totalSkillPoints: character.totalSkillPoints,
      skills: Array.from(character.skills.entries()),
      unlockedAbilities: Array.from(character.unlockedAbilities),
      unlockedPassives: Array.from(character.unlockedPassives)
    };

    return JSON.stringify(saveData);
  }

  loadCharacterData(saveString: string): ProgressionData | null {
    try {
      const saveData = JSON.parse(saveString);

      const character: ProgressionData = {
        level: saveData.level,
        experience: saveData.experience,
        skillPoints: saveData.skillPoints,
        totalSkillPoints: saveData.totalSkillPoints,
        stats: { ...this.baseStats },
        skills: new Map(saveData.skills),
        unlockedAbilities: new Set(saveData.unlockedAbilities),
        unlockedPassives: new Set(saveData.unlockedPassives)
      };

      this.recalculateStats(character);
      return character;
    } catch (error) {
      console.error('Failed to load character data:', error);
      return null;
    }
  }
}
