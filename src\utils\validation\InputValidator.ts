/**
 * Input validation and sanitization utilities
 * Provides security-focused validation for user inputs and game data
 */

export interface ValidationResult<T = unknown> {
  isValid: boolean;
  errors: string[];
  sanitizedValue?: T;
}

export interface ValidationRule<T> {
  name: string;
  validate: (value: T) => boolean;
  errorMessage: string;
  sanitize?: (value: T) => T;
}

export class InputValidator {
  private static readonly MAX_STRING_LENGTH = 1000;
  private static readonly MAX_NUMBER_VALUE = 1e6;
  private static readonly MIN_NUMBER_VALUE = -1e6;

  /**
   * Sanitize string input to prevent XSS attacks
   */
  static sanitizeString(input: string): string {
    if (typeof input !== 'string') {
      return '';
    }

    return input
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .trim()
      .substring(0, this.MAX_STRING_LENGTH);
  }

  /**
   * Validate and sanitize numeric input
   */
  static validateNumber(input: unknown, min?: number, max?: number): ValidationResult<number> {
    const errors: string[] = [];
    let sanitizedValue: number;

    // Type check
    if (typeof input !== 'number' && typeof input !== 'string') {
      errors.push('Input must be a number or numeric string');
      return { isValid: false, errors };
    }

    // Convert to number
    sanitizedValue = typeof input === 'string' ? parseFloat(input) : input;

    // Check if valid number
    if (isNaN(sanitizedValue) || !isFinite(sanitizedValue)) {
      errors.push('Input must be a valid number');
      return { isValid: false, errors };
    }

    // Range validation
    const minValue = min ?? this.MIN_NUMBER_VALUE;
    const maxValue = max ?? this.MAX_NUMBER_VALUE;

    if (sanitizedValue < minValue) {
      errors.push(`Value must be at least ${minValue}`);
      sanitizedValue = minValue;
    }

    if (sanitizedValue > maxValue) {
      errors.push(`Value must be at most ${maxValue}`);
      sanitizedValue = maxValue;
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue
    };
  }

  /**
   * Validate game configuration objects
   */
  static validateGameConfig(config: unknown): ValidationResult<Record<string, unknown>> {
    const errors: string[] = [];
    const sanitizedConfig: Record<string, unknown> = {};

    if (!config || typeof config !== 'object') {
      errors.push('Configuration must be an object');
      return { isValid: false, errors };
    }

    // Validate canvas dimensions
    if (config.canvas) {
      const widthResult = this.validateNumber(config.canvas.width, 100, 4000);
      const heightResult = this.validateNumber(config.canvas.height, 100, 4000);

      sanitizedConfig.canvas = {
        width: widthResult.sanitizedValue || 1024,
        height: heightResult.sanitizedValue || 768,
        targetFPS: this.validateNumber(config.canvas.targetFPS, 30, 120).sanitizedValue || 60
      };

      errors.push(...widthResult.errors, ...heightResult.errors);
    }

    // Validate debug settings
    if (config.debug) {
      sanitizedConfig.debug = {
        showFPS: Boolean(config.debug.showFPS),
        showColliders: Boolean(config.debug.showColliders),
        logPerformance: Boolean(config.debug.logPerformance)
      };
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: sanitizedConfig
    };
  }

  /**
   * Validate player input for movement and actions
   */
  static validatePlayerInput(input: unknown): ValidationResult<Record<string, unknown>> {
    const errors: string[] = [];
    const sanitizedInput: Record<string, unknown> = {};

    if (!input || typeof input !== 'object') {
      errors.push('Player input must be an object');
      return { isValid: false, errors };
    }

    // Validate movement vector
    if (input.movement) {
      const x = this.validateNumber(input.movement.x, -1, 1);
      const y = this.validateNumber(input.movement.y, -1, 1);

      sanitizedInput.movement = {
        x: x.sanitizedValue || 0,
        y: y.sanitizedValue || 0
      };

      errors.push(...x.errors, ...y.errors);
    }

    // Validate action flags
    sanitizedInput.actions = {
      shoot: Boolean(input.actions?.shoot),
      special: Boolean(input.actions?.special),
      interact: Boolean(input.actions?.interact)
    };

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: sanitizedInput
    };
  }

  /**
   * Validate file upload data
   */
  static validateFileData(data: unknown, allowedTypes: string[] = []): ValidationResult<File> {
    const errors: string[] = [];

    if (!data) {
      errors.push('File data is required');
      return { isValid: false, errors };
    }

    // Check file size (max 10MB for game assets)
    if (data.size && data.size > 10 * 1024 * 1024) {
      errors.push('File size must be less than 10MB');
    }

    // Check file type
    if (allowedTypes.length > 0 && data.type && !allowedTypes.includes(data.type)) {
      errors.push(`File type must be one of: ${allowedTypes.join(', ')}`);
    }

    // Sanitize filename
    let sanitizedName = '';
    if (data.name) {
      sanitizedName = this.sanitizeString(data.name)
        .replace(/[^a-zA-Z0-9._-]/g, '_') // Replace special chars with underscore
        .substring(0, 255); // Limit filename length
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: { ...data, name: sanitizedName }
    };
  }

  /**
   * Create a validation chain for complex validation scenarios
   */
  static createValidator<T>(...rules: ValidationRule<T>[]) {
    return (value: T): ValidationResult => {
      const errors: string[] = [];
      let sanitizedValue = value;

      for (const rule of rules) {
        if (!rule.validate(sanitizedValue)) {
          errors.push(rule.errorMessage);
        }

        if (rule.sanitize) {
          sanitizedValue = rule.sanitize(sanitizedValue);
        }
      }

      return {
        isValid: errors.length === 0,
        errors,
        sanitizedValue
      };
    };
  }
}

/**
 * Common validation rules
 */
export const ValidationRules = {
  required: <T>(errorMessage = 'Field is required'): ValidationRule<T> => ({
    name: 'required',
    validate: (value) => value != null && value !== '',
    errorMessage
  }),

  minLength: (min: number, errorMessage?: string): ValidationRule<string> => ({
    name: 'minLength',
    validate: (value) => typeof value === 'string' && value.length >= min,
    errorMessage: errorMessage || `Minimum length is ${min} characters`
  }),

  maxLength: (max: number, errorMessage?: string): ValidationRule<string> => ({
    name: 'maxLength',
    validate: (value) => typeof value === 'string' && value.length <= max,
    errorMessage: errorMessage || `Maximum length is ${max} characters`,
    sanitize: (value) => typeof value === 'string' ? value.substring(0, max) : value
  }),

  range: (min: number, max: number, errorMessage?: string): ValidationRule<number> => ({
    name: 'range',
    validate: (value) => typeof value === 'number' && value >= min && value <= max,
    errorMessage: errorMessage || `Value must be between ${min} and ${max}`,
    sanitize: (value) => typeof value === 'number' ? Math.max(min, Math.min(max, value)) : value
  }),

  pattern: (regex: RegExp, errorMessage?: string): ValidationRule<string> => ({
    name: 'pattern',
    validate: (value) => typeof value === 'string' && regex.test(value),
    errorMessage: errorMessage || 'Value does not match required pattern'
  })
};
