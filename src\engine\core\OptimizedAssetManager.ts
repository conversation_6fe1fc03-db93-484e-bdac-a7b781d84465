/**
 * Optimized asset management system with progressive loading and compression
 */

import { SafeOperations } from '@/utils/safety/SafeOperations';

export type AssetType = 'image' | 'audio' | 'json' | 'text' | 'binary';
export type LoadingPriority = 'critical' | 'high' | 'medium' | 'low';
export type CompressionType = 'none' | 'gzip' | 'webp' | 'avif';

export interface AssetDescriptor {
  id: string;
  url: string;
  type: AssetType;
  priority: LoadingPriority;
  size?: number;
  compression?: CompressionType;
  fallbacks?: string[]; // Fallback URLs for different formats
  preload?: boolean;
  cache?: boolean;
  dependencies?: string[]; // Asset IDs this asset depends on
}

export interface LoadedAsset<T = any> {
  id: string;
  data: T;
  type: AssetType;
  size: number;
  loadTime: number;
  fromCache: boolean;
  compressed: boolean;
}

export interface LoadingProgress {
  loaded: number;
  total: number;
  percentage: number;
  currentAsset?: string;
  bytesLoaded: number;
  bytesTotal: number;
}

export interface AssetLoadOptions {
  priority?: LoadingPriority;
  timeout?: number;
  retries?: number;
  cache?: boolean;
  onProgress?: (progress: number) => void;
}

/**
 * Advanced asset manager with progressive loading and optimization
 */
export class OptimizedAssetManager {
  private assets: Map<string, LoadedAsset> = new Map();
  private loadingQueue: Map<LoadingPriority, AssetDescriptor[]> = new Map([
    ['critical', []],
    ['high', []],
    ['medium', []],
    ['low', []]
  ]);
  
  private loadingPromises: Map<string, Promise<LoadedAsset>> = new Map();
  private loadingProgress: LoadingProgress = {
    loaded: 0,
    total: 0,
    percentage: 0,
    bytesLoaded: 0,
    bytesTotal: 0
  };

  private maxConcurrentLoads: number = 6;
  private currentLoads: number = 0;
  private cache: Map<string, ArrayBuffer> = new Map();
  private compressionSupport: Map<CompressionType, boolean> = new Map();

  private progressCallbacks: Set<(progress: LoadingProgress) => void> = new Set();
  private errorCallbacks: Set<(error: Error, assetId: string) => void> = new Set();

  constructor() {
    this.detectCompressionSupport();
    this.setupCacheCleanup();
  }

  /**
   * Detect browser compression support
   */
  private detectCompressionSupport(): void {
    // Check WebP support
    const webpCanvas = document.createElement('canvas');
    webpCanvas.width = 1;
    webpCanvas.height = 1;
    this.compressionSupport.set('webp', webpCanvas.toDataURL('image/webp').indexOf('data:image/webp') === 0);

    // Check AVIF support (basic detection)
    this.compressionSupport.set('avif', 'createImageBitmap' in window);
    
    // GZIP is always supported for text assets
    this.compressionSupport.set('gzip', true);
    this.compressionSupport.set('none', true);
  }

  /**
   * Setup automatic cache cleanup
   */
  private setupCacheCleanup(): void {
    // Clean up cache every 5 minutes
    setInterval(() => {
      this.cleanupCache();
    }, 5 * 60 * 1000);

    // Clean up on page unload
    window.addEventListener('beforeunload', () => {
      this.cleanupCache();
    });
  }

  /**
   * Register an asset for loading
   */
  registerAsset(descriptor: AssetDescriptor): void {
    const queue = this.loadingQueue.get(descriptor.priority);
    if (queue) {
      queue.push(descriptor);
      this.updateProgress();
    }
  }

  /**
   * Register multiple assets
   */
  registerAssets(descriptors: AssetDescriptor[]): void {
    for (const descriptor of descriptors) {
      this.registerAsset(descriptor);
    }
  }

  /**
   * Load a single asset
   */
  async loadAsset(id: string, options: AssetLoadOptions = {}): Promise<LoadedAsset> {
    // Check if already loaded
    const existing = this.assets.get(id);
    if (existing) {
      return existing;
    }

    // Check if already loading
    const existingPromise = this.loadingPromises.get(id);
    if (existingPromise) {
      return existingPromise;
    }

    // Find asset descriptor
    const descriptor = this.findAssetDescriptor(id);
    if (!descriptor) {
      throw new Error(`Asset not found: ${id}`);
    }

    // Create loading promise
    const loadingPromise = this.performAssetLoad(descriptor, options);
    this.loadingPromises.set(id, loadingPromise);

    try {
      const asset = await loadingPromise;
      this.assets.set(id, asset);
      this.loadingPromises.delete(id);
      return asset;
    } catch (error) {
      this.loadingPromises.delete(id);
      this.notifyError(error as Error, id);
      throw error;
    }
  }

  /**
   * Load assets by priority
   */
  async loadAssetsByPriority(priority: LoadingPriority): Promise<LoadedAsset[]> {
    const queue = this.loadingQueue.get(priority);
    if (!queue || queue.length === 0) {
      return [];
    }

    const promises = queue.map(descriptor => this.loadAsset(descriptor.id));
    const results = await Promise.allSettled(promises);

    // Clear the queue
    queue.length = 0;

    // Return successful loads
    return results
      .filter((result): result is PromiseFulfilledResult<LoadedAsset> => result.status === 'fulfilled')
      .map(result => result.value);
  }

  /**
   * Load all registered assets
   */
  async loadAllAssets(): Promise<void> {
    const priorities: LoadingPriority[] = ['critical', 'high', 'medium', 'low'];
    
    for (const priority of priorities) {
      await this.loadAssetsByPriority(priority);
    }
  }

  /**
   * Perform the actual asset loading
   */
  private async performAssetLoad(descriptor: AssetDescriptor, options: AssetLoadOptions): Promise<LoadedAsset> {
    const startTime = performance.now();
    this.currentLoads++;

    try {
      // Wait for available slot if at max concurrent loads
      while (this.currentLoads > this.maxConcurrentLoads) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }

      // Load dependencies first
      if (descriptor.dependencies) {
        await Promise.all(descriptor.dependencies.map(depId => this.loadAsset(depId)));
      }

      // Try to load from cache first
      const cacheKey = this.getCacheKey(descriptor);
      let data: any;
      let fromCache = false;

      if (descriptor.cache !== false && this.cache.has(cacheKey)) {
        data = await this.loadFromCache(cacheKey, descriptor.type);
        fromCache = true;
      } else {
        data = await this.loadFromNetwork(descriptor, options);
        
        // Cache the result if caching is enabled
        if (descriptor.cache !== false && data instanceof ArrayBuffer) {
          this.cache.set(cacheKey, data);
        }
      }

      const loadTime = performance.now() - startTime;
      const size = this.calculateAssetSize(data);

      const asset: LoadedAsset = {
        id: descriptor.id,
        data,
        type: descriptor.type,
        size,
        loadTime,
        fromCache,
        compressed: descriptor.compression !== 'none'
      };

      this.updateLoadingProgress(size);
      return asset;

    } finally {
      this.currentLoads--;
    }
  }

  /**
   * Load asset from network
   */
  private async loadFromNetwork(descriptor: AssetDescriptor, options: AssetLoadOptions): Promise<any> {
    const urls = this.getOptimalUrls(descriptor);
    let lastError: Error | null = null;

    for (const url of urls) {
      try {
        const response = await this.fetchWithTimeout(url, options.timeout || 30000);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await this.processResponse(response, descriptor.type);
      } catch (error) {
        lastError = error as Error;
        console.warn(`Failed to load ${url}:`, error);
      }
    }

    throw lastError || new Error(`Failed to load asset: ${descriptor.id}`);
  }

  /**
   * Get optimal URLs based on browser support
   */
  private getOptimalUrls(descriptor: AssetDescriptor): string[] {
    const urls = [descriptor.url];
    
    if (descriptor.fallbacks) {
      // Filter fallbacks based on compression support
      const supportedFallbacks = descriptor.fallbacks.filter(url => {
        const extension = url.split('.').pop()?.toLowerCase();
        switch (extension) {
          case 'webp':
            return this.compressionSupport.get('webp');
          case 'avif':
            return this.compressionSupport.get('avif');
          default:
            return true;
        }
      });
      
      urls.push(...supportedFallbacks);
    }

    return urls;
  }

  /**
   * Fetch with timeout
   */
  private async fetchWithTimeout(url: string, timeout: number): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        signal: controller.signal,
        cache: 'default'
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * Process response based on asset type
   */
  private async processResponse(response: Response, type: AssetType): Promise<any> {
    switch (type) {
      case 'image':
        const blob = await response.blob();
        return await this.createImageFromBlob(blob);
      
      case 'audio':
        const audioBuffer = await response.arrayBuffer();
        return audioBuffer; // Will be processed by AudioManager
      
      case 'json':
        return await response.json();
      
      case 'text':
        return await response.text();
      
      case 'binary':
        return await response.arrayBuffer();
      
      default:
        throw new Error(`Unsupported asset type: ${type}`);
    }
  }

  /**
   * Create image from blob
   */
  private async createImageFromBlob(blob: Blob): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = URL.createObjectURL(blob);
    });
  }

  /**
   * Load from cache
   */
  private async loadFromCache(cacheKey: string, type: AssetType): Promise<any> {
    const cached = this.cache.get(cacheKey);
    if (!cached) {
      throw new Error('Cache miss');
    }

    // Convert cached data back to appropriate format
    switch (type) {
      case 'image':
        const blob = new Blob([cached]);
        return await this.createImageFromBlob(blob);
      
      case 'json':
        const text = new TextDecoder().decode(cached);
        return JSON.parse(text);
      
      case 'text':
        return new TextDecoder().decode(cached);
      
      default:
        return cached;
    }
  }

  /**
   * Get cache key for asset
   */
  private getCacheKey(descriptor: AssetDescriptor): string {
    return `${descriptor.id}_${descriptor.url}_${descriptor.compression || 'none'}`;
  }

  /**
   * Calculate asset size
   */
  private calculateAssetSize(data: any): number {
    if (data instanceof ArrayBuffer) {
      return data.byteLength;
    } else if (data instanceof HTMLImageElement) {
      return data.width * data.height * 4; // Estimate RGBA bytes
    } else if (typeof data === 'string') {
      return new Blob([data]).size;
    } else {
      return JSON.stringify(data).length;
    }
  }

  /**
   * Find asset descriptor by ID
   */
  private findAssetDescriptor(id: string): AssetDescriptor | null {
    for (const queue of this.loadingQueue.values()) {
      const descriptor = queue.find(desc => desc.id === id);
      if (descriptor) {
        return descriptor;
      }
    }
    return null;
  }

  /**
   * Update loading progress
   */
  private updateProgress(): void {
    let total = 0;
    for (const queue of this.loadingQueue.values()) {
      total += queue.length;
    }
    
    this.loadingProgress.total = total + this.assets.size;
    this.loadingProgress.loaded = this.assets.size;
    this.loadingProgress.percentage = total > 0 ? (this.loadingProgress.loaded / this.loadingProgress.total) * 100 : 100;
    
    this.notifyProgress();
  }

  /**
   * Update loading progress with bytes
   */
  private updateLoadingProgress(bytesLoaded: number): void {
    this.loadingProgress.bytesLoaded += bytesLoaded;
    this.loadingProgress.loaded++;
    this.loadingProgress.percentage = this.loadingProgress.total > 0 
      ? (this.loadingProgress.loaded / this.loadingProgress.total) * 100 
      : 100;
    
    this.notifyProgress();
  }

  /**
   * Notify progress callbacks
   */
  private notifyProgress(): void {
    for (const callback of this.progressCallbacks) {
      try {
        callback(this.loadingProgress);
      } catch (error) {
        console.error('Progress callback error:', error);
      }
    }
  }

  /**
   * Notify error callbacks
   */
  private notifyError(error: Error, assetId: string): void {
    for (const callback of this.errorCallbacks) {
      try {
        callback(error, assetId);
      } catch (callbackError) {
        console.error('Error callback error:', callbackError);
      }
    }
  }

  /**
   * Clean up cache
   */
  private cleanupCache(): void {
    // Remove old cache entries (simple LRU-like cleanup)
    if (this.cache.size > 100) {
      const entries = Array.from(this.cache.entries());
      const toRemove = entries.slice(0, entries.length - 50);
      
      for (const [key] of toRemove) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Get asset by ID
   */
  getAsset<T = any>(id: string): LoadedAsset<T> | null {
    return this.assets.get(id) as LoadedAsset<T> || null;
  }

  /**
   * Check if asset is loaded
   */
  isLoaded(id: string): boolean {
    return this.assets.has(id);
  }

  /**
   * Get loading progress
   */
  getProgress(): LoadingProgress {
    return { ...this.loadingProgress };
  }

  /**
   * Add progress callback
   */
  onProgress(callback: (progress: LoadingProgress) => void): void {
    this.progressCallbacks.add(callback);
  }

  /**
   * Add error callback
   */
  onError(callback: (error: Error, assetId: string) => void): void {
    this.errorCallbacks.add(callback);
  }

  /**
   * Remove progress callback
   */
  offProgress(callback: (progress: LoadingProgress) => void): void {
    this.progressCallbacks.delete(callback);
  }

  /**
   * Remove error callback
   */
  offError(callback: (error: Error, assetId: string) => void): void {
    this.errorCallbacks.delete(callback);
  }

  /**
   * Clear all assets and cache
   */
  clear(): void {
    this.assets.clear();
    this.cache.clear();
    this.loadingPromises.clear();
    
    for (const queue of this.loadingQueue.values()) {
      queue.length = 0;
    }
    
    this.loadingProgress = {
      loaded: 0,
      total: 0,
      percentage: 0,
      bytesLoaded: 0,
      bytesTotal: 0
    };
  }

  /**
   * Get memory usage statistics
   */
  getMemoryUsage(): { assets: number; cache: number; total: number } {
    let assetsSize = 0;
    for (const asset of this.assets.values()) {
      assetsSize += asset.size;
    }

    let cacheSize = 0;
    for (const buffer of this.cache.values()) {
      cacheSize += buffer.byteLength;
    }

    return {
      assets: assetsSize,
      cache: cacheSize,
      total: assetsSize + cacheSize
    };
  }
}
