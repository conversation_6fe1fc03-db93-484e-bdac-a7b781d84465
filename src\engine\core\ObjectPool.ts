/**
 * Generic object pooling system for efficient memory management
 * Prevents garbage collection by reusing objects instead of creating new ones
 */

export interface Poolable {
  reset(): void;
  isActive(): boolean;
  setActive(active: boolean): void;
  destroy?(): void; // Optional cleanup method for memory management
}

export class ObjectPool<T extends Poolable> {
  private pool: T[] = [];
  private activeObjects: T[] = [];
  private createFunction: () => T;
  private resetFunction?: (obj: T) => void;
  private maxSize: number;
  private initialSize: number;

  constructor(
    createFunction: () => T,
    initialSize: number = 10,
    maxSize: number = 100,
    resetFunction?: (obj: T) => void
  ) {
    this.createFunction = createFunction;
    this.resetFunction = resetFunction;
    this.maxSize = maxSize;
    this.initialSize = initialSize;

    this.preallocate();
  }

  private preallocate(): void {
    for (let i = 0; i < this.initialSize; i++) {
      const obj = this.createFunction();
      obj.setActive(false);
      this.pool.push(obj);
    }
  }

  acquire(): T {
    let obj: T;

    if (this.pool.length > 0) {
      // Reuse object from pool
      obj = this.pool.pop()!;
    } else if (this.getTotalObjects() < this.maxSize) {
      // Create new object if under max limit
      obj = this.createFunction();
    } else {
      // Pool is full - implement graceful degradation
      console.warn('Object pool is full, reusing oldest active object');

      // Find the oldest active object and reuse it
      if (this.activeObjects.length > 0) {
        obj = this.activeObjects.shift()!; // Remove oldest from active list
        console.warn(`Forcibly recycling object of type ${obj.constructor.name}`);
      } else {
        // Fallback: create a new object anyway (emergency allocation)
        console.error('Emergency allocation: creating object beyond pool limit');
        obj = this.createFunction();
      }
    }

    // Reset object state
    obj.reset();
    if (this.resetFunction) {
      this.resetFunction(obj);
    }

    obj.setActive(true);
    this.activeObjects.push(obj);

    return obj;
  }

  release(obj: T): void {
    if (!obj) return;

    const index = this.activeObjects.indexOf(obj);
    if (index === -1) {
      console.warn('Trying to release object that is not in active list');
      return;
    }

    // Remove from active list
    this.activeObjects.splice(index, 1);

    // Deactivate and reset object
    obj.setActive(false);
    obj.reset();

    // Return to pool if there's space
    if (this.pool.length < this.maxSize) {
      this.pool.push(obj);
    } else {
      // Pool is full, properly destroy the object if possible
      if (obj.destroy) {
        obj.destroy();
      }
      // Object will be garbage collected
    }
  }

  releaseAll(): void {
    // Move all active objects back to pool
    for (const obj of this.activeObjects) {
      obj.setActive(false);
      obj.reset();
      this.pool.push(obj);
    }
    this.activeObjects = [];
  }

  update(): void {
    // Check for objects that should be automatically released
    for (let i = this.activeObjects.length - 1; i >= 0; i--) {
      const obj = this.activeObjects[i];
      if (!obj.isActive()) {
        this.release(obj);
      }
    }
  }

  getActiveObjects(): T[] {
    return [...this.activeObjects];
  }

  getActiveCount(): number {
    return this.activeObjects.length;
  }

  getPoolSize(): number {
    return this.pool.length;
  }

  getTotalObjects(): number {
    return this.pool.length + this.activeObjects.length;
  }

  getStats(): {
    active: number;
    pooled: number;
    total: number;
    maxSize: number;
    utilization: number;
  } {
    const active = this.activeObjects.length;
    const pooled = this.pool.length;
    const total = active + pooled;

    return {
      active,
      pooled,
      total,
      maxSize: this.maxSize,
      utilization: total / this.maxSize
    };
  }

  clear(): void {
    // Destroy all objects if they have a destroy method
    for (const obj of this.activeObjects) {
      if (obj.destroy) {
        obj.destroy();
      }
    }

    for (const obj of this.pool) {
      if (obj.destroy) {
        obj.destroy();
      }
    }

    this.activeObjects = [];
    this.pool = [];
  }

  /**
   * Force cleanup of inactive objects to free memory
   */
  cleanup(): void {
    // Remove inactive objects from active list
    for (let i = this.activeObjects.length - 1; i >= 0; i--) {
      const obj = this.activeObjects[i];
      if (!obj.isActive()) {
        this.activeObjects.splice(i, 1);

        // Return to pool or destroy if pool is full
        if (this.pool.length < this.maxSize) {
          obj.reset();
          this.pool.push(obj);
        } else if (obj.destroy) {
          obj.destroy();
        }
      }
    }
  }

  /**
   * Trim pool size to reduce memory usage
   */
  trim(targetSize?: number): void {
    const target = targetSize ?? Math.floor(this.maxSize * 0.5);

    while (this.pool.length > target) {
      const obj = this.pool.pop();
      if (obj && obj.destroy) {
        obj.destroy();
      }
    }
  }

  resize(newMaxSize: number): void {
    this.maxSize = newMaxSize;

    // If new size is smaller, remove excess objects from pool
    while (this.getTotalObjects() > newMaxSize && this.pool.length > 0) {
      this.pool.pop();
    }
  }
}

/**
 * Pool manager for handling multiple object pools
 */
export class PoolManager {
  private pools: Map<string, ObjectPool<any>> = new Map();

  registerPool<T extends Poolable>(
    name: string,
    createFunction: () => T,
    initialSize: number = 10,
    maxSize: number = 100,
    resetFunction?: (obj: T) => void
  ): ObjectPool<T> {
    const pool = new ObjectPool(createFunction, initialSize, maxSize, resetFunction);
    this.pools.set(name, pool);
    return pool;
  }

  getPool<T extends Poolable>(name: string): ObjectPool<T> | null {
    return this.pools.get(name) || null;
  }

  acquire<T extends Poolable>(poolName: string): T | null {
    const pool = this.getPool<T>(poolName);
    if (!pool) {
      console.error(`Pool '${poolName}' not found`);
      return null;
    }
    return pool.acquire();
  }

  release<T extends Poolable>(poolName: string, obj: T): void {
    const pool = this.getPool<T>(poolName);
    if (pool) {
      pool.release(obj);
    }
  }

  updateAll(): void {
    for (const pool of this.pools.values()) {
      pool.update();
    }
  }

  getStats(): Map<string, any> {
    const stats = new Map();
    for (const [name, pool] of this.pools) {
      stats.set(name, pool.getStats());
    }
    return stats;
  }

  clearAll(): void {
    for (const pool of this.pools.values()) {
      pool.clear();
    }
  }

  /**
   * Cleanup all pools to free memory
   */
  cleanupAll(): void {
    for (const pool of this.pools.values()) {
      pool.cleanup();
    }
  }

  /**
   * Trim all pools to reduce memory usage
   */
  trimAll(targetSizeRatio: number = 0.5): void {
    for (const pool of this.pools.values()) {
      pool.trim(Math.floor(pool.getStats().maxSize * targetSizeRatio));
    }
  }

  /**
   * Get memory usage statistics for all pools
   */
  getMemoryStats(): {
    totalPools: number;
    totalObjects: number;
    totalActive: number;
    totalPooled: number;
    averageUtilization: number;
  } {
    let totalObjects = 0;
    let totalActive = 0;
    let totalPooled = 0;
    let totalUtilization = 0;

    for (const pool of this.pools.values()) {
      const stats = pool.getStats();
      totalObjects += stats.total;
      totalActive += stats.active;
      totalPooled += stats.pooled;
      totalUtilization += stats.utilization;
    }

    return {
      totalPools: this.pools.size,
      totalObjects,
      totalActive,
      totalPooled,
      averageUtilization: this.pools.size > 0 ? totalUtilization / this.pools.size : 0
    };
  }

  destroy(): void {
    this.clearAll();
    this.pools.clear();
  }
}
