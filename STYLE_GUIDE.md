# BulletHell RougeLike RPG - Code Style Guide

## Overview
This document establishes consistent coding patterns and conventions for the project to improve maintainability and readability.

## Naming Conventions

### Classes
- **PascalCase** for class names
- **PascalCase** for interfaces and types
- **<PERSON>Case** for enums

```typescript
// ✅ Good
class GameScene extends BaseScene { }
interface PlayerConfig { }
type LogLevel = 'debug' | 'info' | 'warn' | 'error';
enum GameState { MENU, PLAYING, PAUSED }

// ❌ Bad
class gameScene { }
interface playerConfig { }
```

### Variables and Functions
- **camelCase** for variables, functions, and methods
- **camelCase** for object properties

```typescript
// ✅ Good
const playerPosition = new Vector2();
function updatePlayerMovement() { }
const config = { maxHealth: 100, moveSpeed: 200 };

// ❌ Bad
const PlayerPosition = new Vector2();
function UpdatePlayerMovement() { }
const config = { MaxHealth: 100, MoveSpeed: 200 };
```

### Constants
- **SCREAMING_SNAKE_CASE** for module-level constants
- **camelCase** for local constants

```typescript
// ✅ Good
const COLLISION_LAYERS = ['player', 'enemy', 'projectile'];
const DEFAULT_CONFIG = { width: 1024, height: 768 };

function someFunction() {
  const localConstant = 42;
}

// ❌ Bad
const collisionLayers = ['player', 'enemy', 'projectile'];
const defaultConfig = { width: 1024, height: 768 };
```

### Files and Directories
- **PascalCase** for class files: `GameScene.ts`, `Player.ts`
- **camelCase** for utility files: `mathUtils.ts`, `validation.ts`
- **kebab-case** for directories: `game-entities/`, `error-handling/`

## Code Organization Patterns

### Class Structure
Follow this order for class members:

```typescript
export class ExampleClass {
  // 1. Static properties
  private static readonly DEFAULT_CONFIG = {};
  
  // 2. Public properties
  public position: Vector2;
  public isActive: boolean;
  
  // 3. Private properties
  private config: Config;
  private state: State;
  
  // 4. Constructor
  constructor(config: Config) {
    this.config = config;
  }
  
  // 5. Static methods
  static create(options: Options): ExampleClass {
    return new ExampleClass(options);
  }
  
  // 6. Public methods (lifecycle first)
  initialize(): void { }
  update(deltaTime: number): void { }
  render(renderer: Renderer): void { }
  destroy(): void { }
  
  // 7. Public utility methods
  getPosition(): Vector2 { }
  setPosition(pos: Vector2): void { }
  
  // 8. Private methods
  private updateInternal(): void { }
  private validateConfig(): boolean { }
}
```

### Error Handling
Use the simplified error handler consistently:

```typescript
// ✅ Good
import { error, warn, info, assertNotNull } from '@/utils/error/SimpleErrorHandler';

function riskyOperation(data: unknown): ProcessedData {
  const validData = assertNotNull(data, 'Data cannot be null');
  
  try {
    return processData(validData);
  } catch (err) {
    error('Failed to process data', {
      component: 'dataProcessor',
      action: 'process',
      data: { error: err instanceof Error ? err.message : err }
    });
    throw err;
  }
}

// ❌ Bad - Don't use complex error handling
const errorHandler = ErrorHandler.getInstance();
errorHandler.handleError({
  id: 'complex-error',
  level: 'error',
  message: 'Something went wrong',
  // ... lots of complex properties
});
```

### Function Signatures
Be consistent with parameter ordering and naming:

```typescript
// ✅ Good - Consistent parameter order
function update(deltaTime: number, inputManager: InputManager, context?: GameContext): void { }
function render(renderer: CanvasRenderer, camera?: Camera): void { }
function checkCollision(objectA: GameObject, objectB: GameObject): CollisionResult { }

// ❌ Bad - Inconsistent parameter order
function update(inputManager: InputManager, deltaTime: number): void { }
function render(camera: Camera, renderer: CanvasRenderer): void { }
function checkCollision(objectB: GameObject, objectA: GameObject): CollisionResult { }
```

### Return Types
Always specify return types for public methods:

```typescript
// ✅ Good
public getPosition(): Vector2 { return this.position; }
public isAlive(): boolean { return this.health > 0; }
public update(deltaTime: number): void { /* ... */ }

// ❌ Bad
public getPosition() { return this.position; }
public isAlive() { return this.health > 0; }
```

## Architecture Patterns

### Dependency Injection
Use constructor injection for required dependencies:

```typescript
// ✅ Good
export class GameScene extends BaseScene {
  constructor(
    private inputManager: InputManager,
    private collisionManager: CollisionManager
  ) {
    super();
  }
}

// ❌ Bad - Don't use singletons for everything
export class GameScene extends BaseScene {
  private inputManager = InputManager.getInstance();
  private collisionManager = CollisionManager.getInstance();
}
```

### Configuration Objects
Use typed configuration objects:

```typescript
// ✅ Good
interface PlayerConfig {
  maxHealth: number;
  moveSpeed: number;
  collisionRadius: number;
}

class Player {
  constructor(private config: PlayerConfig) { }
}

// ❌ Bad - Don't use magic numbers
class Player {
  constructor() {
    this.maxHealth = 100; // Magic number
    this.moveSpeed = 200; // Magic number
  }
}
```

### Event Handling
Use consistent event patterns:

```typescript
// ✅ Good - Type-safe events
interface GameEvents {
  playerDied: { player: Player; cause: string };
  enemySpawned: { enemy: Enemy; position: Vector2 };
}

class EventEmitter<T> {
  on<K extends keyof T>(event: K, handler: (data: T[K]) => void): void { }
  emit<K extends keyof T>(event: K, data: T[K]): void { }
}

// ❌ Bad - String-based events
class EventEmitter {
  on(event: string, handler: (data: any) => void): void { }
  emit(event: string, data: any): void { }
}
```

## Performance Guidelines

### Object Creation
Minimize object creation in hot paths:

```typescript
// ✅ Good - Reuse objects
class CollisionSystem {
  private tempVector = new Vector2();
  
  checkCollision(a: GameObject, b: GameObject): boolean {
    this.tempVector.set(b.x - a.x, b.y - a.y);
    return this.tempVector.magnitude() < a.radius + b.radius;
  }
}

// ❌ Bad - Create objects in hot paths
class CollisionSystem {
  checkCollision(a: GameObject, b: GameObject): boolean {
    const diff = new Vector2(b.x - a.x, b.y - a.y); // New object every call
    return diff.magnitude() < a.radius + b.radius;
  }
}
```

### Method Chaining
Support method chaining for fluent APIs:

```typescript
// ✅ Good
class Vector2 {
  add(other: Vector2): Vector2 {
    this.x += other.x;
    this.y += other.y;
    return this; // Enable chaining
  }
  
  multiply(scalar: number): Vector2 {
    this.x *= scalar;
    this.y *= scalar;
    return this;
  }
}

// Usage: vector.add(velocity).multiply(deltaTime);
```

## Testing Patterns

### Test Structure
Use consistent test organization:

```typescript
describe('ClassName', () => {
  describe('methodName', () => {
    it('should do something specific', () => {
      // Arrange
      const input = createTestInput();
      
      // Act
      const result = methodUnderTest(input);
      
      // Assert
      expect(result).toBe(expectedValue);
    });
  });
});
```

### Mock Patterns
Use consistent mocking:

```typescript
// ✅ Good - Explicit mocks
const mockInputManager = {
  getMovementVector: vi.fn(() => new Vector2(1, 0)),
  isKeyPressed: vi.fn(() => false)
} as jest.Mocked<InputManager>;

// ❌ Bad - Implicit any types
const mockInputManager = {
  getMovementVector: vi.fn(),
  isKeyPressed: vi.fn()
};
```

## Documentation Standards

### JSDoc Comments
Use JSDoc for public APIs:

```typescript
/**
 * Calculates the distance between two points
 * @param pointA - The first point
 * @param pointB - The second point
 * @returns The distance between the points
 * @example
 * ```typescript
 * const distance = calculateDistance(
 *   new Vector2(0, 0),
 *   new Vector2(3, 4)
 * ); // Returns 5
 * ```
 */
function calculateDistance(pointA: Vector2, pointB: Vector2): number {
  return pointA.distance(pointB);
}
```

### README Structure
Follow this structure for component READMEs:

```markdown
# Component Name

## Overview
Brief description of what this component does.

## Usage
Basic usage examples.

## API Reference
Public methods and properties.

## Performance Notes
Any performance considerations.

## Testing
How to test this component.
```

## Enforcement

### Linting Rules
Configure ESLint with these rules:
- `@typescript-eslint/naming-convention`
- `@typescript-eslint/explicit-function-return-type`
- `@typescript-eslint/no-explicit-any`
- `prefer-const`
- `no-var`

### Pre-commit Hooks
Set up pre-commit hooks to enforce:
- Code formatting (Prettier)
- Linting (ESLint)
- Type checking (TypeScript)
- Test execution

This style guide should be followed for all new code and gradually applied to existing code during refactoring.
