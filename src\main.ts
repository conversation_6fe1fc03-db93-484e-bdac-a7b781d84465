/**
 * Main entry point for the Bullet Hell Rogue-like RPG
 * Initializes the game engine and starts the game loop
 */

import { AudioManager } from '@/engine/audio/AudioManager';
import { AssetManager } from '@/engine/core/AssetManager';
import { Game } from '@/engine/core/Game';
import { SceneManager } from '@/engine/core/SceneManager';
import { InputManager } from '@/engine/input/InputManager';
import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';
import { GameScene } from '@/game/scenes/GameScene';
import { LoadingScene } from '@/game/scenes/LoadingScene';
import { MenuScene } from '@/game/scenes/MenuScene';
import { ErrorHandler } from '@/utils/error/ErrorHandler';
import { InputValidator } from '@/utils/validation/InputValidator';

// Game configuration
const GAME_CONFIG = {
  canvas: {
    width: 1024,
    height: 768,
    targetFPS: 60,
    pixelRatio: window.devicePixelRatio || 1,
  },
  debug: {
    showFPS: true,
    showColliders: false,
    logPerformance: true,
  },
  mobile: {
    enableTouchControls: 'ontouchstart' in window,
    scaleToFit: true,
  }
};

class GameApplication {
  private game: Game | null = null;
  private canvas: HTMLCanvasElement | null = null;
  private loadingScreen: HTMLElement | null = null;
  private errorHandler: ErrorHandler;

  constructor() {
    this.errorHandler = ErrorHandler.getInstance();
    this.setupGlobalErrorHandling();
  }

  private setupGlobalErrorHandling(): void {
    // Handle uncaught errors
    window.addEventListener('error', (event) => {
      this.errorHandler.error('Uncaught error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
      });
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.errorHandler.error('Unhandled promise rejection', {
        reason: event.reason
      });
      event.preventDefault(); // Prevent console error
    });

    // Handle canvas context lost
    window.addEventListener('webglcontextlost', (event) => {
      this.errorHandler.warning('WebGL context lost', { event });
      event.preventDefault();
    });
  }

  async initialize(): Promise<void> {
    try {
      // Validate game configuration
      const configValidation = InputValidator.validateGameConfig(GAME_CONFIG);
      if (!configValidation.isValid) {
        this.errorHandler.error('Invalid game configuration', {
          errors: configValidation.errors
        });
        throw new Error('Game configuration validation failed');
      }

      // Get canvas element
      this.canvas = document.getElementById('gameCanvas') as HTMLCanvasElement;
      this.loadingScreen = document.getElementById('loadingScreen');

      if (!this.canvas) {
        this.errorHandler.error('Game canvas element not found in DOM');
        throw new Error('Game canvas not found');
      }

      // Validate canvas element
      if (!(this.canvas instanceof HTMLCanvasElement)) {
        this.errorHandler.error('Element with id "gameCanvas" is not a canvas element');
        throw new Error('Invalid canvas element');
      }

      // Setup canvas
      this.setupCanvas();

      // Initialize core systems with error handling
      let renderer: CanvasRenderer;
      let inputManager: InputManager;
      let audioManager: AudioManager;
      let sceneManager: SceneManager;
      let assetManager: AssetManager;

      try {
        renderer = new CanvasRenderer(this.canvas, GAME_CONFIG.canvas);
      } catch (error) {
        this.errorHandler.error('Failed to initialize renderer', error);
        throw new Error('Renderer initialization failed');
      }

      try {
        inputManager = new InputManager(this.canvas);
      } catch (error) {
        this.errorHandler.error('Failed to initialize input manager', error);
        throw new Error('Input manager initialization failed');
      }

      try {
        audioManager = new AudioManager();
      } catch (error) {
        this.errorHandler.warning('Failed to initialize audio manager', error);
        // Audio is not critical, create a mock audio manager
        audioManager = new AudioManager();
      }

      try {
        sceneManager = new SceneManager();
        assetManager = new AssetManager();
      } catch (error) {
        this.errorHandler.error('Failed to initialize core systems', error);
        throw new Error('Core systems initialization failed');
      }

      // Create game instance
      try {
        this.game = new Game({
          renderer,
          inputManager,
          audioManager,
          sceneManager,
          assetManager,
          config: GAME_CONFIG
        });
      } catch (error) {
        this.errorHandler.error('Failed to create game instance', error);
        throw new Error('Game instance creation failed');
      }

      // Load initial assets with timeout
      try {
        await Promise.race([
          this.loadAssets(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Asset loading timeout')), 30000)
          )
        ]);
      } catch (error) {
        this.errorHandler.error('Failed to load assets', error);
        // Continue with default assets or show error to user
        this.showError(error as Error);
        return;
      }

      // Setup scenes
      try {
        this.setupScenes();
      } catch (error) {
        this.errorHandler.error('Failed to setup scenes', error);
        throw new Error('Scene setup failed');
      }

      // Hide loading screen
      this.hideLoadingScreen();

      // Start the game
      this.game.start();

      console.log('🎮 Bullet Hell Rogue-like RPG initialized successfully!');

    } catch (error) {
      console.error('❌ Failed to initialize game:', error);
      this.showError(error as Error);
    }
  }

  private setupCanvas(): void {
    if (!this.canvas) return;

    const { width, height, pixelRatio } = GAME_CONFIG.canvas;

    // Set canvas size
    this.canvas.width = width * pixelRatio;
    this.canvas.height = height * pixelRatio;
    this.canvas.style.width = `${width}px`;
    this.canvas.style.height = `${height}px`;

    // Scale canvas for high DPI displays
    const ctx = this.canvas.getContext('2d');
    if (ctx && pixelRatio !== 1) {
      ctx.scale(pixelRatio, pixelRatio);
    }

    // Mobile responsive scaling
    if (GAME_CONFIG.mobile.scaleToFit) {
      this.setupResponsiveCanvas();
    }
  }

  private setupResponsiveCanvas(): void {
    if (!this.canvas) return;

    const resizeCanvas = () => {
      const container = document.getElementById('gameContainer');
      if (!container) return;

      const containerWidth = container.clientWidth;
      const containerHeight = container.clientHeight;
      const gameAspectRatio = GAME_CONFIG.canvas.width / GAME_CONFIG.canvas.height;
      const containerAspectRatio = containerWidth / containerHeight;

      let scale: number;
      if (containerAspectRatio > gameAspectRatio) {
        scale = containerHeight / GAME_CONFIG.canvas.height;
      } else {
        scale = containerWidth / GAME_CONFIG.canvas.width;
      }

      // Limit scale to prevent too small or too large rendering
      scale = Math.max(0.5, Math.min(2, scale));

      this.canvas!.style.transform = `scale(${scale})`;
    };

    window.addEventListener('resize', resizeCanvas);
    resizeCanvas();
  }

  private async loadAssets(): Promise<void> {
    if (!this.game) return;

    // This will be expanded with actual asset loading
    // For now, we'll use procedurally generated assets
    console.log('📦 Loading game assets...');

    // Simulate loading time for better UX
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  private setupScenes(): void {
    if (!this.game) return;

    const sceneManager = this.game.getSceneManager();
    const inputManager = this.game.getInputManager();

    // Create and configure menu scene
    const menuScene = new MenuScene();
    menuScene.setSceneManager(sceneManager);
    menuScene.setInputManager(inputManager);

    // Create and configure game scene
    const gameScene = new GameScene();
    gameScene.setInputManager(inputManager);

    // Register scenes
    sceneManager.addScene('loading', new LoadingScene());
    sceneManager.addScene('menu', menuScene);
    sceneManager.addScene('game', gameScene);

    // Start with menu scene
    sceneManager.switchTo('menu');
  }

  private hideLoadingScreen(): void {
    if (this.loadingScreen) {
      this.loadingScreen.style.opacity = '0';
      setTimeout(() => {
        if (this.loadingScreen) {
          this.loadingScreen.style.display = 'none';
        }
      }, 500);
    }
  }

  private showError(error: Error): void {
    if (this.loadingScreen) {
      this.loadingScreen.innerHTML = `
        <div style="color: #ff6b6b; text-align: center;">
          <h2>❌ Game Failed to Load</h2>
          <p>${error.message}</p>
          <button onclick="location.reload()" style="
            margin-top: 20px;
            padding: 10px 20px;
            background: #333;
            color: white;
            border: 1px solid #666;
            cursor: pointer;
            border-radius: 4px;
          ">Retry</button>
        </div>
      `;
    }
  }

  destroy(): void {
    if (this.game) {
      this.game.destroy();
      this.game = null;
    }
  }
}

// Initialize the game when DOM is ready
document.addEventListener('DOMContentLoaded', async () => {
  const app = new GameApplication();
  await app.initialize();

  // Handle page unload
  window.addEventListener('beforeunload', () => {
    app.destroy();
  });
});

// Handle visibility change for performance optimization
document.addEventListener('visibilitychange', () => {
  // Game will handle pause/resume internally
});

// Export for debugging
(window as any).game = GameApplication;
