#!/usr/bin/env node

/**
 * Enhanced test runner with coverage reporting and quality gates
 */

import { spawn } from 'child_process';
import { readFileSync, existsSync } from 'fs';
import { join } from 'path';

const COVERAGE_THRESHOLDS = {
  statements: 70,
  branches: 70,
  functions: 70,
  lines: 70
};

const CRITICAL_FILES = [
  'src/engine/core/ObjectPool.ts',
  'src/engine/physics/CollisionSystem.ts',
  'src/engine/physics/SpatialGrid.ts',
  'src/utils/math/Vector2.ts',
  'src/game/scenes/GameScene.ts'
];

class TestRunner {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0,
      duration: 0,
      coverage: null
    };
  }

  async runTests(options = {}) {
    const {
      coverage = false,
      watch = false,
      ui = false,
      filter = '',
      verbose = false
    } = options;

    console.log('🧪 Starting test suite...\n');

    const vitestArgs = ['vitest'];
    
    if (coverage) {
      vitestArgs.push('--coverage');
    }
    
    if (watch) {
      vitestArgs.push('--watch');
    }
    
    if (ui) {
      vitestArgs.push('--ui');
    }
    
    if (filter) {
      vitestArgs.push('--grep', filter);
    }

    if (verbose) {
      vitestArgs.push('--reporter=verbose');
    }

    try {
      const startTime = Date.now();
      await this.executeVitest(vitestArgs);
      this.testResults.duration = Date.now() - startTime;

      if (coverage) {
        await this.processCoverageResults();
      }

      this.printSummary();
      return this.testResults;
    } catch (error) {
      console.error('❌ Test execution failed:', error.message);
      process.exit(1);
    }
  }

  async executeVitest(args) {
    return new Promise((resolve, reject) => {
      const child = spawn('npx', args, {
        stdio: 'inherit',
        shell: true
      });

      child.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Tests failed with exit code ${code}`));
        }
      });

      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  async processCoverageResults() {
    const coverageFile = join(process.cwd(), 'coverage', 'coverage-summary.json');
    
    if (!existsSync(coverageFile)) {
      console.log('⚠️  No coverage file found');
      return;
    }

    try {
      const coverageData = JSON.parse(readFileSync(coverageFile, 'utf8'));
      this.testResults.coverage = coverageData;
      
      this.validateCoverage(coverageData);
    } catch (error) {
      console.error('❌ Failed to process coverage results:', error.message);
    }
  }

  validateCoverage(coverageData) {
    console.log('\n📊 Coverage Analysis');
    console.log('====================');

    const total = coverageData.total;
    const overallCoverage = {
      statements: total.statements.pct,
      branches: total.branches.pct,
      functions: total.functions.pct,
      lines: total.lines.pct
    };

    console.log('Overall Coverage:');
    console.log(`  Statements: ${overallCoverage.statements.toFixed(1)}% ${this.getCoverageStatus(overallCoverage.statements, COVERAGE_THRESHOLDS.statements)}`);
    console.log(`  Branches:   ${overallCoverage.branches.toFixed(1)}% ${this.getCoverageStatus(overallCoverage.branches, COVERAGE_THRESHOLDS.branches)}`);
    console.log(`  Functions:  ${overallCoverage.functions.toFixed(1)}% ${this.getCoverageStatus(overallCoverage.functions, COVERAGE_THRESHOLDS.functions)}`);
    console.log(`  Lines:      ${overallCoverage.lines.toFixed(1)}% ${this.getCoverageStatus(overallCoverage.lines, COVERAGE_THRESHOLDS.lines)}`);

    // Check critical files
    console.log('\nCritical Files:');
    let criticalFilesPassed = 0;
    
    for (const criticalFile of CRITICAL_FILES) {
      const fileData = coverageData[criticalFile];
      if (fileData) {
        const fileCoverage = fileData.statements.pct;
        const status = fileCoverage >= 85 ? '✅' : '❌';
        console.log(`  ${status} ${criticalFile}: ${fileCoverage.toFixed(1)}%`);
        if (fileCoverage >= 85) criticalFilesPassed++;
      } else {
        console.log(`  ⚠️  ${criticalFile}: Not found in coverage`);
      }
    }

    // Overall assessment
    const overallPassed = Object.values(overallCoverage).every((value, index) => 
      value >= Object.values(COVERAGE_THRESHOLDS)[index]
    );

    console.log('\n' + '='.repeat(50));
    if (overallPassed && criticalFilesPassed === CRITICAL_FILES.length) {
      console.log('✅ All coverage thresholds met!');
    } else {
      console.log('❌ Coverage thresholds not met');
      this.printCoverageRecommendations(overallCoverage);
    }
  }

  getCoverageStatus(actual, threshold) {
    if (actual >= threshold) {
      return '✅';
    } else if (actual >= threshold - 10) {
      return '⚠️';
    } else {
      return '❌';
    }
  }

  printCoverageRecommendations(coverage) {
    console.log('\n💡 Recommendations:');
    
    if (coverage.statements < COVERAGE_THRESHOLDS.statements) {
      console.log('  - Add more unit tests to increase statement coverage');
    }
    
    if (coverage.branches < COVERAGE_THRESHOLDS.branches) {
      console.log('  - Add tests for edge cases and error conditions');
    }
    
    if (coverage.functions < COVERAGE_THRESHOLDS.functions) {
      console.log('  - Ensure all public methods have tests');
    }
    
    if (coverage.lines < COVERAGE_THRESHOLDS.lines) {
      console.log('  - Review untested code paths');
    }

    console.log('  - Focus on critical system files first');
    console.log('  - Consider adding integration tests');
  }

  printSummary() {
    console.log('\n' + '='.repeat(50));
    console.log('🏁 Test Summary');
    console.log('='.repeat(50));
    console.log(`Duration: ${(this.testResults.duration / 1000).toFixed(2)}s`);
    
    if (this.testResults.coverage) {
      const total = this.testResults.coverage.total;
      console.log(`Overall Coverage: ${total.statements.pct.toFixed(1)}%`);
    }
    
    console.log('='.repeat(50));
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const options = {
    coverage: args.includes('--coverage'),
    watch: args.includes('--watch'),
    ui: args.includes('--ui'),
    verbose: args.includes('--verbose'),
    filter: ''
  };

  // Extract filter if provided
  const grepIndex = args.indexOf('--grep');
  if (grepIndex !== -1 && args[grepIndex + 1]) {
    options.filter = args[grepIndex + 1];
  }

  const runner = new TestRunner();
  await runner.runTests(options);
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export default TestRunner;
