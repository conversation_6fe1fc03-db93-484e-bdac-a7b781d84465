/**
 * High-performance Canvas 2D renderer with optimizations for 60 FPS
 */

export interface RenderConfig {
  width: number;
  height: number;
  targetFPS: number;
  pixelRatio: number;
}

export interface Transform {
  x: number;
  y: number;
  rotation: number;
  scaleX: number;
  scaleY: number;
}

export class CanvasRenderer {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private config: RenderConfig;
  
  private transformStack: Transform[] = [];
  private currentTransform: Transform = { x: 0, y: 0, rotation: 0, scaleX: 1, scaleY: 1 };
  
  // Performance optimizations
  private imageCache: Map<string, HTMLImageElement> = new Map();
  private pathCache: Map<string, Path2D> = new Map();
  
  constructor(canvas: HTMLCanvasElement, config: RenderConfig) {
    this.canvas = canvas;
    this.config = config;
    
    const ctx = canvas.getContext('2d', {
      alpha: false, // Disable alpha for better performance
      desynchronized: true, // Allow async rendering
    });
    
    if (!ctx) {
      throw new Error('Failed to get 2D rendering context');
    }
    
    this.ctx = ctx;
    this.setupContext();
  }

  private setupContext(): void {
    // Optimize context settings for performance
    this.ctx.imageSmoothingEnabled = false; // Pixel-perfect rendering
    this.ctx.textBaseline = 'top';
    this.ctx.textAlign = 'left';
    
    // Set initial transform
    this.resetTransform();
  }

  clear(color: string = '#000000'): void {
    this.ctx.fillStyle = color;
    this.ctx.fillRect(0, 0, this.config.width, this.config.height);
  }

  present(): void {
    // In Canvas 2D, presentation is automatic
    // This method exists for API consistency and future optimizations
  }

  // Transform methods
  pushTransform(): void {
    this.transformStack.push({ ...this.currentTransform });
    this.ctx.save();
  }

  popTransform(): void {
    if (this.transformStack.length > 0) {
      this.currentTransform = this.transformStack.pop()!;
      this.ctx.restore();
    }
  }

  translate(x: number, y: number): void {
    this.currentTransform.x += x;
    this.currentTransform.y += y;
    this.ctx.translate(x, y);
  }

  rotate(angle: number): void {
    this.currentTransform.rotation += angle;
    this.ctx.rotate(angle);
  }

  scale(x: number, y: number = x): void {
    this.currentTransform.scaleX *= x;
    this.currentTransform.scaleY *= y;
    this.ctx.scale(x, y);
  }

  resetTransform(): void {
    this.currentTransform = { x: 0, y: 0, rotation: 0, scaleX: 1, scaleY: 1 };
    this.ctx.resetTransform();
    
    // Apply pixel ratio scaling
    if (this.config.pixelRatio !== 1) {
      this.ctx.scale(this.config.pixelRatio, this.config.pixelRatio);
    }
  }

  // Drawing methods
  drawRect(x: number, y: number, width: number, height: number, color: string): void {
    this.ctx.fillStyle = color;
    this.ctx.fillRect(x, y, width, height);
  }

  drawRectOutline(x: number, y: number, width: number, height: number, color: string, lineWidth: number = 1): void {
    this.ctx.strokeStyle = color;
    this.ctx.lineWidth = lineWidth;
    this.ctx.strokeRect(x, y, width, height);
  }

  drawCircle(x: number, y: number, radius: number, color: string): void {
    this.ctx.fillStyle = color;
    this.ctx.beginPath();
    this.ctx.arc(x, y, radius, 0, Math.PI * 2);
    this.ctx.fill();
  }

  drawCircleOutline(x: number, y: number, radius: number, color: string, lineWidth: number = 1): void {
    this.ctx.strokeStyle = color;
    this.ctx.lineWidth = lineWidth;
    this.ctx.beginPath();
    this.ctx.arc(x, y, radius, 0, Math.PI * 2);
    this.ctx.stroke();
  }

  drawLine(x1: number, y1: number, x2: number, y2: number, color: string, lineWidth: number = 1): void {
    this.ctx.strokeStyle = color;
    this.ctx.lineWidth = lineWidth;
    this.ctx.beginPath();
    this.ctx.moveTo(x1, y1);
    this.ctx.lineTo(x2, y2);
    this.ctx.stroke();
  }

  drawText(text: string, x: number, y: number, font: string = '16px Arial', color: string = '#ffffff'): void {
    this.ctx.fillStyle = color;
    this.ctx.font = font;
    this.ctx.fillText(text, x, y);
  }

  drawImage(
    image: HTMLImageElement,
    sx: number = 0, sy: number = 0, sw?: number, sy2?: number,
    dx: number = 0, dy: number = 0, dw?: number, dh?: number
  ): void {
    if (sw !== undefined && sy2 !== undefined && dw !== undefined && dh !== undefined) {
      // Draw with source and destination rectangles
      this.ctx.drawImage(image, sx, sy, sw, sy2, dx, dy, dw, dh);
    } else {
      // Draw at position with original size
      this.ctx.drawImage(image, dx, dy);
    }
  }

  drawSprite(
    image: HTMLImageElement,
    sourceX: number, sourceY: number, sourceWidth: number, sourceHeight: number,
    destX: number, destY: number, destWidth?: number, destHeight?: number
  ): void {
    const dw = destWidth || sourceWidth;
    const dh = destHeight || sourceHeight;
    this.ctx.drawImage(image, sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, dw, dh);
  }

  // Utility methods
  setGlobalAlpha(alpha: number): void {
    this.ctx.globalAlpha = Math.max(0, Math.min(1, alpha));
  }

  setBlendMode(mode: GlobalCompositeOperation): void {
    this.ctx.globalCompositeOperation = mode;
  }

  getCanvasSize(): { width: number; height: number } {
    return { width: this.config.width, height: this.config.height };
  }

  getContext(): CanvasRenderingContext2D {
    return this.ctx;
  }

  // Performance methods
  cacheImage(id: string, image: HTMLImageElement): void {
    this.imageCache.set(id, image);
  }

  getCachedImage(id: string): HTMLImageElement | undefined {
    return this.imageCache.get(id);
  }

  clearImageCache(): void {
    this.imageCache.clear();
  }

  destroy(): void {
    this.clearImageCache();
    this.pathCache.clear();
    this.transformStack = [];
  }
}
