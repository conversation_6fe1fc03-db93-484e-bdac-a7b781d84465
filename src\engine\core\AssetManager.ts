/**
 * Asset management system for loading and caching game resources
 */

export interface Asset {
  id: string;
  type: 'image' | 'audio' | 'json' | 'text';
  data: any;
  loaded: boolean;
}

export class AssetManager {
  private assets: Map<string, Asset> = new Map();
  private loadingPromises: Map<string, Promise<Asset>> = new Map();

  async loadImage(id: string, src: string): Promise<HTMLImageElement> {
    if (this.assets.has(id)) {
      const asset = this.assets.get(id)!;
      if (asset.loaded) {
        return asset.data as HTMLImageElement;
      }
    }

    // Check if already loading
    if (this.loadingPromises.has(id)) {
      const asset = await this.loadingPromises.get(id)!;
      return asset.data as HTMLImageElement;
    }

    const promise = this.createImageLoadPromise(id, src);
    this.loadingPromises.set(id, promise);

    try {
      const asset = await promise;
      this.loadingPromises.delete(id);
      return asset.data as HTMLImageElement;
    } catch (error) {
      this.loadingPromises.delete(id);
      throw error;
    }
  }

  private createImageLoadPromise(id: string, src: string): Promise<Asset> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        const asset: Asset = {
          id,
          type: 'image',
          data: img,
          loaded: true
        };
        this.assets.set(id, asset);
        resolve(asset);
      };

      img.onerror = () => {
        reject(new Error(`Failed to load image: ${src}`));
      };

      img.src = src;
    });
  }

  async loadAudio(id: string, src: string): Promise<HTMLAudioElement> {
    if (this.assets.has(id)) {
      const asset = this.assets.get(id)!;
      if (asset.loaded) {
        return asset.data as HTMLAudioElement;
      }
    }

    if (this.loadingPromises.has(id)) {
      const asset = await this.loadingPromises.get(id)!;
      return asset.data as HTMLAudioElement;
    }

    const promise = this.createAudioLoadPromise(id, src);
    this.loadingPromises.set(id, promise);

    try {
      const asset = await promise;
      this.loadingPromises.delete(id);
      return asset.data as HTMLAudioElement;
    } catch (error) {
      this.loadingPromises.delete(id);
      throw error;
    }
  }

  private createAudioLoadPromise(id: string, src: string): Promise<Asset> {
    return new Promise((resolve, reject) => {
      const audio = new Audio();
      
      audio.oncanplaythrough = () => {
        const asset: Asset = {
          id,
          type: 'audio',
          data: audio,
          loaded: true
        };
        this.assets.set(id, asset);
        resolve(asset);
      };

      audio.onerror = () => {
        reject(new Error(`Failed to load audio: ${src}`));
      };

      audio.src = src;
      audio.load();
    });
  }

  async loadJSON(id: string, src: string): Promise<any> {
    if (this.assets.has(id)) {
      const asset = this.assets.get(id)!;
      if (asset.loaded) {
        return asset.data;
      }
    }

    if (this.loadingPromises.has(id)) {
      const asset = await this.loadingPromises.get(id)!;
      return asset.data;
    }

    const promise = this.createJSONLoadPromise(id, src);
    this.loadingPromises.set(id, promise);

    try {
      const asset = await promise;
      this.loadingPromises.delete(id);
      return asset.data;
    } catch (error) {
      this.loadingPromises.delete(id);
      throw error;
    }
  }

  private async createJSONLoadPromise(id: string, src: string): Promise<Asset> {
    try {
      const response = await fetch(src);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      
      const asset: Asset = {
        id,
        type: 'json',
        data,
        loaded: true
      };
      this.assets.set(id, asset);
      return asset;
    } catch (error) {
      throw new Error(`Failed to load JSON: ${src} - ${error}`);
    }
  }

  getAsset(id: string): Asset | null {
    return this.assets.get(id) || null;
  }

  isLoaded(id: string): boolean {
    const asset = this.assets.get(id);
    return asset ? asset.loaded : false;
  }

  unloadAsset(id: string): void {
    this.assets.delete(id);
  }

  unloadAll(): void {
    this.assets.clear();
    this.loadingPromises.clear();
  }

  getLoadedAssets(): string[] {
    return Array.from(this.assets.keys()).filter(id => this.isLoaded(id));
  }

  getMemoryUsage(): { totalAssets: number; loadedAssets: number } {
    const totalAssets = this.assets.size;
    const loadedAssets = this.getLoadedAssets().length;
    return { totalAssets, loadedAssets };
  }
}
