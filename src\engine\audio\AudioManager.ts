/**
 * Audio management system with Web Audio API support and fallback to HTML5 Audio
 */

export interface AudioConfig {
  volume: number;
  loop: boolean;
  fadeIn?: number;
  fadeOut?: number;
}

export interface AudioSource {
  id: string;
  buffer?: AudioBuffer;
  element?: HTMLAudioElement;
  config: AudioConfig;
  isPlaying: boolean;
  isPaused: boolean;
  startTime: number;
  pauseTime: number;
}

export class AudioManager {
  private audioContext: AudioContext | null = null;
  private masterGain: GainNode | null = null;
  private sources: Map<string, AudioSource> = new Map();
  private loadedBuffers: Map<string, AudioBuffer> = new Map();
  
  private masterVolume: number = 1.0;
  private musicVolume: number = 0.7;
  private sfxVolume: number = 0.8;
  
  private useWebAudio: boolean = true;

  constructor() {
    this.initializeAudioContext();
  }

  private initializeAudioContext(): void {
    try {
      // Try to create Web Audio Context
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      this.masterGain = this.audioContext.createGain();
      this.masterGain.connect(this.audioContext.destination);
      this.masterGain.gain.value = this.masterVolume;
      
      // Handle audio context state
      if (this.audioContext.state === 'suspended') {
        // Will be resumed on first user interaction
        document.addEventListener('click', this.resumeAudioContext.bind(this), { once: true });
        document.addEventListener('keydown', this.resumeAudioContext.bind(this), { once: true });
        document.addEventListener('touchstart', this.resumeAudioContext.bind(this), { once: true });
      }
      
      console.log('🔊 Web Audio API initialized');
    } catch (error) {
      console.warn('Web Audio API not supported, falling back to HTML5 Audio:', error);
      this.useWebAudio = false;
    }
  }

  private async resumeAudioContext(): Promise<void> {
    if (this.audioContext && this.audioContext.state === 'suspended') {
      await this.audioContext.resume();
      console.log('🔊 Audio context resumed');
    }
  }

  async loadAudio(id: string, src: string, config: Partial<AudioConfig> = {}): Promise<void> {
    const audioConfig: AudioConfig = {
      volume: 1.0,
      loop: false,
      ...config
    };

    if (this.useWebAudio && this.audioContext) {
      try {
        const response = await fetch(src);
        const arrayBuffer = await response.arrayBuffer();
        const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
        
        this.loadedBuffers.set(id, audioBuffer);
        
        const source: AudioSource = {
          id,
          buffer: audioBuffer,
          config: audioConfig,
          isPlaying: false,
          isPaused: false,
          startTime: 0,
          pauseTime: 0
        };
        
        this.sources.set(id, source);
      } catch (error) {
        console.warn(`Failed to load audio with Web Audio API: ${src}`, error);
        await this.loadAudioFallback(id, src, audioConfig);
      }
    } else {
      await this.loadAudioFallback(id, src, audioConfig);
    }
  }

  private async loadAudioFallback(id: string, src: string, config: AudioConfig): Promise<void> {
    return new Promise((resolve, reject) => {
      const audio = new Audio();
      
      audio.oncanplaythrough = () => {
        const source: AudioSource = {
          id,
          element: audio,
          config,
          isPlaying: false,
          isPaused: false,
          startTime: 0,
          pauseTime: 0
        };
        
        this.sources.set(id, source);
        resolve();
      };
      
      audio.onerror = () => {
        reject(new Error(`Failed to load audio: ${src}`));
      };
      
      audio.src = src;
      audio.load();
    });
  }

  play(id: string, config?: Partial<AudioConfig>): void {
    const source = this.sources.get(id);
    if (!source) {
      console.warn(`Audio source not found: ${id}`);
      return;
    }

    // Merge config if provided
    if (config) {
      Object.assign(source.config, config);
    }

    if (this.useWebAudio && this.audioContext && source.buffer) {
      this.playWebAudio(source);
    } else if (source.element) {
      this.playHTML5Audio(source);
    }
  }

  private playWebAudio(source: AudioSource): void {
    if (!this.audioContext || !this.masterGain || !source.buffer) return;

    // Create new buffer source for each play
    const bufferSource = this.audioContext.createBufferSource();
    const gainNode = this.audioContext.createGain();
    
    bufferSource.buffer = source.buffer;
    bufferSource.loop = source.config.loop;
    
    // Set volume
    const volume = source.config.volume * this.getVolumeMultiplier(source.id);
    gainNode.gain.value = volume;
    
    // Connect nodes
    bufferSource.connect(gainNode);
    gainNode.connect(this.masterGain);
    
    // Handle fade in
    if (source.config.fadeIn) {
      gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
      gainNode.gain.linearRampToValueAtTime(volume, this.audioContext.currentTime + source.config.fadeIn);
    }
    
    // Start playing
    bufferSource.start();
    source.isPlaying = true;
    source.isPaused = false;
    source.startTime = this.audioContext.currentTime;
    
    // Handle end
    bufferSource.onended = () => {
      source.isPlaying = false;
    };
  }

  private playHTML5Audio(source: AudioSource): void {
    if (!source.element) return;

    const audio = source.element;
    audio.volume = source.config.volume * this.getVolumeMultiplier(source.id) * this.masterVolume;
    audio.loop = source.config.loop;
    
    audio.currentTime = 0;
    audio.play().catch(error => {
      console.warn(`Failed to play audio: ${source.id}`, error);
    });
    
    source.isPlaying = true;
    source.isPaused = false;
    source.startTime = performance.now();
  }

  stop(id: string): void {
    const source = this.sources.get(id);
    if (!source) return;

    if (source.element) {
      source.element.pause();
      source.element.currentTime = 0;
    }
    
    source.isPlaying = false;
    source.isPaused = false;
  }

  pause(id: string): void {
    const source = this.sources.get(id);
    if (!source || !source.isPlaying) return;

    if (source.element) {
      source.element.pause();
    }
    
    source.isPlaying = false;
    source.isPaused = true;
    source.pauseTime = performance.now();
  }

  resume(id: string): void {
    const source = this.sources.get(id);
    if (!source || !source.isPaused) return;

    if (source.element) {
      source.element.play().catch(error => {
        console.warn(`Failed to resume audio: ${source.id}`, error);
      });
    }
    
    source.isPlaying = true;
    source.isPaused = false;
  }

  pauseAll(): void {
    for (const source of this.sources.values()) {
      if (source.isPlaying) {
        this.pause(source.id);
      }
    }
  }

  resumeAll(): void {
    for (const source of this.sources.values()) {
      if (source.isPaused) {
        this.resume(source.id);
      }
    }
  }

  stopAll(): void {
    for (const source of this.sources.values()) {
      this.stop(source.id);
    }
  }

  private getVolumeMultiplier(id: string): number {
    // Determine if this is music or SFX based on ID convention
    if (id.startsWith('music_') || id.includes('bgm')) {
      return this.musicVolume;
    }
    return this.sfxVolume;
  }

  // Volume controls
  setMasterVolume(volume: number): void {
    this.masterVolume = Math.max(0, Math.min(1, volume));
    if (this.masterGain) {
      this.masterGain.gain.value = this.masterVolume;
    }
  }

  setMusicVolume(volume: number): void {
    this.musicVolume = Math.max(0, Math.min(1, volume));
  }

  setSFXVolume(volume: number): void {
    this.sfxVolume = Math.max(0, Math.min(1, volume));
  }

  getMasterVolume(): number { return this.masterVolume; }
  getMusicVolume(): number { return this.musicVolume; }
  getSFXVolume(): number { return this.sfxVolume; }

  isPlaying(id: string): boolean {
    const source = this.sources.get(id);
    return source ? source.isPlaying : false;
  }

  // Procedural sound generation
  generateTone(frequency: number, duration: number, type: OscillatorType = 'sine'): AudioBuffer | null {
    if (!this.audioContext) return null;

    const sampleRate = this.audioContext.sampleRate;
    const length = sampleRate * duration;
    const buffer = this.audioContext.createBuffer(1, length, sampleRate);
    const data = buffer.getChannelData(0);

    for (let i = 0; i < length; i++) {
      const t = i / sampleRate;
      switch (type) {
        case 'sine':
          data[i] = Math.sin(2 * Math.PI * frequency * t);
          break;
        case 'square':
          data[i] = Math.sign(Math.sin(2 * Math.PI * frequency * t));
          break;
        case 'sawtooth':
          data[i] = 2 * (t * frequency - Math.floor(t * frequency + 0.5));
          break;
        case 'triangle':
          data[i] = 2 * Math.abs(2 * (t * frequency - Math.floor(t * frequency + 0.5))) - 1;
          break;
      }

      // Apply envelope
      const envelope = Math.min(1, Math.min(t * 10, (duration - t) * 10));
      data[i] *= envelope * 0.3;
    }

    return buffer;
  }

  createProceduralSounds(): void {
    if (!this.audioContext) return;

    const sounds = [
      { id: 'shoot', freq: 800, duration: 0.1, type: 'square' as OscillatorType },
      { id: 'hit', freq: 200, duration: 0.15, type: 'sawtooth' as OscillatorType },
      { id: 'pickup', freq: 600, duration: 0.2, type: 'sine' as OscillatorType },
      { id: 'levelup', freq: 440, duration: 0.5, type: 'triangle' as OscillatorType },
      { id: 'explosion', freq: 100, duration: 0.3, type: 'sawtooth' as OscillatorType },
      { id: 'enemy_death', freq: 150, duration: 0.4, type: 'sawtooth' as OscillatorType }
    ];

    for (const sound of sounds) {
      const buffer = this.generateTone(sound.freq, sound.duration, sound.type);
      if (buffer) {
        this.loadedBuffers.set(sound.id, buffer);

        const source: AudioSource = {
          id: sound.id,
          buffer: buffer,
          config: { volume: 0.5, loop: false },
          isPlaying: false,
          isPaused: false,
          startTime: 0,
          pauseTime: 0
        };

        this.sources.set(sound.id, source);
      }
    }

    console.log('🎼 Procedural sounds generated');
  }

  // Spatial audio support
  playPositional(id: string, x: number, y: number, listenerX: number = 0, listenerY: number = 0): void {
    if (!this.useWebAudio || !this.audioContext || !this.masterGain) {
      this.play(id);
      return;
    }

    const source = this.sources.get(id);
    if (!source || !source.buffer) return;

    // Create audio nodes
    const bufferSource = this.audioContext.createBufferSource();
    const panner = this.audioContext.createPanner();
    const gainNode = this.audioContext.createGain();

    // Configure panner
    panner.panningModel = 'HRTF';
    panner.distanceModel = 'inverse';
    panner.refDistance = 100;
    panner.maxDistance = 1000;
    panner.rolloffFactor = 1;

    // Set positions
    panner.setPosition(x, y, 0);
    if (this.audioContext.listener.setPosition) {
      this.audioContext.listener.setPosition(listenerX, listenerY, 0);
    }

    // Configure source
    bufferSource.buffer = source.buffer;
    bufferSource.loop = source.config.loop;

    // Calculate distance-based volume
    const distance = Math.sqrt((x - listenerX) ** 2 + (y - listenerY) ** 2);
    const volumeMultiplier = Math.max(0.1, 1 - distance / 500);
    gainNode.gain.value = source.config.volume * this.getVolumeMultiplier(source.id) * volumeMultiplier;

    // Connect nodes
    bufferSource.connect(panner);
    panner.connect(gainNode);
    gainNode.connect(this.masterGain);

    // Start playing
    bufferSource.start();
  }

  // Music management
  playMusic(id: string, fadeIn: number = 1): void {
    // Stop current music
    this.stopMusic(1);

    setTimeout(() => {
      this.play(id, { loop: true, fadeIn, volume: 0.8 });
    }, fadeIn * 1000);
  }

  stopMusic(fadeOut: number = 0): void {
    for (const source of this.sources.values()) {
      if (source.isPlaying && this.getVolumeMultiplier(source.id) === this.musicVolume) {
        if (fadeOut > 0) {
          // Implement fade out
          this.stop(source.id);
        } else {
          this.stop(source.id);
        }
      }
    }
  }

  update(deltaTime: number): void {
    // Update any time-based audio effects here
    // This method is called every frame
  }

  destroy(): void {
    this.stopAll();
    this.sources.clear();
    this.loadedBuffers.clear();

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
  }
}
