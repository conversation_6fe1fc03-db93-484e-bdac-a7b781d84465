/**
 * Safe operations wrapper for preventing common runtime errors
 */

import { <PERSON><PERSON>r<PERSON>and<PERSON> } from '@/utils/error/ErrorHandler';

export interface SafeResult<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface RetryConfig {
  maxAttempts: number;
  delay: number;
  backoffMultiplier: number;
  maxDelay: number;
}

export class SafeOperations {
  private static errorHandler = ErrorHandler.getInstance();

  // Safe array operations
  static safeArrayAccess<T>(array: T[], index: number, defaultValue?: T): T | undefined {
    try {
      if (!Array.isArray(array)) {
        this.errorHandler.warning('safeArrayAccess: Not an array', { array, index });
        return defaultValue;
      }

      if (index < 0 || index >= array.length) {
        return defaultValue;
      }

      return array[index];
    } catch (error) {
      this.errorHandler.error('safeArrayAccess failed', { error, array, index });
      return defaultValue;
    }
  }

  static safeArrayPush<T>(array: T[], item: T): boolean {
    try {
      if (!Array.isArray(array)) {
        this.errorHandler.warning('safeArrayPush: Not an array', { array, item });
        return false;
      }

      array.push(item);
      return true;
    } catch (error) {
      this.errorHandler.error('safeArrayPush failed', { error, array, item });
      return false;
    }
  }

  static safeArrayRemove<T>(array: T[], index: number): T | undefined {
    try {
      if (!Array.isArray(array)) {
        this.errorHandler.warning('safeArrayRemove: Not an array', { array, index });
        return undefined;
      }

      if (index < 0 || index >= array.length) {
        return undefined;
      }

      return array.splice(index, 1)[0];
    } catch (error) {
      this.errorHandler.error('safeArrayRemove failed', { error, array, index });
      return undefined;
    }
  }

  // Safe object operations
  static safePropertyAccess<T>(obj: unknown, path: string, defaultValue?: T): T | undefined {
    try {
      if (!obj || typeof obj !== 'object') {
        return defaultValue;
      }

      const keys = path.split('.');
      let current: unknown = obj;

      for (const key of keys) {
        if (current === null || current === undefined || typeof current !== 'object' || !(key in current)) {
          return defaultValue;
        }
        current = (current as Record<string, unknown>)[key];
      }

      return current as T;
    } catch (error) {
      this.errorHandler.error('safePropertyAccess failed', { error, obj, path });
      return defaultValue;
    }
  }

  static safePropertySet<T>(obj: Record<string, unknown>, path: string, value: T): boolean {
    try {
      if (!obj || typeof obj !== 'object') {
        this.errorHandler.warning('safePropertySet: Invalid object', { obj, path, value });
        return false;
      }

      const keys = path.split('.');
      let current: Record<string, unknown> = obj;

      for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (!(key in current) || typeof current[key] !== 'object') {
          current[key] = {};
        }
        current = current[key] as Record<string, unknown>;
      }

      current[keys[keys.length - 1]] = value;
      return true;
    } catch (error) {
      this.errorHandler.error('safePropertySet failed', { error, obj, path, value });
      return false;
    }
  }

  // Safe JSON operations
  static safeJSONParse<T>(json: string, defaultValue?: T): SafeResult<T> {
    try {
      const data = JSON.parse(json);
      return { success: true, data };
    } catch (error) {
      this.errorHandler.warning('safeJSONParse failed', { error, json: json.substring(0, 100) });
      return {
        success: false,
        data: defaultValue,
        error: error instanceof Error ? error.message : 'JSON parse error'
      };
    }
  }

  static safeJSONStringify(obj: any): SafeResult<string> {
    try {
      const json = JSON.stringify(obj);
      return { success: true, data: json };
    } catch (error) {
      this.errorHandler.error('safeJSONStringify failed', { error, obj });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'JSON stringify error'
      };
    }
  }

  // Safe localStorage operations
  static safeLocalStorageGet(key: string): SafeResult<string> {
    try {
      const value = localStorage.getItem(key);
      return { success: true, data: value || undefined };
    } catch (error) {
      this.errorHandler.error('safeLocalStorageGet failed', { error, key });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'localStorage get error'
      };
    }
  }

  static safeLocalStorageSet(key: string, value: string): SafeResult<void> {
    try {
      localStorage.setItem(key, value);
      return { success: true };
    } catch (error) {
      this.errorHandler.error('safeLocalStorageSet failed', { error, key, valueLength: value.length });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'localStorage set error'
      };
    }
  }

  static safeLocalStorageRemove(key: string): SafeResult<void> {
    try {
      localStorage.removeItem(key);
      return { success: true };
    } catch (error) {
      this.errorHandler.error('safeLocalStorageRemove failed', { error, key });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'localStorage remove error'
      };
    }
  }

  // Safe mathematical operations
  static safeDivide(a: number, b: number, defaultValue: number = 0): number {
    try {
      if (b === 0) {
        this.errorHandler.warning('safeDivide: Division by zero', { a, b });
        return defaultValue;
      }

      const result = a / b;
      if (!isFinite(result)) {
        this.errorHandler.warning('safeDivide: Non-finite result', { a, b, result });
        return defaultValue;
      }

      return result;
    } catch (error) {
      this.errorHandler.error('safeDivide failed', { error, a, b });
      return defaultValue;
    }
  }

  static safeSquareRoot(value: number, defaultValue: number = 0): number {
    try {
      if (value < 0) {
        this.errorHandler.warning('safeSquareRoot: Negative value', { value });
        return defaultValue;
      }

      return Math.sqrt(value);
    } catch (error) {
      this.errorHandler.error('safeSquareRoot failed', { error, value });
      return defaultValue;
    }
  }

  static safeClamp(value: number, min: number, max: number): number {
    try {
      if (min > max) {
        this.errorHandler.warning('safeClamp: min > max', { value, min, max });
        [min, max] = [max, min];
      }

      return Math.max(min, Math.min(max, value));
    } catch (error) {
      this.errorHandler.error('safeClamp failed', { error, value, min, max });
      return value;
    }
  }

  // Safe function execution with retry
  static async safeExecuteWithRetry<T>(
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {}
  ): Promise<SafeResult<T>> {
    const finalConfig: RetryConfig = {
      maxAttempts: 3,
      delay: 1000,
      backoffMultiplier: 2,
      maxDelay: 10000,
      ...config
    };

    let lastError: Error | null = null;
    let currentDelay = finalConfig.delay;

    for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
      try {
        const result = await operation();
        return { success: true, data: result };
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        this.errorHandler.warning(`Attempt ${attempt} failed`, {
          error: lastError.message,
          attempt,
          maxAttempts: finalConfig.maxAttempts
        });

        if (attempt < finalConfig.maxAttempts) {
          await this.delay(currentDelay);
          currentDelay = Math.min(currentDelay * finalConfig.backoffMultiplier, finalConfig.maxDelay);
        }
      }
    }

    this.errorHandler.error('safeExecuteWithRetry: All attempts failed', {
      error: lastError?.message,
      attempts: finalConfig.maxAttempts
    });

    return {
      success: false,
      error: lastError?.message || 'Operation failed after retries'
    };
  }

  // Safe async operation with timeout
  static async safeExecuteWithTimeout<T>(
    operation: () => Promise<T>,
    timeoutMs: number = 5000
  ): Promise<SafeResult<T>> {
    try {
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Operation timed out')), timeoutMs);
      });

      const result = await Promise.race([operation(), timeoutPromise]);
      return { success: true, data: result };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.errorHandler.error('safeExecuteWithTimeout failed', { error: errorMessage, timeoutMs });
      return { success: false, error: errorMessage };
    }
  }

  // Safe DOM operations
  static safeQuerySelector(selector: string): SafeResult<Element> {
    try {
      const element = document.querySelector(selector);
      if (!element) {
        return { success: false, error: `Element not found: ${selector}` };
      }
      return { success: true, data: element };
    } catch (error) {
      this.errorHandler.error('safeQuerySelector failed', { error, selector });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Query selector error'
      };
    }
  }

  static safeAddEventListener(
    element: Element | Window | Document,
    event: string,
    handler: EventListener,
    options?: AddEventListenerOptions
  ): SafeResult<() => void> {
    try {
      element.addEventListener(event, handler, options);

      // Return cleanup function
      const cleanup = () => {
        try {
          element.removeEventListener(event, handler, options);
        } catch (error) {
          this.errorHandler.warning('Event listener cleanup failed', { error, event });
        }
      };

      return { success: true, data: cleanup };
    } catch (error) {
      this.errorHandler.error('safeAddEventListener failed', { error, event });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Event listener error'
      };
    }
  }

  // Utility methods
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Safe type checking
  static isValidNumber(value: any): value is number {
    return typeof value === 'number' && !isNaN(value) && isFinite(value);
  }

  static isValidString(value: any): value is string {
    return typeof value === 'string';
  }

  static isValidArray(value: any): value is any[] {
    return Array.isArray(value);
  }

  static isValidObject(value: any): value is object {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
  }

  // Safe conversion methods
  static toNumber(value: unknown, defaultValue: number = 0): number {
    if (this.isValidNumber(value)) return value;

    const converted = Number(value);
    if (this.isValidNumber(converted)) return converted;

    this.errorHandler.warning('toNumber: Invalid conversion', { value, defaultValue });
    return defaultValue;
  }

  static toString(value: unknown, defaultValue: string = ''): string {
    if (this.isValidString(value)) return value;

    try {
      return String(value);
    } catch (error) {
      this.errorHandler.warning('toString: Conversion failed', { value, defaultValue, error });
      return defaultValue;
    }
  }
}
