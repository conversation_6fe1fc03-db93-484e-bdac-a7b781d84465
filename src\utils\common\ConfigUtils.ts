/**
 * Common configuration utility functions to reduce code duplication
 */

import { InputValidator } from '@/utils/validation/InputValidator';

export interface GameConfig {
  canvas: {
    width: number;
    height: number;
    targetFPS: number;
    pixelRatio: number;
  };
  debug: {
    showFPS: boolean;
    showColliders: boolean;
    logPerformance: boolean;
    enableConsole: boolean;
  };
  performance: {
    maxEntities: number;
    collisionBudget: number;
    renderBudget: number;
    updateBudget: number;
  };
  audio: {
    masterVolume: number;
    musicVolume: number;
    sfxVolume: number;
    enabled: boolean;
  };
  graphics: {
    particleCount: number;
    shadowQuality: 'low' | 'medium' | 'high';
    antiAliasing: boolean;
    vsync: boolean;
  };
}

export interface PlayerConfig {
  stats: {
    health: number;
    mana: number;
    speed: number;
    damage: number;
  };
  controls: {
    moveLeft: string;
    moveRight: string;
    moveUp: string;
    moveDown: string;
    shoot: string;
    special: string;
    interact: string;
    pause: string;
  };
  gameplay: {
    invulnerabilityTime: number;
    respawnTime: number;
    maxLives: number;
  };
}

export interface EnemyConfig {
  [enemyType: string]: {
    stats: {
      health: number;
      damage: number;
      speed: number;
      experienceValue: number;
    };
    ai: {
      type: 'aggressive' | 'defensive' | 'patrol' | 'ranged';
      detectionRange: number;
      attackRange: number;
      attackCooldown: number;
    };
    spawning: {
      weight: number;
      minLevel: number;
      maxLevel: number;
    };
  };
}

export class ConfigUtils {
  private static readonly DEFAULT_GAME_CONFIG: GameConfig = {
    canvas: {
      width: 1024,
      height: 768,
      targetFPS: 60,
      pixelRatio: window.devicePixelRatio || 1
    },
    debug: {
      showFPS: false,
      showColliders: false,
      logPerformance: false,
      enableConsole: process.env.NODE_ENV === 'development'
    },
    performance: {
      maxEntities: 1000,
      collisionBudget: 500,
      renderBudget: 16, // ms
      updateBudget: 16 // ms
    },
    audio: {
      masterVolume: 1.0,
      musicVolume: 0.7,
      sfxVolume: 0.8,
      enabled: true
    },
    graphics: {
      particleCount: 100,
      shadowQuality: 'medium',
      antiAliasing: true,
      vsync: true
    }
  };

  private static readonly DEFAULT_PLAYER_CONFIG: PlayerConfig = {
    stats: {
      health: 100,
      mana: 50,
      speed: 200,
      damage: 10
    },
    controls: {
      moveLeft: 'KeyA',
      moveRight: 'KeyD',
      moveUp: 'KeyW',
      moveDown: 'KeyS',
      shoot: 'Space',
      special: 'KeyQ',
      interact: 'KeyE',
      pause: 'Escape'
    },
    gameplay: {
      invulnerabilityTime: 1.5,
      respawnTime: 3.0,
      maxLives: 3
    }
  };

  private static readonly DEFAULT_ENEMY_CONFIG: EnemyConfig = {
    basic: {
      stats: {
        health: 30,
        damage: 5,
        speed: 80,
        experienceValue: 10
      },
      ai: {
        type: 'aggressive',
        detectionRange: 150,
        attackRange: 30,
        attackCooldown: 1.0
      },
      spawning: {
        weight: 1.0,
        minLevel: 1,
        maxLevel: 10
      }
    },
    shooter: {
      stats: {
        health: 20,
        damage: 8,
        speed: 60,
        experienceValue: 15
      },
      ai: {
        type: 'ranged',
        detectionRange: 200,
        attackRange: 150,
        attackCooldown: 2.0
      },
      spawning: {
        weight: 0.7,
        minLevel: 3,
        maxLevel: 15
      }
    },
    tank: {
      stats: {
        health: 80,
        damage: 15,
        speed: 40,
        experienceValue: 25
      },
      ai: {
        type: 'defensive',
        detectionRange: 100,
        attackRange: 40,
        attackCooldown: 3.0
      },
      spawning: {
        weight: 0.3,
        minLevel: 5,
        maxLevel: 20
      }
    }
  };

  /**
   * Load and validate game configuration
   */
  static loadGameConfig(userConfig?: Partial<GameConfig>): GameConfig {
    const config = this.mergeConfigs(this.DEFAULT_GAME_CONFIG, userConfig || {});
    
    // Validate configuration
    const validation = InputValidator.validateGameConfig(config);
    if (!validation.isValid) {
      console.warn('Invalid game configuration:', validation.errors);
      return this.DEFAULT_GAME_CONFIG;
    }
    
    return validation.sanitizedValue || config;
  }

  /**
   * Load and validate player configuration
   */
  static loadPlayerConfig(userConfig?: Partial<PlayerConfig>): PlayerConfig {
    const config = this.mergeConfigs(this.DEFAULT_PLAYER_CONFIG, userConfig || {});
    
    // Validate player stats
    if (config.stats.health <= 0 || config.stats.speed <= 0) {
      console.warn('Invalid player stats, using defaults');
      config.stats = { ...this.DEFAULT_PLAYER_CONFIG.stats };
    }
    
    // Validate control keys
    const controls = Object.values(config.controls);
    const uniqueControls = new Set(controls);
    if (controls.length !== uniqueControls.size) {
      console.warn('Duplicate control keys detected, using defaults');
      config.controls = { ...this.DEFAULT_PLAYER_CONFIG.controls };
    }
    
    return config;
  }

  /**
   * Load and validate enemy configuration
   */
  static loadEnemyConfig(userConfig?: Partial<EnemyConfig>): EnemyConfig {
    const config = this.mergeConfigs(this.DEFAULT_ENEMY_CONFIG, userConfig || {});
    
    // Validate each enemy type
    for (const [enemyType, enemyConfig] of Object.entries(config)) {
      if (enemyConfig.stats.health <= 0 || enemyConfig.stats.speed <= 0) {
        console.warn(`Invalid stats for enemy type ${enemyType}, using defaults`);
        config[enemyType] = { ...this.DEFAULT_ENEMY_CONFIG.basic };
      }
    }
    
    return config;
  }

  /**
   * Save configuration to localStorage
   */
  static saveConfig(key: string, config: any): void {
    try {
      localStorage.setItem(key, JSON.stringify(config));
    } catch (error) {
      console.warn('Failed to save configuration to localStorage:', error);
    }
  }

  /**
   * Load configuration from localStorage
   */
  static loadConfig<T>(key: string, defaultConfig: T): T {
    try {
      const stored = localStorage.getItem(key);
      if (stored) {
        const parsed = JSON.parse(stored);
        return this.mergeConfigs(defaultConfig, parsed);
      }
    } catch (error) {
      console.warn('Failed to load configuration from localStorage:', error);
    }
    
    return defaultConfig;
  }

  /**
   * Deep merge two configuration objects
   */
  private static mergeConfigs<T>(base: T, override: Partial<T>): T {
    const result = { ...base };
    
    for (const key in override) {
      const value = override[key];
      if (value !== undefined) {
        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          // Recursively merge objects
          (result as any)[key] = this.mergeConfigs((base as any)[key] || {}, value);
        } else {
          // Direct assignment for primitives and arrays
          (result as any)[key] = value;
        }
      }
    }
    
    return result;
  }

  /**
   * Get configuration for specific screen resolution
   */
  static getConfigForResolution(width: number, height: number): Partial<GameConfig> {
    const aspectRatio = width / height;
    const pixelCount = width * height;
    
    // Adjust performance settings based on resolution
    let performanceConfig: Partial<GameConfig['performance']> = {};
    
    if (pixelCount > 1920 * 1080) {
      // High resolution - reduce performance settings
      performanceConfig = {
        maxEntities: 500,
        collisionBudget: 300,
        renderBudget: 20
      };
    } else if (pixelCount < 1280 * 720) {
      // Low resolution - increase performance settings
      performanceConfig = {
        maxEntities: 1500,
        collisionBudget: 800,
        renderBudget: 12
      };
    }
    
    // Adjust graphics settings
    let graphicsConfig: Partial<GameConfig['graphics']> = {};
    
    if (pixelCount > 1920 * 1080) {
      graphicsConfig = {
        particleCount: 50,
        shadowQuality: 'low'
      };
    } else if (pixelCount < 1280 * 720) {
      graphicsConfig = {
        particleCount: 150,
        shadowQuality: 'high'
      };
    }
    
    return {
      canvas: {
        width,
        height,
        targetFPS: 60,
        pixelRatio: window.devicePixelRatio || 1
      },
      performance: performanceConfig,
      graphics: graphicsConfig
    };
  }

  /**
   * Detect optimal configuration based on device capabilities
   */
  static detectOptimalConfig(): Partial<GameConfig> {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    
    // Basic capability detection
    const hasWebGL = !!gl;
    const deviceMemory = (navigator as any).deviceMemory || 4; // GB
    const hardwareConcurrency = navigator.hardwareConcurrency || 4;
    
    let config: Partial<GameConfig> = {};
    
    // Adjust based on device capabilities
    if (deviceMemory < 4 || hardwareConcurrency < 4) {
      // Low-end device
      config = {
        performance: {
          maxEntities: 300,
          collisionBudget: 200,
          renderBudget: 20
        },
        graphics: {
          particleCount: 50,
          shadowQuality: 'low',
          antiAliasing: false
        }
      };
    } else if (deviceMemory >= 8 && hardwareConcurrency >= 8) {
      // High-end device
      config = {
        performance: {
          maxEntities: 2000,
          collisionBudget: 1000,
          renderBudget: 12
        },
        graphics: {
          particleCount: 200,
          shadowQuality: 'high',
          antiAliasing: true
        }
      };
    }
    
    return config;
  }

  /**
   * Validate configuration values
   */
  static validateConfig(config: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Validate canvas settings
    if (config.canvas) {
      if (config.canvas.width < 320 || config.canvas.width > 4096) {
        errors.push('Canvas width must be between 320 and 4096');
      }
      if (config.canvas.height < 240 || config.canvas.height > 2160) {
        errors.push('Canvas height must be between 240 and 2160');
      }
      if (config.canvas.targetFPS < 30 || config.canvas.targetFPS > 120) {
        errors.push('Target FPS must be between 30 and 120');
      }
    }
    
    // Validate performance settings
    if (config.performance) {
      if (config.performance.maxEntities < 10 || config.performance.maxEntities > 5000) {
        errors.push('Max entities must be between 10 and 5000');
      }
    }
    
    // Validate audio settings
    if (config.audio) {
      if (config.audio.masterVolume < 0 || config.audio.masterVolume > 1) {
        errors.push('Master volume must be between 0 and 1');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get default configurations
   */
  static getDefaults(): {
    game: GameConfig;
    player: PlayerConfig;
    enemies: EnemyConfig;
  } {
    return {
      game: { ...this.DEFAULT_GAME_CONFIG },
      player: { ...this.DEFAULT_PLAYER_CONFIG },
      enemies: { ...this.DEFAULT_ENEMY_CONFIG }
    };
  }
}
