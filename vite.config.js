import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  // Base configuration
  base: './',
  
  // Build configuration
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: process.env.NODE_ENV === 'development',
    minify: 'terser',
    target: 'es2020',
    
    // Rollup options
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html'),
        demo: resolve(__dirname, 'demo.html'),
        'dungeon-demo': resolve(__dirname, 'dungeon-demo.html'),
        'complete-demo': resolve(__dirname, 'complete-demo.html'),
        'ultimate-demo': resolve(__dirname, 'ultimate-demo.html'),
        'map-editor': resolve(__dirname, 'map-editor.html')
      },
      output: {
        manualChunks: {
          // Vendor chunks
          'vendor-core': ['typescript'],
          
          // Engine chunks
          'engine-core': [
            './src/engine/core/Game.ts',
            './src/engine/core/SceneManager.ts',
            './src/engine/core/AssetManager.ts'
          ],
          'engine-physics': [
            './src/engine/physics/CollisionSystem.ts',
            './src/engine/physics/SpatialGrid.ts'
          ],
          'engine-rendering': [
            './src/engine/rendering/CanvasRenderer.ts'
          ],
          'engine-input': [
            './src/engine/input/InputManager.ts'
          ],
          'engine-audio': [
            './src/engine/audio/AudioManager.ts'
          ],
          
          // Game chunks
          'game-entities': [
            './src/game/entities/Player.ts',
            './src/game/entities/Enemy.ts',
            './src/game/entities/Bullet.ts',
            './src/game/entities/Particle.ts'
          ],
          'game-systems': [
            './src/game/systems/ProgressionSystem.ts',
            './src/game/systems/InventorySystem.ts',
            './src/game/systems/LootSystem.ts'
          ],
          'game-scenes': [
            './src/game/scenes/MenuScene.ts',
            './src/game/scenes/GameScene.ts'
          ],
          
          // Utilities
          'utils': [
            './src/utils/math/Vector2.ts',
            './src/utils/error/SimpleErrorHandler.ts'
          ]
        }
      }
    },
    
    // Terser options for minification
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === 'production',
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.debug']
      },
      mangle: {
        properties: false
      }
    },
    
    // Asset optimization
    assetsInlineLimit: 4096, // 4kb
    chunkSizeWarningLimit: 1000, // 1MB
    
    // CSS code splitting
    cssCodeSplit: true
  },
  
  // Development server
  server: {
    port: 3000,
    host: true,
    open: true,
    cors: true,
    
    // Proxy for API calls if needed
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  
  // Preview server (for production builds)
  preview: {
    port: 4173,
    host: true,
    open: true
  },
  
  // Path resolution
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@assets': resolve(__dirname, './assets'),
      '@tests': resolve(__dirname, './tests')
    },
    extensions: ['.ts', '.js', '.json']
  },
  
  // Plugin configuration
  plugins: [
    // HTML processing
    {
      name: 'html-transform',
      transformIndexHtml: {
        enforce: 'pre',
        transform(html, context) {
          // Add meta tags for production
          if (process.env.NODE_ENV === 'production') {
            return html.replace(
              '<head>',
              `<head>
    <meta name="description" content="Ultimate Bullet Hell RPG - A comprehensive bullet hell rogue-like RPG built with TypeScript and HTML5 Canvas">
    <meta name="keywords" content="game, bullet-hell, rpg, rogue-like, typescript, canvas, html5">
    <meta name="author" content="Ultimate Game Developer">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#000000">`
            );
          }
          return html;
        }
      }
    },
    
    // Asset optimization
    {
      name: 'asset-optimization',
      generateBundle(options, bundle) {
        // Log bundle information
        const chunks = Object.values(bundle).filter(chunk => chunk.type === 'chunk');
        const assets = Object.values(bundle).filter(asset => asset.type === 'asset');
        
        console.log(`\n📦 Bundle Summary:`);
        console.log(`   Chunks: ${chunks.length}`);
        console.log(`   Assets: ${assets.length}`);
        
        // Warn about large chunks
        chunks.forEach(chunk => {
          const sizeKB = (chunk.code?.length || 0) / 1024;
          if (sizeKB > 500) {
            console.warn(`⚠️  Large chunk detected: ${chunk.fileName} (${sizeKB.toFixed(1)}KB)`);
          }
        });
      }
    }
  ],
  
  // Optimization
  optimizeDeps: {
    include: [
      // Pre-bundle these dependencies
    ],
    exclude: [
      // Don't pre-bundle these
    ]
  },
  
  // CSS configuration
  css: {
    devSourcemap: true,
    preprocessorOptions: {
      // Add global CSS variables if needed
    }
  },
  
  // Environment variables
  define: {
    __DEV__: process.env.NODE_ENV === 'development',
    __PROD__: process.env.NODE_ENV === 'production',
    __VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
    __BUILD_TIME__: JSON.stringify(new Date().toISOString())
  },
  
  // Worker configuration
  worker: {
    format: 'es'
  },
  
  // Experimental features
  experimental: {
    renderBuiltUrl(filename, { hostType }) {
      if (hostType === 'js') {
        return { js: `new URL('${filename}', import.meta.url).href` };
      } else {
        return { relative: true };
      }
    }
  }
});
