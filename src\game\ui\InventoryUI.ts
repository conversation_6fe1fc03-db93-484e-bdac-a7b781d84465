/**
 * Inventory user interface for item management
 */

import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';
import { InputManager } from '@/engine/input/InputManager';
import { Vector2 } from '@/utils/math/Vector2';
import { InventorySystem, InventorySlot, EquipmentSlots } from '@/game/systems/InventorySystem';
import { Item, LootSystem } from '@/game/systems/LootSystem';

export interface InventoryUIConfig {
  slotSize: number;
  slotSpacing: number;
  slotsPerRow: number;
  colors: {
    background: string;
    slotEmpty: string;
    slotFilled: string;
    slotHover: string;
    slotSelected: string;
    text: string;
    gold: string;
    rarity: { [key: string]: string };
  };
}

export class InventoryUI {
  private config: InventoryUIConfig;
  private inventorySystem: InventorySystem;
  private lootSystem: LootSystem;
  private isVisible: boolean = false;
  private hoveredSlot: number = -1;
  private selectedSlot: number = -1;
  private draggedItem: { item: Item; fromSlot: number } | null = null;
  private showEquipment: boolean = true;

  constructor(
    inventorySystem: InventorySystem,
    lootSystem: LootSystem,
    config: Partial<InventoryUIConfig> = {}
  ) {
    this.inventorySystem = inventorySystem;
    this.lootSystem = lootSystem;
    this.config = {
      slotSize: 50,
      slotSpacing: 55,
      slotsPerRow: 6,
      colors: {
        background: 'rgba(0, 0, 0, 0.9)',
        slotEmpty: '#333333',
        slotFilled: '#555555',
        slotHover: '#777777',
        slotSelected: '#999999',
        text: '#ffffff',
        gold: '#FFD700',
        rarity: {
          common: '#ffffff',
          uncommon: '#1eff00',
          rare: '#0070dd',
          epic: '#a335ee',
          legendary: '#ff8000'
        }
      },
      ...config
    };
  }

  show(): void {
    this.isVisible = true;
  }

  hide(): void {
    this.isVisible = false;
    this.selectedSlot = -1;
    this.draggedItem = null;
  }

  toggle(): void {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }

  update(deltaTime: number, inputManager: InputManager): void {
    if (!this.isVisible) return;

    this.handleInput(inputManager);
  }

  private handleInput(inputManager: InputManager): void {
    const mousePos = inputManager.getMousePosition();
    
    // Update hovered slot
    this.hoveredSlot = this.getSlotAtPosition(mousePos);

    // Handle mouse clicks
    if (inputManager.isMouseButtonPressed(0)) {
      this.handleLeftClick(mousePos);
    } else if (inputManager.isMouseButtonPressed(2)) {
      this.handleRightClick(mousePos);
    }

    // Handle keyboard shortcuts
    if (inputManager.isKeyPressed('KeyS')) {
      this.inventorySystem.sortInventory();
    }

    if (inputManager.isKeyPressed('KeyE')) {
      this.showEquipment = !this.showEquipment;
    }
  }

  private handleLeftClick(mousePos: Vector2): void {
    const slotIndex = this.getSlotAtPosition(mousePos);
    
    if (slotIndex >= 0) {
      const item = this.inventorySystem.getItemAtSlot(slotIndex);
      
      if (this.selectedSlot >= 0 && this.selectedSlot !== slotIndex) {
        // Move item
        this.inventorySystem.moveItem(this.selectedSlot, slotIndex);
        this.selectedSlot = -1;
      } else if (item) {
        // Select slot
        this.selectedSlot = slotIndex;
      } else {
        this.selectedSlot = -1;
      }
    } else {
      // Check equipment slots
      const equipSlot = this.getEquipmentSlotAtPosition(mousePos);
      if (equipSlot) {
        this.handleEquipmentClick(equipSlot);
      } else {
        this.selectedSlot = -1;
      }
    }
  }

  private handleRightClick(mousePos: Vector2): void {
    const slotIndex = this.getSlotAtPosition(mousePos);
    
    if (slotIndex >= 0) {
      const item = this.inventorySystem.getItemAtSlot(slotIndex);
      
      if (item) {
        if (item.type === 'consumable') {
          // Use consumable
          this.inventorySystem.useItem(slotIndex);
        } else if (item.type === 'weapon' || item.type === 'armor' || item.type === 'accessory') {
          // Equip item
          this.inventorySystem.equipItem(slotIndex);
        }
      }
    }
  }

  private handleEquipmentClick(equipSlot: keyof EquipmentSlots): void {
    this.inventorySystem.unequipItem(equipSlot);
  }

  private getSlotAtPosition(mousePos: Vector2): number {
    const startX = 50;
    const startY = 150;
    const maxSlots = this.inventorySystem.getMaxSlots();
    
    for (let i = 0; i < maxSlots; i++) {
      const row = Math.floor(i / this.config.slotsPerRow);
      const col = i % this.config.slotsPerRow;
      
      const slotX = startX + col * this.config.slotSpacing;
      const slotY = startY + row * this.config.slotSpacing;
      
      if (mousePos.x >= slotX && mousePos.x <= slotX + this.config.slotSize &&
          mousePos.y >= slotY && mousePos.y <= slotY + this.config.slotSize) {
        return i;
      }
    }
    
    return -1;
  }

  private getEquipmentSlotAtPosition(mousePos: Vector2): keyof EquipmentSlots | null {
    if (!this.showEquipment) return null;

    const equipmentSlots = [
      { name: 'weapon' as keyof EquipmentSlots, x: 500, y: 200 },
      { name: 'helmet' as keyof EquipmentSlots, x: 600, y: 150 },
      { name: 'chest' as keyof EquipmentSlots, x: 600, y: 200 },
      { name: 'legs' as keyof EquipmentSlots, x: 600, y: 250 },
      { name: 'boots' as keyof EquipmentSlots, x: 600, y: 300 },
      { name: 'gloves' as keyof EquipmentSlots, x: 550, y: 200 },
      { name: 'ring1' as keyof EquipmentSlots, x: 650, y: 180 },
      { name: 'ring2' as keyof EquipmentSlots, x: 650, y: 220 },
      { name: 'amulet' as keyof EquipmentSlots, x: 600, y: 100 }
    ];

    for (const slot of equipmentSlots) {
      if (mousePos.x >= slot.x && mousePos.x <= slot.x + this.config.slotSize &&
          mousePos.y >= slot.y && mousePos.y <= slot.y + this.config.slotSize) {
        return slot.name;
      }
    }

    return null;
  }

  render(renderer: CanvasRenderer): void {
    if (!this.isVisible) return;

    const { width, height } = renderer.getCanvasSize();

    // Draw background
    renderer.setGlobalAlpha(0.95);
    renderer.drawRect(0, 0, width, height, this.config.colors.background);
    renderer.setGlobalAlpha(1);

    // Draw header
    this.renderHeader(renderer);

    // Draw inventory grid
    this.renderInventoryGrid(renderer);

    // Draw equipment panel
    if (this.showEquipment) {
      this.renderEquipmentPanel(renderer);
    }

    // Draw item tooltip
    if (this.hoveredSlot >= 0) {
      this.renderItemTooltip(renderer, this.hoveredSlot);
    }
  }

  private renderHeader(renderer: CanvasRenderer): void {
    const { width } = renderer.getCanvasSize();
    
    // Title
    renderer.drawText(
      'Inventory',
      width / 2 - 50,
      30,
      'bold 24px monospace',
      this.config.colors.text
    );

    // Gold
    const goldText = `Gold: ${this.inventorySystem.getGold()}`;
    renderer.drawText(goldText, 20, 60, '18px monospace', this.config.colors.gold);

    // Inventory info
    const usedSlots = this.inventorySystem.getUsedSlots();
    const maxSlots = this.inventorySystem.getMaxSlots();
    const slotsText = `Slots: ${usedSlots}/${maxSlots}`;
    renderer.drawText(slotsText, 20, 85, '16px monospace', this.config.colors.text);

    // Controls
    renderer.drawText('Left Click: Select/Move | Right Click: Use/Equip | S: Sort | E: Toggle Equipment', 
      20, 110, '12px monospace', '#888888');
  }

  private renderInventoryGrid(renderer: CanvasRenderer): void {
    const slots = this.inventorySystem.getInventorySlots();
    const startX = 50;
    const startY = 150;

    for (let i = 0; i < slots.length; i++) {
      const slot = slots[i];
      const row = Math.floor(i / this.config.slotsPerRow);
      const col = i % this.config.slotsPerRow;
      
      const slotX = startX + col * this.config.slotSpacing;
      const slotY = startY + row * this.config.slotSpacing;
      
      this.renderInventorySlot(renderer, slot, slotX, slotY, i);
    }
  }

  private renderInventorySlot(
    renderer: CanvasRenderer,
    slot: InventorySlot,
    x: number,
    y: number,
    index: number
  ): void {
    // Determine slot color
    let slotColor = this.config.colors.slotEmpty;
    if (slot.item) {
      slotColor = this.config.colors.slotFilled;
    }
    if (index === this.hoveredSlot) {
      slotColor = this.config.colors.slotHover;
    }
    if (index === this.selectedSlot) {
      slotColor = this.config.colors.slotSelected;
    }

    // Draw slot background
    renderer.drawRect(x, y, this.config.slotSize, this.config.slotSize, slotColor);
    
    // Draw slot border
    renderer.drawRectOutline(x, y, this.config.slotSize, this.config.slotSize, '#666666', 2);

    // Draw item if present
    if (slot.item) {
      this.renderItem(renderer, slot.item, x, y, slot.quantity);
    }
  }

  private renderItem(renderer: CanvasRenderer, item: Item, x: number, y: number, quantity: number): void {
    // Draw rarity border
    const rarityColor = this.config.colors.rarity[item.rarity] || this.config.colors.text;
    renderer.drawRectOutline(x + 2, y + 2, this.config.slotSize - 4, this.config.slotSize - 4, rarityColor, 2);

    // Draw item icon
    renderer.drawText(
      item.icon,
      x + this.config.slotSize / 2 - 12,
      y + this.config.slotSize / 2 - 12,
      '24px monospace',
      this.config.colors.text
    );

    // Draw quantity if stackable
    if (item.stackSize > 1 && quantity > 1) {
      renderer.drawText(
        quantity.toString(),
        x + this.config.slotSize - 15,
        y + this.config.slotSize - 15,
        '12px monospace',
        this.config.colors.text
      );
    }
  }

  private renderEquipmentPanel(renderer: CanvasRenderer): void {
    const equipment = this.inventorySystem.getEquipment();
    const panelX = 450;
    const panelY = 50;
    const panelWidth = 300;
    const panelHeight = 400;

    // Draw panel background
    renderer.setGlobalAlpha(0.8);
    renderer.drawRect(panelX, panelY, panelWidth, panelHeight, '#222222');
    renderer.setGlobalAlpha(1);
    
    // Draw panel border
    renderer.drawRectOutline(panelX, panelY, panelWidth, panelHeight, '#666666', 2);

    // Draw title
    renderer.drawText('Equipment', panelX + 10, panelY + 25, 'bold 18px monospace', this.config.colors.text);

    // Draw equipment slots
    const equipmentSlots = [
      { name: 'weapon', x: 500, y: 200, label: 'Weapon' },
      { name: 'helmet', x: 600, y: 150, label: 'Helmet' },
      { name: 'chest', x: 600, y: 200, label: 'Chest' },
      { name: 'legs', x: 600, y: 250, label: 'Legs' },
      { name: 'boots', x: 600, y: 300, label: 'Boots' },
      { name: 'gloves', x: 550, y: 200, label: 'Gloves' },
      { name: 'ring1', x: 650, y: 180, label: 'Ring 1' },
      { name: 'ring2', x: 650, y: 220, label: 'Ring 2' },
      { name: 'amulet', x: 600, y: 100, label: 'Amulet' }
    ];

    for (const slot of equipmentSlots) {
      const item = equipment[slot.name as keyof EquipmentSlots];
      
      // Draw slot
      const slotColor = item ? this.config.colors.slotFilled : this.config.colors.slotEmpty;
      renderer.drawRect(slot.x, slot.y, this.config.slotSize, this.config.slotSize, slotColor);
      renderer.drawRectOutline(slot.x, slot.y, this.config.slotSize, this.config.slotSize, '#666666', 2);

      // Draw item if equipped
      if (item) {
        this.renderItem(renderer, item, slot.x, slot.y, 1);
      }

      // Draw slot label
      renderer.drawText(
        slot.label,
        slot.x - 10,
        slot.y - 15,
        '10px monospace',
        '#888888'
      );
    }
  }

  private renderItemTooltip(renderer: CanvasRenderer, slotIndex: number): void {
    const item = this.inventorySystem.getItemAtSlot(slotIndex);
    if (!item) return;

    const mousePos = new Vector2(400, 300); // Fixed position for now
    const tooltipLines = this.lootSystem.getItemTooltip(item);
    const lineHeight = 16;
    const padding = 10;
    const tooltipWidth = 250;
    const tooltipHeight = tooltipLines.length * lineHeight + padding * 2;

    // Background
    renderer.setGlobalAlpha(0.95);
    renderer.drawRect(mousePos.x, mousePos.y, tooltipWidth, tooltipHeight, '#222222');
    renderer.setGlobalAlpha(1);

    // Border
    const rarityColor = this.config.colors.rarity[item.rarity] || this.config.colors.text;
    renderer.drawRectOutline(mousePos.x, mousePos.y, tooltipWidth, tooltipHeight, rarityColor, 2);

    // Text
    let y = mousePos.y + padding + lineHeight;
    for (let i = 0; i < tooltipLines.length; i++) {
      const line = tooltipLines[i];
      let color = this.config.colors.text;
      
      if (i === 0) {
        // Item name
        color = rarityColor;
      } else if (line.startsWith('•')) {
        // Modifier
        color = '#4CAF50';
      } else if (line.includes('Value:')) {
        // Value
        color = this.config.colors.gold;
      }
      
      renderer.drawText(line, mousePos.x + padding, y, '12px monospace', color);
      y += lineHeight;
    }
  }

  // Getters
  isOpen(): boolean {
    return this.isVisible;
  }

  getSelectedSlot(): number {
    return this.selectedSlot;
  }
}
