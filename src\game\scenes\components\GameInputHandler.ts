/**
 * Input handling component for game scenes
 * Centralizes input processing and validation
 */

import { InputManager } from '@/engine/input/InputManager';
import { Player } from '@/game/entities/Player';
import { Bullet } from '@/game/entities/Bullet';
import { Vector2 } from '@/utils/math/Vector2';
import { InputValidator } from '@/utils/validation/InputValidator';

export interface InputState {
  movement: Vector2;
  shooting: boolean;
  showSkillTree: boolean;
  showInventory: boolean;
  mouseWorldPosition: Vector2;
}

export interface InputCallbacks {
  onBulletCreated: (bullet: Bullet) => void;
  onToggleSkillTree: () => void;
  onToggleInventory: () => void;
  onPause: () => void;
}

export class GameInputHandler {
  private inputManager: InputManager;
  private player: Player;
  private camera: { x: number; y: number };
  private callbacks: InputCallbacks;
  private inputState: InputState;

  // Input validation and rate limiting
  private lastShootTime: number = 0;
  private shootCooldown: number = 100; // ms
  private lastToggleTime: number = 0;
  private toggleCooldown: number = 200; // ms

  constructor(
    inputManager: InputManager,
    player: Player,
    camera: { x: number; y: number },
    callbacks: InputCallbacks
  ) {
    this.inputManager = inputManager;
    this.player = player;
    this.camera = camera;
    this.callbacks = callbacks;
    
    this.inputState = {
      movement: new Vector2(),
      shooting: false,
      showSkillTree: false,
      showInventory: false,
      mouseWorldPosition: new Vector2()
    };
  }

  /**
   * Process all input for the current frame
   */
  processInput(deltaTime: number): InputState {
    const currentTime = performance.now();

    this.processMovementInput();
    this.processShootingInput(currentTime);
    this.processUIInput(currentTime);
    this.processMouseInput();

    return { ...this.inputState };
  }

  /**
   * Process movement input with validation
   */
  private processMovementInput(): void {
    const movement = new Vector2();

    // Keyboard movement
    if (this.inputManager.isActionDown('moveLeft')) {
      movement.x -= 1;
    }
    if (this.inputManager.isActionDown('moveRight')) {
      movement.x += 1;
    }
    if (this.inputManager.isActionDown('moveUp')) {
      movement.y -= 1;
    }
    if (this.inputManager.isActionDown('moveDown')) {
      movement.y += 1;
    }

    // Validate and sanitize movement input
    const validationResult = InputValidator.validatePlayerInput({
      movement: { x: movement.x, y: movement.y },
      actions: {}
    });

    if (validationResult.isValid && validationResult.sanitizedValue) {
      this.inputState.movement.set(
        validationResult.sanitizedValue.movement.x,
        validationResult.sanitizedValue.movement.y
      );
    } else {
      this.inputState.movement.set(0, 0);
    }

    // Normalize diagonal movement
    if (this.inputState.movement.magnitude() > 1) {
      this.inputState.movement.normalize();
    }

    // Apply movement to player
    this.player.setMovement(this.inputState.movement);
  }

  /**
   * Process shooting input with rate limiting
   */
  private processShootingInput(currentTime: number): void {
    const isShooting = this.inputManager.isMouseButtonDown(0) || 
                     this.inputManager.isActionDown('shoot');

    this.inputState.shooting = isShooting;

    if (isShooting && currentTime - this.lastShootTime >= this.shootCooldown) {
      this.handleShooting();
      this.lastShootTime = currentTime;
    }
  }

  /**
   * Handle shooting logic
   */
  private handleShooting(): void {
    if (!this.player.isAlive) return;

    // Calculate shooting direction
    const shootDirection = this.calculateShootDirection();
    
    if (shootDirection.magnitude() === 0) {
      // Default to right if no direction specified
      shootDirection.set(1, 0);
    }

    // Create bullet
    const bulletSpeed = 500;
    const bulletVelocity = shootDirection.normalize().multiply(bulletSpeed);
    
    const bullet = new Bullet(
      this.player.position.copy(),
      bulletVelocity,
      this.player.stats.damage,
      'player'
    );

    this.callbacks.onBulletCreated(bullet);
  }

  /**
   * Calculate shooting direction based on input method
   */
  private calculateShootDirection(): Vector2 {
    // Use mouse position if available
    if (this.inputManager.getMousePosition()) {
      const mousePos = this.inputManager.getMousePosition();
      const worldMousePos = new Vector2(
        mousePos.x + this.camera.x,
        mousePos.y + this.camera.y
      );
      
      return worldMousePos.subtract(this.player.position);
    }

    // Fallback to keyboard direction
    const direction = new Vector2();
    
    if (this.inputManager.isActionDown('shootLeft')) direction.x -= 1;
    if (this.inputManager.isActionDown('shootRight')) direction.x += 1;
    if (this.inputManager.isActionDown('shootUp')) direction.y -= 1;
    if (this.inputManager.isActionDown('shootDown')) direction.y += 1;

    return direction;
  }

  /**
   * Process UI input with debouncing
   */
  private processUIInput(currentTime: number): void {
    if (currentTime - this.lastToggleTime < this.toggleCooldown) {
      return;
    }

    // Toggle skill tree
    if (this.inputManager.isKeyPressed('KeyT')) {
      this.inputState.showSkillTree = !this.inputState.showSkillTree;
      this.callbacks.onToggleSkillTree();
      this.lastToggleTime = currentTime;
    }

    // Toggle inventory
    if (this.inputManager.isKeyPressed('KeyI')) {
      this.inputState.showInventory = !this.inputState.showInventory;
      this.callbacks.onToggleInventory();
      this.lastToggleTime = currentTime;
    }

    // Pause game
    if (this.inputManager.isKeyPressed('Escape')) {
      this.callbacks.onPause();
      this.lastToggleTime = currentTime;
    }
  }

  /**
   * Process mouse input and update world position
   */
  private processMouseInput(): void {
    const mousePos = this.inputManager.getMousePosition();
    if (mousePos) {
      this.inputState.mouseWorldPosition.set(
        mousePos.x + this.camera.x,
        mousePos.y + this.camera.y
      );
    }
  }

  /**
   * Handle special abilities
   */
  processSpecialAbilities(): void {
    if (this.inputManager.isKeyPressed('Space')) {
      this.player.useSpecialAbility();
    }

    // Weapon switching
    for (let i = 1; i <= 6; i++) {
      if (this.inputManager.isKeyPressed(`Digit${i}`)) {
        this.switchWeapon(i - 1);
        break;
      }
    }
  }

  /**
   * Switch player weapon
   */
  private switchWeapon(weaponIndex: number): void {
    // Validate weapon index
    const validationResult = InputValidator.validateNumber(weaponIndex, 0, 5);
    
    if (validationResult.isValid && validationResult.sanitizedValue !== undefined) {
      // TODO: Implement weapon switching logic
      console.log(`Switching to weapon ${validationResult.sanitizedValue}`);
    }
  }

  /**
   * Update camera reference
   */
  updateCamera(camera: { x: number; y: number }): void {
    this.camera = camera;
  }

  /**
   * Get current input state
   */
  getInputState(): Readonly<InputState> {
    return this.inputState;
  }

  /**
   * Set shooting cooldown (for different weapons)
   */
  setShootCooldown(cooldown: number): void {
    const validationResult = InputValidator.validateNumber(cooldown, 50, 2000);
    if (validationResult.isValid && validationResult.sanitizedValue !== undefined) {
      this.shootCooldown = validationResult.sanitizedValue;
    }
  }

  /**
   * Enable/disable input processing
   */
  private inputEnabled: boolean = true;

  setInputEnabled(enabled: boolean): void {
    this.inputEnabled = enabled;
    
    if (!enabled) {
      // Clear current input state
      this.inputState.movement.set(0, 0);
      this.inputState.shooting = false;
      this.player.setMovement(new Vector2(0, 0));
    }
  }

  /**
   * Check if input is currently enabled
   */
  isInputEnabled(): boolean {
    return this.inputEnabled;
  }

  /**
   * Handle touch/mobile input
   */
  processTouchInput(): void {
    // TODO: Implement touch input handling for mobile devices
    // This would integrate with the virtual controls system
  }

  /**
   * Get input statistics for debugging
   */
  getInputStats(): {
    lastShootTime: number;
    lastToggleTime: number;
    inputEnabled: boolean;
    currentMovement: Vector2;
  } {
    return {
      lastShootTime: this.lastShootTime,
      lastToggleTime: this.lastToggleTime,
      inputEnabled: this.inputEnabled,
      currentMovement: this.inputState.movement.copy()
    };
  }
}
