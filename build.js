#!/usr/bin/env node

/**
 * Production build script for Ultimate Bullet Hell RPG
 * Optimizes assets, minifies code, and creates production-ready distribution
 */

const fs = require('fs').promises;
const path = require('path');

class ProductionBuilder {
  constructor() {
    this.config = {
      sourceDir: '.',
      outputDir: './dist',
      minifyHTML: true,
      minifyJS: true,
      minifyCSS: true,
      optimizeImages: true,
      generateSourceMaps: false,
      compressionLevel: 9
    };
    
    this.stats = {
      filesProcessed: 0,
      originalSize: 0,
      optimizedSize: 0,
      startTime: Date.now()
    };
  }

  async build() {
    console.log('🚀 Starting production build...');
    console.log('================================');
    
    try {
      // Clean output directory
      await this.cleanOutputDir();
      
      // Create output directory structure
      await this.createOutputStructure();
      
      // Process HTML files
      await this.processHTMLFiles();
      
      // Process and bundle JavaScript/TypeScript
      await this.processJavaScriptFiles();
      
      // Process CSS
      await this.processCSSFiles();
      
      // Copy and optimize assets
      await this.copyAssets();
      
      // Generate service worker for caching
      await this.generateServiceWorker();
      
      // Create production manifest
      await this.createManifest();
      
      // Generate build report
      this.generateBuildReport();
      
      console.log('✅ Production build completed successfully!');
      
    } catch (error) {
      console.error('❌ Build failed:', error);
      process.exit(1);
    }
  }

  async cleanOutputDir() {
    console.log('🧹 Cleaning output directory...');
    
    try {
      await fs.rmdir(this.config.outputDir, { recursive: true });
    } catch (error) {
      // Directory might not exist, which is fine
    }
    
    await fs.mkdir(this.config.outputDir, { recursive: true });
  }

  async createOutputStructure() {
    console.log('📁 Creating output structure...');
    
    const dirs = [
      'src/engine',
      'src/game',
      'src/utils',
      'src/tools',
      'assets',
      'build'
    ];
    
    for (const dir of dirs) {
      await fs.mkdir(path.join(this.config.outputDir, dir), { recursive: true });
    }
  }

  async processHTMLFiles() {
    console.log('📄 Processing HTML files...');
    
    const htmlFiles = [
      'demo.html',
      'dungeon-demo.html', 
      'complete-demo.html',
      'ultimate-demo.html',
      'map-editor.html'
    ];
    
    for (const file of htmlFiles) {
      try {
        const content = await fs.readFile(file, 'utf8');
        const originalSize = content.length;
        
        let optimized = content;
        
        if (this.config.minifyHTML) {
          optimized = this.minifyHTML(optimized);
        }
        
        // Optimize script loading
        optimized = this.optimizeScriptLoading(optimized);
        
        // Update paths for production
        optimized = this.updateProductionPaths(optimized);
        
        // Inline critical CSS
        optimized = await this.inlineCriticalCSS(optimized);
        
        const outputPath = path.join(this.config.outputDir, file);
        await fs.writeFile(outputPath, optimized);
        
        this.updateStats(originalSize, optimized.length);
        console.log(`  ✓ ${file} (${this.formatBytes(originalSize)} → ${this.formatBytes(optimized.length)})`);
        
      } catch (error) {
        console.error(`  ❌ Failed to process ${file}:`, error.message);
      }
    }
  }

  async processJavaScriptFiles() {
    console.log('📜 Processing JavaScript/TypeScript files...');
    
    // Create optimized bundles for main entry points
    const entryPoints = [
      { input: 'src/game/UltimateGame.ts', output: 'ultimate-game.bundle.js' },
      { input: 'src/tools/MapEditor.ts', output: 'map-editor.bundle.js' },
      { input: 'src/engine/core/Game.ts', output: 'game.bundle.js' }
    ];
    
    for (const entry of entryPoints) {
      try {
        const bundle = await this.createBundle(entry.input);
        const outputPath = path.join(this.config.outputDir, entry.output);
        await fs.writeFile(outputPath, bundle);
        
        console.log(`  ✓ ${entry.output} (${this.formatBytes(bundle.length)})`);
        
      } catch (error) {
        console.error(`  ❌ Failed to bundle ${entry.input}:`, error.message);
      }
    }
    
    // Copy individual TypeScript files (converted to JS)
    await this.copyTypeScriptFiles();
  }

  async createBundle(entryPoint) {
    // Simplified bundling - resolve dependencies and concatenate
    const dependencies = await this.resolveDependencies(entryPoint);
    let bundle = '';
    
    // Add module system
    bundle += this.getModuleSystemCode();
    
    // Add all dependencies
    for (const dep of dependencies) {
      try {
        const content = await fs.readFile(dep, 'utf8');
        const compiled = this.compileTypeScript(content);
        const wrapped = this.wrapModule(dep, compiled);
        bundle += wrapped + '\n';
      } catch (error) {
        console.warn(`    Warning: Could not include ${dep}`);
      }
    }
    
    // Minify if enabled
    if (this.config.minifyJS) {
      bundle = this.minifyJavaScript(bundle);
    }
    
    return bundle;
  }

  async resolveDependencies(entryPoint) {
    const dependencies = [];
    const visited = new Set();
    
    const resolve = async (file) => {
      if (visited.has(file)) return;
      visited.add(file);
      
      try {
        const content = await fs.readFile(file, 'utf8');
        const imports = this.extractImports(content);
        
        for (const importPath of imports) {
          const resolved = this.resolveImportPath(file, importPath);
          if (resolved && await this.fileExists(resolved)) {
            await resolve(resolved);
          }
        }
        
        dependencies.push(file);
      } catch (error) {
        // File doesn't exist or can't be read
      }
    };
    
    await resolve(entryPoint);
    return dependencies;
  }

  extractImports(content) {
    const importRegex = /import\s+.*?\s+from\s+['"`]([^'"`]+)['"`]/g;
    const imports = [];
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      imports.push(match[1]);
    }
    
    return imports;
  }

  resolveImportPath(currentFile, importPath) {
    if (importPath.startsWith('@/')) {
      return importPath.replace('@/', 'src/') + '.ts';
    } else if (importPath.startsWith('./') || importPath.startsWith('../')) {
      const currentDir = path.dirname(currentFile);
      return path.resolve(currentDir, importPath + '.ts');
    }
    return null;
  }

  compileTypeScript(content) {
    // Remove TypeScript-specific syntax
    return content
      .replace(/:\s*[A-Za-z_][A-Za-z0-9_<>[\]|&\s]*(?=[,;=\)\{\}])/g, '')
      .replace(/interface\s+[^{]+\{[^}]*\}/g, '')
      .replace(/export\s+interface\s+[^{]+\{[^}]*\}/g, '')
      .replace(/import\s+type\s+[^;]+;/g, '')
      .replace(/as\s+[A-Za-z_][A-Za-z0-9_<>[\]|&\s]*/g, '');
  }

  wrapModule(filePath, content) {
    const moduleName = filePath.replace(/[^a-zA-Z0-9]/g, '_');
    return `
// Module: ${filePath}
(function() {
  const exports = {};
  ${content}
  window.modules = window.modules || {};
  window.modules['${filePath}'] = exports;
})();`;
  }

  getModuleSystemCode() {
    return `
// Simple module system for production
window.modules = {};
window.require = function(path) {
  return window.modules[path] || {};
};
`;
  }

  async copyTypeScriptFiles() {
    console.log('  📋 Copying TypeScript files...');
    
    const tsFiles = await this.findFiles('.', '.ts');
    
    for (const file of tsFiles) {
      try {
        const content = await fs.readFile(file, 'utf8');
        const compiled = this.compileTypeScript(content);
        const jsFile = file.replace('.ts', '.js');
        const outputPath = path.join(this.config.outputDir, jsFile);
        
        await fs.mkdir(path.dirname(outputPath), { recursive: true });
        await fs.writeFile(outputPath, compiled);
        
      } catch (error) {
        // Skip files that can't be processed
      }
    }
  }

  async processCSSFiles() {
    console.log('🎨 Processing CSS files...');
    
    // Extract CSS from HTML files and create combined stylesheet
    const htmlFiles = await this.findFiles(this.config.outputDir, '.html');
    let combinedCSS = '';
    
    for (const file of htmlFiles) {
      const content = await fs.readFile(file, 'utf8');
      const css = this.extractInlineCSS(content);
      combinedCSS += css + '\n';
    }
    
    if (combinedCSS.trim()) {
      const optimized = this.optimizeCSS(combinedCSS);
      const outputPath = path.join(this.config.outputDir, 'styles.min.css');
      await fs.writeFile(outputPath, optimized);
      
      console.log(`  ✓ styles.min.css (${this.formatBytes(optimized.length)})`);
    }
  }

  async copyAssets() {
    console.log('📦 Copying assets...');
    
    // Copy README and documentation
    const docFiles = ['README.md', 'DEVELOPER_GUIDE.md', 'PERFORMANCE_BENCHMARKS.md'];
    
    for (const file of docFiles) {
      try {
        await fs.copyFile(file, path.join(this.config.outputDir, file));
        console.log(`  ✓ ${file}`);
      } catch (error) {
        // File might not exist
      }
    }
  }

  async generateServiceWorker() {
    console.log('⚙️ Generating service worker...');
    
    const swContent = `
// Service Worker for Ultimate Bullet Hell RPG
const CACHE_NAME = 'bullet-hell-rpg-v${Date.now()}';
const urlsToCache = [
  '/',
  '/demo.html',
  '/dungeon-demo.html',
  '/complete-demo.html',
  '/ultimate-demo.html',
  '/map-editor.html',
  '/styles.min.css',
  '/ultimate-game.bundle.js',
  '/map-editor.bundle.js',
  '/game.bundle.js'
];

self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', event => {
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        if (response) {
          return response;
        }
        return fetch(event.request);
      })
  );
});
`;
    
    await fs.writeFile(path.join(this.config.outputDir, 'sw.js'), swContent);
  }

  async createManifest() {
    console.log('📋 Creating production manifest...');
    
    const manifest = {
      name: 'Ultimate Bullet Hell RPG',
      version: '1.0.0',
      buildTime: new Date().toISOString(),
      files: {},
      optimization: {
        totalFiles: this.stats.filesProcessed,
        originalSize: this.stats.originalSize,
        optimizedSize: this.stats.optimizedSize,
        compressionRatio: ((this.stats.originalSize - this.stats.optimizedSize) / this.stats.originalSize * 100).toFixed(2) + '%'
      }
    };
    
    // Add file listing
    const files = await this.findFiles(this.config.outputDir, '.*');
    for (const file of files) {
      try {
        const stats = await fs.stat(file);
        const relativePath = path.relative(this.config.outputDir, file);
        manifest.files[relativePath] = {
          size: stats.size,
          modified: stats.mtime.toISOString()
        };
      } catch (error) {
        // Skip files that can't be accessed
      }
    }
    
    await fs.writeFile(
      path.join(this.config.outputDir, 'manifest.json'),
      JSON.stringify(manifest, null, 2)
    );
  }

  generateBuildReport() {
    const duration = (Date.now() - this.stats.startTime) / 1000;
    const savings = this.stats.originalSize - this.stats.optimizedSize;
    const compressionRatio = (savings / this.stats.originalSize * 100).toFixed(1);
    
    console.log('\n📊 Build Report:');
    console.log('================');
    console.log(`Files processed: ${this.stats.filesProcessed}`);
    console.log(`Original size: ${this.formatBytes(this.stats.originalSize)}`);
    console.log(`Optimized size: ${this.formatBytes(this.stats.optimizedSize)}`);
    console.log(`Size reduction: ${this.formatBytes(savings)} (${compressionRatio}%)`);
    console.log(`Build time: ${duration.toFixed(2)}s`);
    console.log(`Output directory: ${this.config.outputDir}`);
  }

  // Utility methods
  minifyHTML(html) {
    return html
      .replace(/<!--[\s\S]*?-->/g, '')
      .replace(/\s+/g, ' ')
      .replace(/>\s+</g, '><')
      .trim();
  }

  optimizeScriptLoading(html) {
    return html.replace(/<script\s+type="module"\s+src=/g, '<script async src=');
  }

  updateProductionPaths(html) {
    return html
      .replace(/\.\/src\//g, './')
      .replace(/\.ts'/g, ".js'")
      .replace(/\.ts"/g, '.js"');
  }

  async inlineCriticalCSS(html) {
    // For production, you might want to inline critical CSS
    return html;
  }

  extractInlineCSS(html) {
    const styleRegex = /<style[^>]*>([\s\S]*?)<\/style>/gi;
    let css = '';
    let match;
    
    while ((match = styleRegex.exec(html)) !== null) {
      css += match[1] + '\n';
    }
    
    return css;
  }

  optimizeCSS(css) {
    if (!this.config.minifyCSS) return css;
    
    return css
      .replace(/\/\*[\s\S]*?\*\//g, '')
      .replace(/\s+/g, ' ')
      .replace(/\s*([{}:;,>+~])\s*/g, '$1')
      .replace(/;}/g, '}')
      .replace(/[^{}]+\{\s*\}/g, '')
      .trim();
  }

  minifyJavaScript(js) {
    if (!this.config.minifyJS) return js;
    
    return js
      .replace(/\/\*[\s\S]*?\*\//g, '')
      .replace(/\/\/.*$/gm, '')
      .replace(/\s+/g, ' ')
      .replace(/\s*([{}:;,=()[\]])\s*/g, '$1')
      .trim();
  }

  async findFiles(dir, extension) {
    const files = [];
    
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
          const subFiles = await this.findFiles(fullPath, extension);
          files.push(...subFiles);
        } else if (entry.isFile() && (extension === '.*' || entry.name.endsWith(extension))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      // Directory doesn't exist or can't be read
    }
    
    return files;
  }

  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  updateStats(originalSize, optimizedSize) {
    this.stats.filesProcessed++;
    this.stats.originalSize += originalSize;
    this.stats.optimizedSize += optimizedSize;
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// Run the build
if (require.main === module) {
  const builder = new ProductionBuilder();
  builder.build().catch(console.error);
}

module.exports = ProductionBuilder;
