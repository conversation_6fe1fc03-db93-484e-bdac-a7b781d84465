{"build": {"sourceDir": ".", "outputDir": "./dist", "tempDir": "./temp", "cleanOutput": true}, "optimization": {"minifyHTML": true, "minifyJS": true, "minifyCSS": true, "optimizeImages": true, "generateSourceMaps": false, "compressionLevel": 9, "removeComments": true, "removeWhitespace": true, "bundleModules": true}, "assets": {"copyPatterns": [{"from": "README.md", "to": "docs/README.md"}, {"from": "DEVELOPER_GUIDE.md", "to": "docs/DEVELOPER_GUIDE.md"}, {"from": "PERFORMANCE_BENCHMARKS.md", "to": "docs/PERFORMANCE_BENCHMARKS.md"}, {"from": "PROJECT_COMPLETION_SUMMARY.md", "to": "docs/PROJECT_COMPLETION_SUMMARY.md"}, {"from": "DEPLOYMENT_GUIDE.md", "to": "docs/DEPLOYMENT_GUIDE.md"}], "imageOptimization": {"enabled": true, "quality": 85, "progressive": true, "base64Threshold": 10240}}, "html": {"files": ["demo.html", "dungeon-demo.html", "complete-demo.html", "ultimate-demo.html", "map-editor.html"], "optimization": {"removeComments": true, "collapseWhitespace": true, "removeEmptyAttributes": true, "removeRedundantAttributes": true, "useShortDoctype": true, "removeStyleLinkTypeAttributes": true, "removeScriptTypeAttributes": true, "minifyJS": true, "minifyCSS": true}, "inlineCSS": {"enabled": true, "threshold": 5000}}, "javascript": {"entryPoints": [{"input": "src/game/UltimateGame.ts", "output": "ultimate-game.bundle.js", "format": "iife"}, {"input": "src/tools/MapEditor.ts", "output": "map-editor.bundle.js", "format": "iife"}, {"input": "src/engine/core/Game.ts", "output": "game.bundle.js", "format": "iife"}], "typescript": {"enabled": true, "removeTypes": true, "removeInterfaces": true, "removeImportTypes": true}, "minification": {"enabled": true, "mangle": true, "compress": {"drop_console": false, "drop_debugger": true, "pure_funcs": ["console.log"]}}, "moduleSystem": {"type": "custom", "generateLoader": true}}, "css": {"extractInline": true, "combineFiles": true, "outputFile": "styles.min.css", "optimization": {"removeComments": true, "removeWhitespace": true, "removeEmptyRules": true, "mergeDuplicateRules": true, "optimizeSelectors": true}, "autoprefixer": {"enabled": true, "browsers": ["> 1%", "last 2 versions", "not dead"]}}, "serviceWorker": {"enabled": true, "filename": "sw.js", "cacheStrategy": "cacheFirst", "cacheName": "bullet-hell-rpg-cache", "urlsToCache": ["/", "/index.html", "/demo.html", "/dungeon-demo.html", "/complete-demo.html", "/ultimate-demo.html", "/map-editor.html", "/styles.min.css", "/module-loader.js"], "runtimeCaching": [{"urlPattern": "/src/.*\\.js$", "handler": "cacheFirst"}, {"urlPattern": "/docs/.*", "handler": "networkFirst"}]}, "manifest": {"enabled": true, "filename": "manifest.json", "includeFileList": true, "includeOptimizationStats": true, "metadata": {"name": "Ultimate Bullet Hell RPG", "version": "1.0.0", "description": "A comprehensive bullet hell rogue-like RPG with advanced systems", "author": "Ultimate Game Developer", "license": "MIT", "repository": "https://github.com/username/bullet-hell-rpg", "homepage": "https://yourdomain.com"}}, "performance": {"targets": {"firstContentfulPaint": 2000, "largestContentfulPaint": 4000, "cumulativeLayoutShift": 0.1, "firstInputDelay": 100}, "budgets": {"totalSize": "2MB", "jsSize": "1MB", "cssSize": "200KB", "imageSize": "500KB"}}, "security": {"contentSecurityPolicy": {"enabled": true, "directives": {"default-src": ["'self'"], "script-src": ["'self'", "'unsafe-inline'"], "style-src": ["'self'", "'unsafe-inline'"], "img-src": ["'self'", "data:"], "connect-src": ["'self'"], "font-src": ["'self'"], "object-src": ["'none'"], "media-src": ["'self'"], "frame-src": ["'none'"]}}, "headers": {"X-Frame-Options": "SAMEORIGIN", "X-Content-Type-Options": "nosniff", "X-XSS-Protection": "1; mode=block", "Referrer-Policy": "strict-origin-when-cross-origin"}}, "deployment": {"environments": {"development": {"minification": false, "sourceMaps": true, "compression": false}, "staging": {"minification": true, "sourceMaps": true, "compression": true}, "production": {"minification": true, "sourceMaps": false, "compression": true}}, "cdn": {"enabled": false, "baseUrl": "https://cdn.yourdomain.com", "assets": ["css", "js", "images"]}}, "analytics": {"enabled": false, "providers": {"googleAnalytics": {"trackingId": "GA_TRACKING_ID", "anonymizeIp": true}, "customAnalytics": {"endpoint": "https://analytics.yourdomain.com/track", "events": ["gameStart", "levelComplete", "error"]}}}, "monitoring": {"errorTracking": {"enabled": true, "logLevel": "error", "maxErrors": 100}, "performanceTracking": {"enabled": true, "sampleRate": 0.1, "metrics": ["fps", "memoryUsage", "loadTime"]}}}