/**
 * Comprehensive validation system for game data and user input
 */

import { Vector2 } from '@/utils/math/Vector2';

export interface ValidationRule<T> {
  name: string;
  validate: (value: T) => boolean;
  message: string;
}

export interface ValidationSchema<T> {
  [key: string]: ValidationRule<any>[];
}

export interface ValidationResult<T> {
  success: boolean;
  data?: T;
  errors: string[];
  warnings: string[];
}

export class Validator {
  private static commonRules = {
    // Number validation rules
    isNumber: <ValidationRule<any>>{
      name: 'isNumber',
      validate: (value) => typeof value === 'number' && !isNaN(value),
      message: 'Value must be a valid number'
    },

    isPositive: <ValidationRule<number>>{
      name: 'isPositive',
      validate: (value) => value > 0,
      message: 'Value must be positive'
    },

    isNonNegative: <ValidationRule<number>>{
      name: 'isNonNegative',
      validate: (value) => value >= 0,
      message: 'Value must be non-negative'
    },

    inRange: (min: number, max: number): ValidationRule<number> => ({
      name: 'inRange',
      validate: (value) => value >= min && value <= max,
      message: `Value must be between ${min} and ${max}`
    }),

    // String validation rules
    isString: <ValidationRule<any>>{
      name: 'isString',
      validate: (value) => typeof value === 'string',
      message: 'Value must be a string'
    },

    isNotEmpty: <ValidationRule<string>>{
      name: 'isNotEmpty',
      validate: (value) => value.trim().length > 0,
      message: 'Value cannot be empty'
    },

    maxLength: (length: number): ValidationRule<string> => ({
      name: 'maxLength',
      validate: (value) => value.length <= length,
      message: `Value must be ${length} characters or less`
    }),

    minLength: (length: number): ValidationRule<string> => ({
      name: 'minLength',
      validate: (value) => value.length >= length,
      message: `Value must be at least ${length} characters`
    }),

    // Object validation rules
    isObject: <ValidationRule<any>>{
      name: 'isObject',
      validate: (value) => typeof value === 'object' && value !== null,
      message: 'Value must be an object'
    },

    hasProperty: (property: string): ValidationRule<object> => ({
      name: 'hasProperty',
      validate: (value) => property in value,
      message: `Object must have property '${property}'`
    }),

    // Array validation rules
    isArray: <ValidationRule<any>>{
      name: 'isArray',
      validate: (value) => Array.isArray(value),
      message: 'Value must be an array'
    },

    arrayLength: (length: number): ValidationRule<any[]> => ({
      name: 'arrayLength',
      validate: (value) => value.length === length,
      message: `Array must have exactly ${length} elements`
    }),

    arrayMinLength: (length: number): ValidationRule<any[]> => ({
      name: 'arrayMinLength',
      validate: (value) => value.length >= length,
      message: `Array must have at least ${length} elements`
    }),

    arrayMaxLength: (length: number): ValidationRule<any[]> => ({
      name: 'arrayMaxLength',
      validate: (value) => value.length <= length,
      message: `Array must have at most ${length} elements`
    })
  };

  // Game-specific validation rules
  static gameRules = {
    // Vector2 validation
    isValidVector2: <ValidationRule<any>>{
      name: 'isValidVector2',
      validate: (value) => {
        return value instanceof Vector2 || 
               (typeof value === 'object' && 
                typeof value.x === 'number' && 
                typeof value.y === 'number' &&
                !isNaN(value.x) && !isNaN(value.y));
      },
      message: 'Value must be a valid Vector2 object'
    },

    // Position validation
    isValidPosition: <ValidationRule<Vector2>>{
      name: 'isValidPosition',
      validate: (value) => {
        return value.x >= 0 && value.x <= 1024 && 
               value.y >= 0 && value.y <= 768;
      },
      message: 'Position must be within game bounds (0-1024, 0-768)'
    },

    // Health validation
    isValidHealth: <ValidationRule<number>>{
      name: 'isValidHealth',
      validate: (value) => value >= 0 && value <= 10000,
      message: 'Health must be between 0 and 10000'
    },

    // Level validation
    isValidLevel: <ValidationRule<number>>{
      name: 'isValidLevel',
      validate: (value) => Number.isInteger(value) && value >= 1 && value <= 100,
      message: 'Level must be an integer between 1 and 100'
    },

    // Experience validation
    isValidExperience: <ValidationRule<number>>{
      name: 'isValidExperience',
      validate: (value) => Number.isInteger(value) && value >= 0,
      message: 'Experience must be a non-negative integer'
    },

    // Item rarity validation
    isValidRarity: <ValidationRule<string>>{
      name: 'isValidRarity',
      validate: (value) => ['common', 'uncommon', 'rare', 'epic', 'legendary'].includes(value),
      message: 'Rarity must be one of: common, uncommon, rare, epic, legendary'
    },

    // Weapon type validation
    isValidWeaponType: <ValidationRule<string>>{
      name: 'isValidWeaponType',
      validate: (value) => ['bow', 'crossbow', 'staff', 'wand'].includes(value),
      message: 'Weapon type must be one of: bow, crossbow, staff, wand'
    },

    // Color validation
    isValidColor: <ValidationRule<string>>{
      name: 'isValidColor',
      validate: (value) => {
        // Check hex color format
        return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value) ||
               // Check rgb/rgba format
               /^rgba?\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*(,\s*[\d.]+)?\s*\)$/.test(value) ||
               // Check named colors
               ['red', 'green', 'blue', 'yellow', 'orange', 'purple', 'pink', 'brown', 'black', 'white'].includes(value.toLowerCase());
      },
      message: 'Value must be a valid color (hex, rgb, rgba, or named color)'
    }
  };

  // Validate a single value against rules
  static validateValue<T>(value: T, rules: ValidationRule<T>[]): ValidationResult<T> {
    const errors: string[] = [];
    const warnings: string[] = [];

    for (const rule of rules) {
      try {
        if (!rule.validate(value)) {
          errors.push(rule.message);
        }
      } catch (error) {
        errors.push(`Validation rule '${rule.name}' failed: ${error}`);
      }
    }

    return {
      success: errors.length === 0,
      data: errors.length === 0 ? value : undefined,
      errors,
      warnings
    };
  }

  // Validate an object against a schema
  static validateObject<T extends object>(obj: T, schema: ValidationSchema<T>): ValidationResult<T> {
    const errors: string[] = [];
    const warnings: string[] = [];

    for (const [key, rules] of Object.entries(schema)) {
      if (!(key in obj)) {
        errors.push(`Missing required property: ${key}`);
        continue;
      }

      const value = (obj as any)[key];
      const result = this.validateValue(value, rules);
      
      errors.push(...result.errors.map(error => `${key}: ${error}`));
      warnings.push(...result.warnings.map(warning => `${key}: ${warning}`));
    }

    return {
      success: errors.length === 0,
      data: errors.length === 0 ? obj : undefined,
      errors,
      warnings
    };
  }

  // Game-specific validation methods
  static validatePlayerData(playerData: any): ValidationResult<any> {
    const schema: ValidationSchema<any> = {
      position: [this.gameRules.isValidVector2, this.gameRules.isValidPosition],
      health: [this.commonRules.isNumber, this.gameRules.isValidHealth],
      maxHealth: [this.commonRules.isNumber, this.gameRules.isValidHealth],
      level: [this.commonRules.isNumber, this.gameRules.isValidLevel],
      experience: [this.commonRules.isNumber, this.gameRules.isValidExperience]
    };

    return this.validateObject(playerData, schema);
  }

  static validateItemData(itemData: any): ValidationResult<any> {
    const schema: ValidationSchema<any> = {
      name: [this.commonRules.isString, this.commonRules.isNotEmpty, this.commonRules.maxLength(50)],
      rarity: [this.commonRules.isString, this.gameRules.isValidRarity],
      value: [this.commonRules.isNumber, this.commonRules.isNonNegative],
      stackSize: [this.commonRules.isNumber, this.commonRules.isPositive, this.commonRules.inRange(1, 1000)]
    };

    return this.validateObject(itemData, schema);
  }

  static validateEnemyData(enemyData: any): ValidationResult<any> {
    const schema: ValidationSchema<any> = {
      position: [this.gameRules.isValidVector2, this.gameRules.isValidPosition],
      health: [this.commonRules.isNumber, this.gameRules.isValidHealth],
      damage: [this.commonRules.isNumber, this.commonRules.isPositive],
      speed: [this.commonRules.isNumber, this.commonRules.isNonNegative]
    };

    return this.validateObject(enemyData, schema);
  }

  static validateSaveData(saveData: any): ValidationResult<any> {
    const schema: ValidationSchema<any> = {
      version: [this.commonRules.isString, this.commonRules.isNotEmpty],
      timestamp: [this.commonRules.isNumber, this.commonRules.isPositive],
      playerState: [this.commonRules.isObject],
      progression: [this.commonRules.isObject],
      inventory: [this.commonRules.isObject]
    };

    return this.validateObject(saveData, schema);
  }

  // Input sanitization
  static sanitizeString(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .substring(0, 1000); // Limit length
  }

  static sanitizeNumber(input: any, min: number = -Infinity, max: number = Infinity): number {
    const num = Number(input);
    if (isNaN(num)) return 0;
    return Math.max(min, Math.min(max, num));
  }

  static sanitizeVector2(input: any): Vector2 {
    if (input instanceof Vector2) return input;
    
    const x = this.sanitizeNumber(input?.x, -10000, 10000);
    const y = this.sanitizeNumber(input?.y, -10000, 10000);
    
    return new Vector2(x, y);
  }

  // Batch validation
  static validateBatch<T>(items: T[], validator: (item: T) => ValidationResult<T>): {
    valid: T[];
    invalid: { item: T; errors: string[] }[];
    totalErrors: number;
  } {
    const valid: T[] = [];
    const invalid: { item: T; errors: string[] }[] = [];
    let totalErrors = 0;

    for (const item of items) {
      const result = validator(item);
      if (result.success && result.data) {
        valid.push(result.data);
      } else {
        invalid.push({ item, errors: result.errors });
        totalErrors += result.errors.length;
      }
    }

    return { valid, invalid, totalErrors };
  }

  // Create custom validation rule
  static createRule<T>(
    name: string,
    validate: (value: T) => boolean,
    message: string
  ): ValidationRule<T> {
    return { name, validate, message };
  }

  // Combine multiple validation rules
  static combineRules<T>(...rules: ValidationRule<T>[]): ValidationRule<T>[] {
    return rules;
  }
}
