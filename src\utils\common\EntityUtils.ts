/**
 * Common entity utility functions to reduce code duplication
 */

import { Vector2 } from '@/utils/math/Vector2';
import { MathUtils } from './MathUtils';

export interface EntityStats {
  health: number;
  maxHealth: number;
  mana?: number;
  maxMana?: number;
  damage: number;
  speed: number;
  defense?: number;
  criticalChance?: number;
  criticalMultiplier?: number;
}

export interface EntityBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface MovementState {
  position: Vector2;
  velocity: Vector2;
  acceleration: Vector2;
  maxSpeed: number;
  friction: number;
}

export class EntityUtils {
  /**
   * Apply damage to an entity with defense calculation
   */
  static applyDamage(
    stats: EntityStats,
    damage: number,
    isCritical: boolean = false,
    damageType: 'physical' | 'magical' = 'physical'
  ): {
    actualDamage: number;
    newHealth: number;
    isDead: boolean;
  } {
    let actualDamage = damage;
    
    // Apply critical hit
    if (isCritical && stats.criticalMultiplier) {
      actualDamage *= stats.criticalMultiplier;
    }
    
    // Apply defense
    if (stats.defense && damageType === 'physical') {
      const damageReduction = stats.defense / (stats.defense + 100);
      actualDamage *= (1 - damageReduction);
    }
    
    // Ensure minimum damage
    actualDamage = Math.max(1, Math.floor(actualDamage));
    
    const newHealth = Math.max(0, stats.health - actualDamage);
    const isDead = newHealth <= 0;
    
    return {
      actualDamage,
      newHealth,
      isDead
    };
  }

  /**
   * Check if an attack is a critical hit
   */
  static isCriticalHit(criticalChance: number): boolean {
    return Math.random() < MathUtils.clamp(criticalChance, 0, 1);
  }

  /**
   * Calculate experience gain with level scaling
   */
  static calculateExperienceGain(
    baseExperience: number,
    playerLevel: number,
    enemyLevel: number
  ): number {
    const levelDifference = enemyLevel - playerLevel;
    let multiplier = 1;
    
    if (levelDifference > 0) {
      // Bonus for higher level enemies
      multiplier = 1 + (levelDifference * 0.1);
    } else if (levelDifference < -5) {
      // Penalty for much lower level enemies
      multiplier = Math.max(0.1, 1 + (levelDifference * 0.05));
    }
    
    return Math.floor(baseExperience * multiplier);
  }

  /**
   * Update movement with physics
   */
  static updateMovement(
    movement: MovementState,
    inputDirection: Vector2,
    deltaTime: number
  ): void {
    // Apply input to acceleration
    movement.acceleration.set(
      inputDirection.x * movement.maxSpeed,
      inputDirection.y * movement.maxSpeed
    );
    
    // Update velocity
    movement.velocity.add(movement.acceleration.multiply(deltaTime));
    
    // Apply friction
    movement.velocity.multiply(Math.pow(movement.friction, deltaTime));
    
    // Clamp to max speed
    if (movement.velocity.magnitude() > movement.maxSpeed) {
      movement.velocity.normalize().multiply(movement.maxSpeed);
    }
    
    // Update position
    movement.position.add(movement.velocity.multiply(deltaTime));
    
    // Reset acceleration
    movement.acceleration.set(0, 0);
  }

  /**
   * Keep entity within bounds
   */
  static constrainToBounds(
    position: Vector2,
    entityRadius: number,
    bounds: EntityBounds
  ): void {
    position.x = MathUtils.clamp(
      position.x,
      bounds.x + entityRadius,
      bounds.x + bounds.width - entityRadius
    );
    
    position.y = MathUtils.clamp(
      position.y,
      bounds.y + entityRadius,
      bounds.y + bounds.height - entityRadius
    );
  }

  /**
   * Calculate AI steering behavior - seek
   */
  static seek(
    currentPosition: Vector2,
    currentVelocity: Vector2,
    targetPosition: Vector2,
    maxSpeed: number,
    maxForce: number
  ): Vector2 {
    const desired = targetPosition.subtract(currentPosition).normalize().multiply(maxSpeed);
    const steer = desired.subtract(currentVelocity);
    
    if (steer.magnitude() > maxForce) {
      steer.normalize().multiply(maxForce);
    }
    
    return steer;
  }

  /**
   * Calculate AI steering behavior - flee
   */
  static flee(
    currentPosition: Vector2,
    currentVelocity: Vector2,
    targetPosition: Vector2,
    maxSpeed: number,
    maxForce: number
  ): Vector2 {
    const desired = currentPosition.subtract(targetPosition).normalize().multiply(maxSpeed);
    const steer = desired.subtract(currentVelocity);
    
    if (steer.magnitude() > maxForce) {
      steer.normalize().multiply(maxForce);
    }
    
    return steer;
  }

  /**
   * Calculate AI steering behavior - wander
   */
  static wander(
    currentVelocity: Vector2,
    wanderAngle: number,
    wanderRadius: number,
    wanderDistance: number,
    wanderJitter: number,
    deltaTime: number
  ): { force: Vector2; newWanderAngle: number } {
    // Update wander angle
    const newWanderAngle = wanderAngle + (Math.random() - 0.5) * wanderJitter * deltaTime;
    
    // Calculate wander target
    const circleCenter = currentVelocity.normalize().multiply(wanderDistance);
    const displacement = Vector2.fromAngle(newWanderAngle, wanderRadius);
    const wanderTarget = circleCenter.add(displacement);
    
    return {
      force: wanderTarget.normalize(),
      newWanderAngle
    };
  }

  /**
   * Calculate line of sight between two entities
   */
  static hasLineOfSight(
    fromPosition: Vector2,
    toPosition: Vector2,
    obstacles: Array<{ position: Vector2; radius: number }>,
    maxDistance: number = Infinity
  ): boolean {
    const distance = fromPosition.distance(toPosition);
    if (distance > maxDistance) return false;
    
    const direction = toPosition.subtract(fromPosition).normalize();
    const stepSize = 5; // Check every 5 units
    const steps = Math.floor(distance / stepSize);
    
    for (let i = 1; i < steps; i++) {
      const checkPoint = fromPosition.add(direction.multiply(i * stepSize));
      
      for (const obstacle of obstacles) {
        if (checkPoint.distance(obstacle.position) < obstacle.radius) {
          return false;
        }
      }
    }
    
    return true;
  }

  /**
   * Calculate optimal attack position around a target
   */
  static calculateAttackPosition(
    attackerPosition: Vector2,
    targetPosition: Vector2,
    attackRange: number,
    preferredAngle?: number
  ): Vector2 {
    const angle = preferredAngle ?? attackerPosition.angleTo(targetPosition);
    const distance = attackRange * 0.8; // Stay slightly within range
    
    return targetPosition.add(Vector2.fromAngle(angle + Math.PI, distance));
  }

  /**
   * Predict future position of a moving target
   */
  static predictTargetPosition(
    targetPosition: Vector2,
    targetVelocity: Vector2,
    projectileSpeed: number,
    maxPredictionTime: number = 2
  ): Vector2 {
    if (projectileSpeed <= 0) return targetPosition.copy();
    
    const distance = targetPosition.magnitude();
    const timeToTarget = Math.min(distance / projectileSpeed, maxPredictionTime);
    
    return targetPosition.add(targetVelocity.multiply(timeToTarget));
  }

  /**
   * Calculate knockback force
   */
  static calculateKnockback(
    fromPosition: Vector2,
    toPosition: Vector2,
    force: number
  ): Vector2 {
    const direction = toPosition.subtract(fromPosition);
    if (direction.magnitude() === 0) {
      // Random direction if positions are identical
      return Vector2.fromAngle(Math.random() * MathUtils.TWO_PI, force);
    }
    
    return direction.normalize().multiply(force);
  }

  /**
   * Check if entity is within screen bounds (for culling)
   */
  static isOnScreen(
    entityPosition: Vector2,
    entityRadius: number,
    cameraPosition: Vector2,
    screenWidth: number,
    screenHeight: number,
    margin: number = 50
  ): boolean {
    const left = cameraPosition.x - margin;
    const right = cameraPosition.x + screenWidth + margin;
    const top = cameraPosition.y - margin;
    const bottom = cameraPosition.y + screenHeight + margin;
    
    return (
      entityPosition.x + entityRadius >= left &&
      entityPosition.x - entityRadius <= right &&
      entityPosition.y + entityRadius >= top &&
      entityPosition.y - entityRadius <= bottom
    );
  }

  /**
   * Calculate spawn position that's not too close to other entities
   */
  static findSafeSpawnPosition(
    bounds: EntityBounds,
    existingEntities: Array<{ position: Vector2; radius: number }>,
    minDistance: number,
    maxAttempts: number = 50
  ): Vector2 | null {
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      const position = new Vector2(
        MathUtils.random(bounds.x, bounds.x + bounds.width),
        MathUtils.random(bounds.y, bounds.y + bounds.height)
      );
      
      let isSafe = true;
      for (const entity of existingEntities) {
        if (position.distance(entity.position) < minDistance + entity.radius) {
          isSafe = false;
          break;
        }
      }
      
      if (isSafe) {
        return position;
      }
    }
    
    return null; // Couldn't find safe position
  }

  /**
   * Calculate entity level based on experience
   */
  static calculateLevel(experience: number, baseExperience: number = 100): number {
    // Exponential level curve: level = floor(sqrt(exp / base))
    return Math.floor(Math.sqrt(experience / baseExperience)) + 1;
  }

  /**
   * Calculate experience required for next level
   */
  static experienceForLevel(level: number, baseExperience: number = 100): number {
    // Exponential curve: exp = base * level^2
    return baseExperience * level * level;
  }

  /**
   * Scale stats based on level
   */
  static scaleStatsForLevel(baseStats: EntityStats, level: number): EntityStats {
    const scaleFactor = 1 + (level - 1) * 0.1; // 10% increase per level
    
    return {
      ...baseStats,
      health: Math.floor(baseStats.health * scaleFactor),
      maxHealth: Math.floor(baseStats.maxHealth * scaleFactor),
      mana: baseStats.mana ? Math.floor(baseStats.mana * scaleFactor) : undefined,
      maxMana: baseStats.maxMana ? Math.floor(baseStats.maxMana * scaleFactor) : undefined,
      damage: Math.floor(baseStats.damage * scaleFactor),
      speed: baseStats.speed * Math.min(scaleFactor, 1.5), // Cap speed scaling
      defense: baseStats.defense ? Math.floor(baseStats.defense * scaleFactor) : undefined
    };
  }
}
