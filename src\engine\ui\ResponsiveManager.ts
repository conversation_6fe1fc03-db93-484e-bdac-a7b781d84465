/**
 * Responsive design manager for different screen sizes and orientations
 */

import { Vector2 } from '@/utils/math/Vector2';
import { ErrorHandler } from '@/utils/error/ErrorHandler';

export type DeviceType = 'mobile' | 'tablet' | 'desktop';
export type Orientation = 'portrait' | 'landscape';

export interface ScreenInfo {
  width: number;
  height: number;
  devicePixelRatio: number;
  deviceType: DeviceType;
  orientation: Orientation;
  isTouch: boolean;
  aspectRatio: number;
}

export interface ResponsiveBreakpoints {
  mobile: number;
  tablet: number;
  desktop: number;
}

export interface ResponsiveConfig {
  baseWidth: number;
  baseHeight: number;
  minScale: number;
  maxScale: number;
  maintainAspectRatio: boolean;
  centerContent: boolean;
  allowFullscreen: boolean;
  autoResize: boolean;
}

export class ResponsiveManager {
  private errorHandler = ErrorHandler.getInstance();
  private canvas: HTMLCanvasElement;
  private container: HTMLElement;
  private config: ResponsiveConfig;
  
  private currentScreenInfo: ScreenInfo;
  private scale: number = 1;
  private offset: Vector2 = new Vector2(0, 0);
  
  private breakpoints: ResponsiveBreakpoints = {
    mobile: 768,
    tablet: 1024,
    desktop: 1920
  };
  
  private resizeListeners: ((screenInfo: ScreenInfo) => void)[] = [];
  private orientationListeners: ((orientation: Orientation) => void)[] = [];

  constructor(canvas: HTMLCanvasElement, config: Partial<ResponsiveConfig> = {}) {
    this.canvas = canvas;
    this.container = canvas.parentElement || document.body;
    
    this.config = {
      baseWidth: 1024,
      baseHeight: 768,
      minScale: 0.5,
      maxScale: 2.0,
      maintainAspectRatio: true,
      centerContent: true,
      allowFullscreen: true,
      autoResize: true,
      ...config
    };
    
    this.currentScreenInfo = this.getScreenInfo();
    this.setupEventListeners();
    this.updateLayout();
  }

  private setupEventListeners(): void {
    if (this.config.autoResize) {
      window.addEventListener('resize', this.handleResize.bind(this));
      window.addEventListener('orientationchange', this.handleOrientationChange.bind(this));
      
      // Listen for fullscreen changes
      document.addEventListener('fullscreenchange', this.handleFullscreenChange.bind(this));
      document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange.bind(this));
      document.addEventListener('mozfullscreenchange', this.handleFullscreenChange.bind(this));
      document.addEventListener('MSFullscreenChange', this.handleFullscreenChange.bind(this));
    }
  }

  private getScreenInfo(): ScreenInfo {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const devicePixelRatio = window.devicePixelRatio || 1;
    
    return {
      width,
      height,
      devicePixelRatio,
      deviceType: this.detectDeviceType(width),
      orientation: width > height ? 'landscape' : 'portrait',
      isTouch: this.isTouchDevice(),
      aspectRatio: width / height
    };
  }

  private detectDeviceType(width: number): DeviceType {
    if (width <= this.breakpoints.mobile) {
      return 'mobile';
    } else if (width <= this.breakpoints.tablet) {
      return 'tablet';
    } else {
      return 'desktop';
    }
  }

  private isTouchDevice(): boolean {
    return 'ontouchstart' in window || 
           navigator.maxTouchPoints > 0 || 
           (navigator as any).msMaxTouchPoints > 0;
  }

  private handleResize(): void {
    try {
      const newScreenInfo = this.getScreenInfo();
      const oldDeviceType = this.currentScreenInfo.deviceType;
      const oldOrientation = this.currentScreenInfo.orientation;
      
      this.currentScreenInfo = newScreenInfo;
      this.updateLayout();
      
      // Notify listeners
      this.notifyResizeListeners(newScreenInfo);
      
      // Check for device type or orientation changes
      if (oldDeviceType !== newScreenInfo.deviceType) {
        this.handleDeviceTypeChange(oldDeviceType, newScreenInfo.deviceType);
      }
      
      if (oldOrientation !== newScreenInfo.orientation) {
        this.notifyOrientationListeners(newScreenInfo.orientation);
      }
      
    } catch (error) {
      this.errorHandler.error('Resize handling failed', error, 'responsive');
    }
  }

  private handleOrientationChange(): void {
    // Delay to allow for orientation change to complete
    setTimeout(() => {
      this.handleResize();
    }, 100);
  }

  private handleFullscreenChange(): void {
    setTimeout(() => {
      this.handleResize();
    }, 100);
  }

  private handleDeviceTypeChange(oldType: DeviceType, newType: DeviceType): void {
    console.log(`📱 Device type changed: ${oldType} → ${newType}`);
    
    // Adjust UI elements based on device type
    this.adjustUIForDeviceType(newType);
  }

  private adjustUIForDeviceType(deviceType: DeviceType): void {
    // This method can be extended to adjust UI elements
    // For now, we'll just update the canvas scaling
    this.updateLayout();
  }

  private updateLayout(): void {
    try {
      const containerRect = this.container.getBoundingClientRect();
      const availableWidth = containerRect.width || this.currentScreenInfo.width;
      const availableHeight = containerRect.height || this.currentScreenInfo.height;
      
      // Calculate scale
      this.scale = this.calculateOptimalScale(availableWidth, availableHeight);
      
      // Calculate canvas size
      const canvasWidth = this.config.baseWidth * this.scale;
      const canvasHeight = this.config.baseHeight * this.scale;
      
      // Calculate offset for centering
      if (this.config.centerContent) {
        this.offset = new Vector2(
          (availableWidth - canvasWidth) / 2,
          (availableHeight - canvasHeight) / 2
        );
      } else {
        this.offset = new Vector2(0, 0);
      }
      
      // Apply to canvas
      this.applyCanvasTransform(canvasWidth, canvasHeight);
      
    } catch (error) {
      this.errorHandler.error('Layout update failed', error, 'responsive');
    }
  }

  private calculateOptimalScale(availableWidth: number, availableHeight: number): number {
    let scaleX = availableWidth / this.config.baseWidth;
    let scaleY = availableHeight / this.config.baseHeight;
    
    let scale: number;
    
    if (this.config.maintainAspectRatio) {
      // Use the smaller scale to maintain aspect ratio
      scale = Math.min(scaleX, scaleY);
    } else {
      // Use average scale (may distort aspect ratio)
      scale = (scaleX + scaleY) / 2;
    }
    
    // Clamp to min/max scale
    scale = Math.max(this.config.minScale, Math.min(this.config.maxScale, scale));
    
    return scale;
  }

  private applyCanvasTransform(width: number, height: number): void {
    // Set canvas display size
    this.canvas.style.width = `${width}px`;
    this.canvas.style.height = `${height}px`;
    this.canvas.style.position = 'absolute';
    this.canvas.style.left = `${this.offset.x}px`;
    this.canvas.style.top = `${this.offset.y}px`;
    
    // Set canvas internal resolution
    const pixelRatio = this.currentScreenInfo.devicePixelRatio;
    this.canvas.width = this.config.baseWidth * pixelRatio;
    this.canvas.height = this.config.baseHeight * pixelRatio;
    
    // Scale canvas context for high DPI displays
    const ctx = this.canvas.getContext('2d');
    if (ctx) {
      ctx.scale(pixelRatio, pixelRatio);
    }
  }

  // Coordinate conversion methods
  screenToCanvas(screenPosition: Vector2): Vector2 {
    const canvasX = (screenPosition.x - this.offset.x) / this.scale;
    const canvasY = (screenPosition.y - this.offset.y) / this.scale;
    
    return new Vector2(canvasX, canvasY);
  }

  canvasToScreen(canvasPosition: Vector2): Vector2 {
    const screenX = canvasPosition.x * this.scale + this.offset.x;
    const screenY = canvasPosition.y * this.scale + this.offset.y;
    
    return new Vector2(screenX, screenY);
  }

  // Fullscreen management
  async enterFullscreen(): Promise<boolean> {
    if (!this.config.allowFullscreen) return false;
    
    try {
      const element = this.container;
      
      if (element.requestFullscreen) {
        await element.requestFullscreen();
      } else if ((element as any).webkitRequestFullscreen) {
        await (element as any).webkitRequestFullscreen();
      } else if ((element as any).mozRequestFullScreen) {
        await (element as any).mozRequestFullScreen();
      } else if ((element as any).msRequestFullscreen) {
        await (element as any).msRequestFullscreen();
      } else {
        return false;
      }
      
      return true;
    } catch (error) {
      this.errorHandler.error('Failed to enter fullscreen', error, 'responsive');
      return false;
    }
  }

  async exitFullscreen(): Promise<boolean> {
    try {
      if (document.exitFullscreen) {
        await document.exitFullscreen();
      } else if ((document as any).webkitExitFullscreen) {
        await (document as any).webkitExitFullscreen();
      } else if ((document as any).mozCancelFullScreen) {
        await (document as any).mozCancelFullScreen();
      } else if ((document as any).msExitFullscreen) {
        await (document as any).msExitFullscreen();
      } else {
        return false;
      }
      
      return true;
    } catch (error) {
      this.errorHandler.error('Failed to exit fullscreen', error, 'responsive');
      return false;
    }
  }

  isFullscreen(): boolean {
    return !!(document.fullscreenElement || 
             (document as any).webkitFullscreenElement || 
             (document as any).mozFullScreenElement || 
             (document as any).msFullscreenElement);
  }

  async toggleFullscreen(): Promise<boolean> {
    if (this.isFullscreen()) {
      return await this.exitFullscreen();
    } else {
      return await this.enterFullscreen();
    }
  }

  // Event listeners
  addResizeListener(listener: (screenInfo: ScreenInfo) => void): void {
    this.resizeListeners.push(listener);
  }

  removeResizeListener(listener: (screenInfo: ScreenInfo) => void): void {
    const index = this.resizeListeners.indexOf(listener);
    if (index > -1) {
      this.resizeListeners.splice(index, 1);
    }
  }

  addOrientationListener(listener: (orientation: Orientation) => void): void {
    this.orientationListeners.push(listener);
  }

  removeOrientationListener(listener: (orientation: Orientation) => void): void {
    const index = this.orientationListeners.indexOf(listener);
    if (index > -1) {
      this.orientationListeners.splice(index, 1);
    }
  }

  private notifyResizeListeners(screenInfo: ScreenInfo): void {
    for (const listener of this.resizeListeners) {
      try {
        listener(screenInfo);
      } catch (error) {
        this.errorHandler.error('Resize listener failed', error, 'responsive');
      }
    }
  }

  private notifyOrientationListeners(orientation: Orientation): void {
    for (const listener of this.orientationListeners) {
      try {
        listener(orientation);
      } catch (error) {
        this.errorHandler.error('Orientation listener failed', error, 'responsive');
      }
    }
  }

  // Getters
  getScreenInfo(): ScreenInfo {
    return { ...this.currentScreenInfo };
  }

  getScale(): number {
    return this.scale;
  }

  getOffset(): Vector2 {
    return this.offset.copy();
  }

  getCanvasSize(): Vector2 {
    return new Vector2(this.config.baseWidth, this.config.baseHeight);
  }

  getDisplaySize(): Vector2 {
    return new Vector2(
      this.config.baseWidth * this.scale,
      this.config.baseHeight * this.scale
    );
  }

  // Configuration
  updateConfig(config: Partial<ResponsiveConfig>): void {
    this.config = { ...this.config, ...config };
    this.updateLayout();
  }

  getConfig(): ResponsiveConfig {
    return { ...this.config };
  }

  updateBreakpoints(breakpoints: Partial<ResponsiveBreakpoints>): void {
    this.breakpoints = { ...this.breakpoints, ...breakpoints };
    this.handleResize(); // Recalculate device type
  }

  getBreakpoints(): ResponsiveBreakpoints {
    return { ...this.breakpoints };
  }

  // Utility methods
  isMobile(): boolean {
    return this.currentScreenInfo.deviceType === 'mobile';
  }

  isTablet(): boolean {
    return this.currentScreenInfo.deviceType === 'tablet';
  }

  isDesktop(): boolean {
    return this.currentScreenInfo.deviceType === 'desktop';
  }

  isPortrait(): boolean {
    return this.currentScreenInfo.orientation === 'portrait';
  }

  isLandscape(): boolean {
    return this.currentScreenInfo.orientation === 'landscape';
  }

  isTouchDevice(): boolean {
    return this.currentScreenInfo.isTouch;
  }

  // Cleanup
  destroy(): void {
    window.removeEventListener('resize', this.handleResize.bind(this));
    window.removeEventListener('orientationchange', this.handleOrientationChange.bind(this));
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange.bind(this));
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange.bind(this));
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange.bind(this));
    document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange.bind(this));
    
    this.resizeListeners = [];
    this.orientationListeners = [];
  }
}
