import { describe, it, expect, beforeEach, vi } from 'vitest';
import { GameScene } from '@/game/scenes/GameScene';
import { InputManager } from '@/engine/input/InputManager';
import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';
import { createMockCanvas, createMockContext2D } from '../setup';

describe('GameScene Integration', () => {
  let gameScene: GameScene;
  let mockInputManager: InputManager;
  let mockRenderer: CanvasRenderer;
  let mockCanvas: HTMLCanvasElement;

  beforeEach(() => {
    // Create mock canvas and renderer
    mockCanvas = createMockCanvas() as HTMLCanvasElement;
    mockRenderer = new CanvasRenderer(mockCanvas, {
      width: 1024,
      height: 768,
      pixelRatio: 1,
      antialias: false
    });

    // Create mock input manager
    mockInputManager = new InputManager();
    
    // Create game scene
    gameScene = new GameScene();
    gameScene.setInputManager(mockInputManager);
    gameScene.initialize();
  });

  describe('initialization', () => {
    it('should initialize all game systems', () => {
      expect(gameScene).toBeDefined();
      // Scene should be properly initialized without errors
    });

    it('should setup player and enemies', () => {
      // Player should be created and positioned
      expect(gameScene['player']).toBeDefined();
      expect(gameScene['player'].position.x).toBe(512);
      expect(gameScene['player'].position.y).toBe(384);

      // Initial enemies should be spawned
      expect(gameScene['enemies']).toBeDefined();
      expect(gameScene['enemies'].length).toBeGreaterThan(0);
    });

    it('should initialize progression and inventory systems', () => {
      expect(gameScene['progressionSystem']).toBeDefined();
      expect(gameScene['inventorySystem']).toBeDefined();
      expect(gameScene['lootSystem']).toBeDefined();
      expect(gameScene['playerProgression']).toBeDefined();
    });
  });

  describe('game loop integration', () => {
    it('should handle complete update cycle', () => {
      const deltaTime = 0.016; // 60 FPS

      // Mock input state
      vi.spyOn(mockInputManager, 'isMouseButtonDown').mockReturnValue(false);
      vi.spyOn(mockInputManager, 'isActionDown').mockReturnValue(false);
      vi.spyOn(mockInputManager, 'isKeyPressed').mockReturnValue(false);
      vi.spyOn(mockInputManager, 'getMousePosition').mockReturnValue({ x: 100, y: 100 });

      // Should not throw errors during update
      expect(() => {
        gameScene.fixedUpdate(deltaTime);
        gameScene.update(deltaTime);
      }).not.toThrow();
    });

    it('should handle player shooting', () => {
      const deltaTime = 0.016;
      const initialBulletCount = gameScene['bullets'].length;

      // Mock shooting input
      vi.spyOn(mockInputManager, 'isMouseButtonDown').mockReturnValue(true);
      vi.spyOn(mockInputManager, 'isActionDown').mockReturnValue(false);
      vi.spyOn(mockInputManager, 'isKeyPressed').mockReturnValue(false);
      vi.spyOn(mockInputManager, 'getMousePosition').mockReturnValue({ x: 100, y: 100 });

      // Update game
      gameScene.fixedUpdate(deltaTime);

      // Should create bullets when shooting
      expect(gameScene['bullets'].length).toBeGreaterThan(initialBulletCount);
    });

    it('should handle enemy spawning over time', () => {
      const deltaTime = 0.016;
      const initialEnemyCount = gameScene['enemies'].length;

      // Mock no input
      vi.spyOn(mockInputManager, 'isMouseButtonDown').mockReturnValue(false);
      vi.spyOn(mockInputManager, 'isActionDown').mockReturnValue(false);
      vi.spyOn(mockInputManager, 'isKeyPressed').mockReturnValue(false);
      vi.spyOn(mockInputManager, 'getMousePosition').mockReturnValue({ x: 100, y: 100 });

      // Simulate time passing for enemy spawning
      for (let i = 0; i < 150; i++) { // ~2.4 seconds at 60 FPS
        gameScene.update(deltaTime);
      }

      // Should spawn new enemies
      expect(gameScene['enemies'].length).toBeGreaterThan(initialEnemyCount);
    });
  });

  describe('collision system integration', () => {
    it('should handle bullet-enemy collisions', () => {
      // Create a bullet near an enemy
      const bullet = gameScene['bullets'][0] || (() => {
        // Force create a bullet if none exist
        vi.spyOn(mockInputManager, 'isMouseButtonDown').mockReturnValue(true);
        gameScene.fixedUpdate(0.016);
        return gameScene['bullets'][0];
      })();

      const enemy = gameScene['enemies'][0];
      
      if (bullet && enemy) {
        // Position bullet near enemy
        bullet.getPosition().x = enemy.position.x;
        bullet.getPosition().y = enemy.position.y;
        
        const initialEnemyHealth = enemy.stats.health;
        
        // Update collision detection
        gameScene['handleCollisions']();
        
        // Enemy should take damage or bullet should be removed
        expect(enemy.stats.health < initialEnemyHealth || !bullet.isActive()).toBe(true);
      }
    });

    it('should handle player-enemy collisions', () => {
      const player = gameScene['player'];
      const enemy = gameScene['enemies'][0];
      
      if (enemy) {
        // Position enemy near player
        enemy.position.x = player.position.x;
        enemy.position.y = player.position.y;
        
        const initialPlayerHealth = player.stats.health;
        
        // Update collision detection
        gameScene['handleCollisions']();
        
        // Player should take damage
        expect(player.stats.health).toBeLessThan(initialPlayerHealth);
      }
    });
  });

  describe('progression system integration', () => {
    it('should award experience when enemy dies', () => {
      const initialLevel = gameScene['playerProgression'].level;
      const initialExperience = gameScene['playerProgression'].experience;
      
      const enemy = gameScene['enemies'][0];
      if (enemy) {
        // Kill enemy
        enemy.takeDamage(enemy.stats.health);
        
        // Handle enemy death
        gameScene['handleEnemyDeath'](enemy);
        
        // Should gain experience
        expect(gameScene['playerProgression'].experience).toBeGreaterThan(initialExperience);
      }
    });

    it('should generate loot when enemy dies', () => {
      const initialItemCount = gameScene['items'].length;
      
      const enemy = gameScene['enemies'][0];
      if (enemy) {
        // Kill enemy
        enemy.takeDamage(enemy.stats.health);
        
        // Handle enemy death
        gameScene['handleEnemyDeath'](enemy);
        
        // May generate loot (not guaranteed)
        expect(gameScene['items'].length).toBeGreaterThanOrEqual(initialItemCount);
      }
    });
  });

  describe('UI system integration', () => {
    it('should toggle skill tree', () => {
      const initialShowSkillTree = gameScene['showSkillTree'];
      
      // Mock K key press
      vi.spyOn(mockInputManager, 'isKeyPressed').mockImplementation((key) => key === 'KeyK');
      
      gameScene.fixedUpdate(0.016);
      
      expect(gameScene['showSkillTree']).toBe(!initialShowSkillTree);
    });

    it('should toggle inventory', () => {
      const initialShowInventory = gameScene['showInventory'];
      
      // Mock I key press
      vi.spyOn(mockInputManager, 'isKeyPressed').mockImplementation((key) => key === 'KeyI');
      
      gameScene.fixedUpdate(0.016);
      
      expect(gameScene['showInventory']).toBe(!initialShowInventory);
    });

    it('should skip game updates when UI is open', () => {
      // Open skill tree
      gameScene['showSkillTree'] = true;
      
      const initialEnemyCount = gameScene['enemies'].length;
      const initialBulletCount = gameScene['bullets'].length;
      
      // Mock no input
      vi.spyOn(mockInputManager, 'isKeyPressed').mockReturnValue(false);
      
      gameScene.fixedUpdate(0.016);
      
      // Game entities should not be updated
      expect(gameScene['enemies'].length).toBe(initialEnemyCount);
      expect(gameScene['bullets'].length).toBe(initialBulletCount);
    });
  });

  describe('rendering integration', () => {
    it('should render without errors', () => {
      expect(() => {
        gameScene.render(mockRenderer);
      }).not.toThrow();
    });

    it('should render UI overlays when active', () => {
      const drawTextSpy = vi.spyOn(mockRenderer, 'drawText');
      
      // Open skill tree
      gameScene['showSkillTree'] = true;
      
      gameScene.render(mockRenderer);
      
      // Should render skill tree UI
      expect(drawTextSpy).toHaveBeenCalledWith(
        expect.stringContaining('Skill Tree'),
        expect.any(Number),
        expect.any(Number),
        expect.any(String),
        expect.any(String)
      );
    });

    it('should render game over screen when player dies', () => {
      const drawTextSpy = vi.spyOn(mockRenderer, 'drawText');
      
      // Kill player
      gameScene['player'].takeDamage(gameScene['player'].stats.health);
      
      gameScene.render(mockRenderer);
      
      // Should render game over screen
      expect(drawTextSpy).toHaveBeenCalledWith(
        expect.stringContaining('GAME OVER'),
        expect.any(Number),
        expect.any(Number),
        expect.any(String),
        expect.any(String)
      );
    });
  });

  describe('game restart integration', () => {
    it('should restart game when player presses R after death', () => {
      // Kill player
      gameScene['player'].takeDamage(gameScene['player'].stats.health);
      expect(gameScene['player'].isAlive).toBe(false);
      
      // Mock R key press
      vi.spyOn(mockInputManager, 'isKeyPressed').mockImplementation((key) => key === 'KeyR');
      
      gameScene.onEnter();
      
      // Player should be alive again
      expect(gameScene['player'].isAlive).toBe(true);
      expect(gameScene['player'].stats.health).toBe(gameScene['player'].stats.maxHealth);
    });

    it('should reset game state on restart', () => {
      // Modify game state
      gameScene['score'] = 1000;
      gameScene['gameTime'] = 100;
      gameScene['bullets'].push({} as any);
      gameScene['items'].push({} as any);
      
      // Kill player and restart
      gameScene['player'].takeDamage(gameScene['player'].stats.health);
      vi.spyOn(mockInputManager, 'isKeyPressed').mockImplementation((key) => key === 'KeyR');
      
      gameScene.onEnter();
      
      // Game state should be reset
      expect(gameScene['score']).toBe(0);
      expect(gameScene['gameTime']).toBe(0);
      expect(gameScene['bullets']).toHaveLength(0);
      expect(gameScene['items']).toHaveLength(0);
    });
  });

  describe('performance integration', () => {
    it('should maintain performance with many objects', () => {
      // Add many enemies and bullets
      for (let i = 0; i < 50; i++) {
        gameScene['spawnEnemy'](Math.random() * 1000, Math.random() * 700, 'basic');
      }
      
      // Force create many bullets
      vi.spyOn(mockInputManager, 'isMouseButtonDown').mockReturnValue(true);
      for (let i = 0; i < 20; i++) {
        gameScene.fixedUpdate(0.016);
      }
      
      const startTime = performance.now();
      
      // Update game multiple times
      for (let i = 0; i < 10; i++) {
        gameScene.fixedUpdate(0.016);
        gameScene.update(0.016);
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should complete in reasonable time (less than 100ms for 10 frames)
      expect(duration).toBeLessThan(100);
      
      // Should track collision performance
      expect(gameScene['lastCollisionChecks']).toBeGreaterThan(0);
    });
  });
});
