import { describe, it, expect } from 'vitest';
import { InputValidator, ValidationRules } from '@/utils/validation/InputValidator';

describe('InputValidator', () => {
  describe('sanitizeString', () => {
    it('should remove HTML tags', () => {
      const input = '<script>alert("xss")</script>Hello';
      const result = InputValidator.sanitizeString(input);
      expect(result).toBe('scriptalert("xss")/scriptHello');
    });

    it('should remove javascript protocol', () => {
      const input = 'javascript:alert("xss")';
      const result = InputValidator.sanitizeString(input);
      expect(result).toBe('alert("xss")');
    });

    it('should remove event handlers', () => {
      const input = 'onclick=alert("xss")';
      const result = InputValidator.sanitizeString(input);
      expect(result).toBe('alert("xss")');
    });

    it('should handle non-string input', () => {
      const result = InputValidator.sanitizeString(123 as any);
      expect(result).toBe('');
    });

    it('should limit string length', () => {
      const longString = 'a'.repeat(2000);
      const result = InputValidator.sanitizeString(longString);
      expect(result.length).toBe(1000);
    });
  });

  describe('validateNumber', () => {
    it('should validate valid numbers', () => {
      const result = InputValidator.validateNumber(42);
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe(42);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate numeric strings', () => {
      const result = InputValidator.validateNumber('42.5');
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe(42.5);
    });

    it('should reject invalid input types', () => {
      const result = InputValidator.validateNumber({});
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Input must be a number or numeric string');
    });

    it('should reject NaN values', () => {
      const result = InputValidator.validateNumber('not-a-number');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Input must be a valid number');
    });

    it('should enforce minimum range', () => {
      const result = InputValidator.validateNumber(5, 10, 20);
      expect(result.isValid).toBe(false);
      expect(result.sanitizedValue).toBe(10);
      expect(result.errors).toContain('Value must be at least 10');
    });

    it('should enforce maximum range', () => {
      const result = InputValidator.validateNumber(25, 10, 20);
      expect(result.isValid).toBe(false);
      expect(result.sanitizedValue).toBe(20);
      expect(result.errors).toContain('Value must be at most 20');
    });
  });

  describe('validateGameConfig', () => {
    it('should validate valid game config', () => {
      const config = {
        canvas: {
          width: 1024,
          height: 768,
          targetFPS: 60
        },
        debug: {
          showFPS: true,
          showColliders: false,
          logPerformance: true
        }
      };

      const result = InputValidator.validateGameConfig(config);
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toEqual(config);
    });

    it('should reject non-object config', () => {
      const result = InputValidator.validateGameConfig('invalid');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Configuration must be an object');
    });

    it('should sanitize canvas dimensions', () => {
      const config = {
        canvas: {
          width: 5000, // Too large
          height: 50,  // Too small
          targetFPS: 200 // Too high
        }
      };

      const result = InputValidator.validateGameConfig(config);
      expect(result.sanitizedValue.canvas.width).toBe(4000);
      expect(result.sanitizedValue.canvas.height).toBe(100);
      expect(result.sanitizedValue.canvas.targetFPS).toBe(120);
    });
  });

  describe('validatePlayerInput', () => {
    it('should validate valid player input', () => {
      const input = {
        movement: { x: 0.5, y: -0.3 },
        actions: { shoot: true, special: false, interact: true }
      };

      const result = InputValidator.validatePlayerInput(input);
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toEqual(input);
    });

    it('should reject non-object input', () => {
      const result = InputValidator.validatePlayerInput('invalid');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Player input must be an object');
    });

    it('should clamp movement values', () => {
      const input = {
        movement: { x: 2.0, y: -1.5 },
        actions: { shoot: true }
      };

      const result = InputValidator.validatePlayerInput(input);
      expect(result.sanitizedValue.movement.x).toBe(1);
      expect(result.sanitizedValue.movement.y).toBe(-1);
    });

    it('should handle missing movement', () => {
      const input = {
        actions: { shoot: true }
      };

      const result = InputValidator.validatePlayerInput(input);
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue.actions.shoot).toBe(true);
    });
  });

  describe('validateFileData', () => {
    it('should validate valid file data', () => {
      const fileData = {
        name: 'test.png',
        type: 'image/png',
        size: 1024
      };

      const result = InputValidator.validateFileData(fileData, ['image/png', 'image/jpeg']);
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue.name).toBe('test.png');
    });

    it('should reject files that are too large', () => {
      const fileData = {
        name: 'large.png',
        type: 'image/png',
        size: 20 * 1024 * 1024 // 20MB
      };

      const result = InputValidator.validateFileData(fileData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File size must be less than 10MB');
    });

    it('should reject invalid file types', () => {
      const fileData = {
        name: 'script.js',
        type: 'application/javascript',
        size: 1024
      };

      const result = InputValidator.validateFileData(fileData, ['image/png']);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File type must be one of: image/png');
    });

    it('should sanitize filename', () => {
      const fileData = {
        name: '<script>alert("xss")</script>test.png',
        type: 'image/png',
        size: 1024
      };

      const result = InputValidator.validateFileData(fileData);
      expect(result.sanitizedValue.name).toBe('scriptalert(_xss_)_scripttest.png');
    });
  });

  describe('ValidationRules', () => {
    it('should validate required fields', () => {
      const rule = ValidationRules.required();
      expect(rule.validate('test')).toBe(true);
      expect(rule.validate('')).toBe(false);
      expect(rule.validate(null)).toBe(false);
      expect(rule.validate(undefined)).toBe(false);
    });

    it('should validate minimum length', () => {
      const rule = ValidationRules.minLength(5);
      expect(rule.validate('hello')).toBe(true);
      expect(rule.validate('hi')).toBe(false);
    });

    it('should validate maximum length and sanitize', () => {
      const rule = ValidationRules.maxLength(5);
      expect(rule.validate('hello')).toBe(true);
      expect(rule.validate('hello world')).toBe(false);
      
      if (rule.sanitize) {
        expect(rule.sanitize('hello world')).toBe('hello');
      }
    });

    it('should validate number ranges', () => {
      const rule = ValidationRules.range(1, 10);
      expect(rule.validate(5)).toBe(true);
      expect(rule.validate(0)).toBe(false);
      expect(rule.validate(15)).toBe(false);
      
      if (rule.sanitize) {
        expect(rule.sanitize(0)).toBe(1);
        expect(rule.sanitize(15)).toBe(10);
      }
    });

    it('should validate patterns', () => {
      const rule = ValidationRules.pattern(/^[a-zA-Z]+$/);
      expect(rule.validate('hello')).toBe(true);
      expect(rule.validate('hello123')).toBe(false);
    });
  });

  describe('createValidator', () => {
    it('should chain multiple validation rules', () => {
      const validator = InputValidator.createValidator(
        ValidationRules.required(),
        ValidationRules.minLength(3),
        ValidationRules.maxLength(10)
      );

      const result1 = validator('hello');
      expect(result1.isValid).toBe(true);

      const result2 = validator('hi');
      expect(result2.isValid).toBe(false);
      expect(result2.errors).toContain('Minimum length is 3 characters');

      const result3 = validator('this is too long');
      expect(result3.isValid).toBe(false);
      expect(result3.sanitizedValue).toBe('this is to');
    });
  });
});
