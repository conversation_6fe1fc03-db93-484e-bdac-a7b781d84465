/**
 * Collision detection and response system
 */

import { Vector2 } from '@/utils/math/Vector2';

// Reusable Vector2 instances to avoid allocations in hot paths
const tempVector1 = new Vector2(0, 0);
const tempVector2 = new Vector2(0, 0);
const tempVector3 = new Vector2(0, 0);

export interface Collider {
  position: Vector2;
  size?: number;
  collisionRadius?: number;
  bounds?: { x: number; y: number; width: number; height: number };
  type: 'circle' | 'rectangle';
}

export interface CollisionResult {
  hasCollision: boolean;
  distance?: number;
  overlap?: number;
  normal?: Vector2;
  point?: Vector2;
}

export class CollisionSystem {
  
  /**
   * Check collision between two circular colliders
   */
  static checkCircleCollision(a: Collider, b: Collider): CollisionResult {
    const radiusA = a.collisionRadius || a.size || 16;
    const radiusB = b.collisionRadius || b.size || 16;
    
    const distance = a.position.distance(b.position);
    const minDistance = radiusA + radiusB;
    
    if (distance <= minDistance) {
      const overlap = minDistance - distance;

      // Calculate normal using temp vector
      tempVector1.set(
        b.position.x - a.position.x,
        b.position.y - a.position.y
      ).normalize();

      // Calculate collision point using temp vector
      tempVector2.set(a.position.x, a.position.y).add(tempVector1.copy().multiply(radiusA));
      
      return {
        hasCollision: true,
        distance,
        overlap,
        normal: tempVector1.copy(), // Create copy for return
        point: tempVector2.copy()   // Create copy for return
      };
    }
    
    return { hasCollision: false };
  }

  /**
   * Check collision between two rectangular colliders
   */
  static checkRectangleCollision(a: Collider, b: Collider): CollisionResult {
    if (!a.bounds || !b.bounds) {
      return { hasCollision: false };
    }
    
    const aLeft = a.bounds.x;
    const aRight = a.bounds.x + a.bounds.width;
    const aTop = a.bounds.y;
    const aBottom = a.bounds.y + a.bounds.height;
    
    const bLeft = b.bounds.x;
    const bRight = b.bounds.x + b.bounds.width;
    const bTop = b.bounds.y;
    const bBottom = b.bounds.y + b.bounds.height;
    
    const hasCollision = !(aRight < bLeft || aLeft > bRight || aBottom < bTop || aTop > bBottom);
    
    if (hasCollision) {
      // Calculate overlap
      const overlapX = Math.min(aRight - bLeft, bRight - aLeft);
      const overlapY = Math.min(aBottom - bTop, bBottom - aTop);
      const overlap = Math.min(overlapX, overlapY);
      
      // Calculate normal (direction to separate) - reuse temp vector
      tempVector1.set(0, 0);
      if (overlapX < overlapY) {
        tempVector1.x = a.position.x < b.position.x ? -1 : 1;
      } else {
        tempVector1.y = a.position.y < b.position.y ? -1 : 1;
      }

      // Calculate collision point - reuse temp vector
      tempVector2.set(
        Math.max(aLeft, bLeft) + Math.min(aRight, bRight) / 2,
        Math.max(aTop, bTop) + Math.min(aBottom, bBottom) / 2
      );
      
      return {
        hasCollision: true,
        overlap,
        normal: tempVector1.copy(), // Create a copy for return
        point: tempVector2.copy()   // Create a copy for return
      };
    }
    
    return { hasCollision: false };
  }

  /**
   * Check collision between circle and rectangle
   */
  static checkCircleRectangleCollision(circle: Collider, rect: Collider): CollisionResult {
    if (!rect.bounds) {
      return { hasCollision: false };
    }
    
    const radius = circle.collisionRadius || circle.size || 16;
    
    // Find closest point on rectangle to circle center
    const closestX = Math.max(rect.bounds.x, Math.min(circle.position.x, rect.bounds.x + rect.bounds.width));
    const closestY = Math.max(rect.bounds.y, Math.min(circle.position.y, rect.bounds.y + rect.bounds.height));
    
    // Use temp vector for closest point
    tempVector1.set(closestX, closestY);
    const distance = circle.position.distance(tempVector1);

    if (distance <= radius) {
      const overlap = radius - distance;

      // Calculate normal using temp vector
      tempVector2.set(
        circle.position.x - closestX,
        circle.position.y - closestY
      ).normalize();

      // Point is the closest point
      tempVector3.set(closestX, closestY);
      
      return {
        hasCollision: true,
        distance,
        overlap,
        normal: tempVector2.copy(), // Create copy for return
        point: tempVector3.copy()   // Create copy for return
      };
    }
    
    return { hasCollision: false };
  }

  /**
   * Generic collision check that determines types automatically
   */
  static checkCollision(a: Collider, b: Collider): CollisionResult {
    if (a.type === 'circle' && b.type === 'circle') {
      return this.checkCircleCollision(a, b);
    } else if (a.type === 'rectangle' && b.type === 'rectangle') {
      return this.checkRectangleCollision(a, b);
    } else if (a.type === 'circle' && b.type === 'rectangle') {
      return this.checkCircleRectangleCollision(a, b);
    } else if (a.type === 'rectangle' && b.type === 'circle') {
      const result = this.checkCircleRectangleCollision(b, a);
      if (result.normal) {
        result.normal = result.normal.multiply(-1); // Flip normal
      }
      return result;
    }
    
    return { hasCollision: false };
  }

  /**
   * Check if a point is inside a circle
   */
  static pointInCircle(point: Vector2, circle: Collider): boolean {
    const radius = circle.collisionRadius || circle.size || 16;
    return point.distance(circle.position) <= radius;
  }

  /**
   * Check if a point is inside a rectangle
   */
  static pointInRectangle(point: Vector2, rect: Collider): boolean {
    if (!rect.bounds) return false;
    
    return point.x >= rect.bounds.x &&
           point.x <= rect.bounds.x + rect.bounds.width &&
           point.y >= rect.bounds.y &&
           point.y <= rect.bounds.y + rect.bounds.height;
  }

  /**
   * Get all objects within a certain radius of a point
   */
  static getObjectsInRadius<T extends { position: Vector2 }>(
    center: Vector2,
    radius: number,
    objects: T[]
  ): T[] {
    return objects.filter(obj => center.distance(obj.position) <= radius);
  }

  /**
   * Get the closest object to a point
   */
  static getClosestObject<T extends { position: Vector2 }>(
    center: Vector2,
    objects: T[]
  ): { object: T; distance: number } | null {
    if (objects.length === 0) return null;
    
    let closest = objects[0];
    let closestDistance = center.distance(closest.position);
    
    for (let i = 1; i < objects.length; i++) {
      const distance = center.distance(objects[i].position);
      if (distance < closestDistance) {
        closest = objects[i];
        closestDistance = distance;
      }
    }
    
    return { object: closest, distance: closestDistance };
  }

  /**
   * Separate two overlapping circular objects
   */
  static separateCircles(a: Collider, b: Collider, result: CollisionResult): void {
    if (!result.hasCollision || !result.normal || !result.overlap) return;
    
    const separation = result.normal.multiply(result.overlap / 2);
    
    // Move objects apart
    a.position = a.position.subtract(separation);
    b.position = b.position.add(separation);
  }

  /**
   * Resolve collision with bounce effect
   */
  static resolveCollisionWithBounce(
    a: { position: Vector2; velocity: Vector2; mass?: number },
    b: { position: Vector2; velocity: Vector2; mass?: number },
    result: CollisionResult,
    restitution: number = 0.8
  ): void {
    if (!result.hasCollision || !result.normal) return;
    
    const massA = a.mass || 1;
    const massB = b.mass || 1;
    const totalMass = massA + massB;
    
    // Relative velocity
    const relativeVelocity = a.velocity.subtract(b.velocity);
    const velocityAlongNormal = relativeVelocity.dot(result.normal);
    
    // Don't resolve if velocities are separating
    if (velocityAlongNormal > 0) return;
    
    // Calculate impulse
    const impulse = -(1 + restitution) * velocityAlongNormal / totalMass;
    const impulseVector = result.normal.multiply(impulse);
    
    // Apply impulse
    a.velocity = a.velocity.add(impulseVector.multiply(massB));
    b.velocity = b.velocity.subtract(impulseVector.multiply(massA));
  }

  /**
   * Simple spatial partitioning for broad-phase collision detection
   */
  static spatialHash<T extends { position: Vector2 }>(
    objects: T[],
    cellSize: number = 64
  ): Map<string, T[]> {
    const grid = new Map<string, T[]>();
    
    for (const obj of objects) {
      const cellX = Math.floor(obj.position.x / cellSize);
      const cellY = Math.floor(obj.position.y / cellSize);
      const key = `${cellX},${cellY}`;
      
      if (!grid.has(key)) {
        grid.set(key, []);
      }
      grid.get(key)!.push(obj);
    }
    
    return grid;
  }

  /**
   * Get potential collision pairs using spatial hashing
   */
  static getPotentialCollisionPairs<T extends { position: Vector2 }>(
    objects: T[],
    cellSize: number = 64
  ): Array<[T, T]> {
    const grid = this.spatialHash(objects, cellSize);
    const pairs: Array<[T, T]> = [];
    const checked = new Set<string>();
    
    for (const cell of grid.values()) {
      for (let i = 0; i < cell.length; i++) {
        for (let j = i + 1; j < cell.length; j++) {
          const objA = cell[i];
          const objB = cell[j];
          const pairKey = `${objA}:${objB}`;
          
          if (!checked.has(pairKey)) {
            pairs.push([objA, objB]);
            checked.add(pairKey);
          }
        }
      }
    }
    
    return pairs;
  }
}
