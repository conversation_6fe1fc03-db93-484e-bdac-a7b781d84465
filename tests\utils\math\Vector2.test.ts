import { describe, it, expect } from 'vitest';
import { Vector2 } from '@/utils/math/Vector2';

describe('Vector2', () => {
  describe('constructor', () => {
    it('should create vector with given coordinates', () => {
      const v = new Vector2(3, 4);
      expect(v.x).toBe(3);
      expect(v.y).toBe(4);
    });

    it('should create zero vector by default', () => {
      const v = new Vector2();
      expect(v.x).toBe(0);
      expect(v.y).toBe(0);
    });
  });

  describe('set', () => {
    it('should set coordinates and return self', () => {
      const v = new Vector2();
      const result = v.set(5, 6);
      
      expect(v.x).toBe(5);
      expect(v.y).toBe(6);
      expect(result).toBe(v); // Should return self for chaining
    });
  });

  describe('copy', () => {
    it('should create a new vector with same coordinates', () => {
      const v1 = new Vector2(3, 4);
      const v2 = v1.copy();
      
      expect(v2.x).toBe(3);
      expect(v2.y).toBe(4);
      expect(v2).not.toBe(v1); // Should be different objects
    });
  });

  describe('add', () => {
    it('should add another vector', () => {
      const v1 = new Vector2(3, 4);
      const v2 = new Vector2(1, 2);
      const result = v1.add(v2);
      
      expect(result.x).toBe(4);
      expect(result.y).toBe(6);
      expect(result).toBe(v1); // Should modify original vector
    });

    it('should handle zero vector addition', () => {
      const v1 = new Vector2(3, 4);
      const v2 = new Vector2(0, 0);
      const result = v1.add(v2);
      
      expect(result.x).toBe(3);
      expect(result.y).toBe(4);
    });
  });

  describe('subtract', () => {
    it('should subtract another vector', () => {
      const v1 = new Vector2(5, 7);
      const v2 = new Vector2(2, 3);
      const result = v1.subtract(v2);
      
      expect(result.x).toBe(3);
      expect(result.y).toBe(4);
      expect(result).toBe(v1); // Should modify original vector
    });
  });

  describe('multiply', () => {
    it('should multiply by scalar', () => {
      const v = new Vector2(3, 4);
      const result = v.multiply(2);
      
      expect(result.x).toBe(6);
      expect(result.y).toBe(8);
      expect(result).toBe(v); // Should modify original vector
    });

    it('should handle zero multiplication', () => {
      const v = new Vector2(3, 4);
      const result = v.multiply(0);
      
      expect(result.x).toBe(0);
      expect(result.y).toBe(0);
    });

    it('should handle negative multiplication', () => {
      const v = new Vector2(3, 4);
      const result = v.multiply(-1);
      
      expect(result.x).toBe(-3);
      expect(result.y).toBe(-4);
    });
  });

  describe('divide', () => {
    it('should divide by scalar', () => {
      const v = new Vector2(6, 8);
      const result = v.divide(2);
      
      expect(result.x).toBe(3);
      expect(result.y).toBe(4);
      expect(result).toBe(v); // Should modify original vector
    });

    it('should handle division by 1', () => {
      const v = new Vector2(3, 4);
      const result = v.divide(1);
      
      expect(result.x).toBe(3);
      expect(result.y).toBe(4);
    });

    it('should handle division by zero', () => {
      const v = new Vector2(3, 4);
      const result = v.divide(0);
      
      expect(result.x).toBe(0);
      expect(result.y).toBe(0);
    });
  });

  describe('magnitude', () => {
    it('should calculate correct magnitude', () => {
      const v = new Vector2(3, 4);
      expect(v.magnitude()).toBe(5); // 3-4-5 triangle
    });

    it('should return 0 for zero vector', () => {
      const v = new Vector2(0, 0);
      expect(v.magnitude()).toBe(0);
    });

    it('should handle negative coordinates', () => {
      const v = new Vector2(-3, -4);
      expect(v.magnitude()).toBe(5);
    });
  });

  describe('magnitudeSquared', () => {
    it('should calculate squared magnitude', () => {
      const v = new Vector2(3, 4);
      expect(v.magnitudeSquared()).toBe(25); // 3² + 4² = 25
    });

    it('should be more efficient than magnitude for comparisons', () => {
      const v = new Vector2(3, 4);
      expect(v.magnitudeSquared()).toBe(v.magnitude() ** 2);
    });
  });

  describe('normalize', () => {
    it('should normalize vector to unit length', () => {
      const v = new Vector2(3, 4);
      const result = v.normalize();
      
      expect(result.magnitude()).toBeCloseTo(1, 5);
      expect(result).toBe(v); // Should modify original vector
    });

    it('should handle zero vector', () => {
      const v = new Vector2(0, 0);
      const result = v.normalize();
      
      expect(result.x).toBe(0);
      expect(result.y).toBe(0);
    });

    it('should preserve direction', () => {
      const v = new Vector2(6, 8);
      const originalRatio = v.y / v.x;
      v.normalize();
      const newRatio = v.y / v.x;
      
      expect(newRatio).toBeCloseTo(originalRatio, 5);
    });
  });

  describe('distance', () => {
    it('should calculate distance between vectors', () => {
      const v1 = new Vector2(0, 0);
      const v2 = new Vector2(3, 4);
      
      expect(v1.distance(v2)).toBe(5);
      expect(v2.distance(v1)).toBe(5); // Should be symmetric
    });

    it('should return 0 for same vector', () => {
      const v1 = new Vector2(3, 4);
      const v2 = new Vector2(3, 4);
      
      expect(v1.distance(v2)).toBe(0);
    });
  });

  describe('distanceSquared', () => {
    it('should calculate squared distance', () => {
      const v1 = new Vector2(0, 0);
      const v2 = new Vector2(3, 4);
      
      expect(v1.distanceSquared(v2)).toBe(25);
    });

    it('should be more efficient than distance for comparisons', () => {
      const v1 = new Vector2(0, 0);
      const v2 = new Vector2(3, 4);
      
      expect(v1.distanceSquared(v2)).toBe(v1.distance(v2) ** 2);
    });
  });

  describe('dot', () => {
    it('should calculate dot product', () => {
      const v1 = new Vector2(2, 3);
      const v2 = new Vector2(4, 5);
      
      expect(v1.dot(v2)).toBe(23); // 2*4 + 3*5 = 23
    });

    it('should return 0 for perpendicular vectors', () => {
      const v1 = new Vector2(1, 0);
      const v2 = new Vector2(0, 1);
      
      expect(v1.dot(v2)).toBe(0);
    });

    it('should be commutative', () => {
      const v1 = new Vector2(2, 3);
      const v2 = new Vector2(4, 5);
      
      expect(v1.dot(v2)).toBe(v2.dot(v1));
    });
  });

  describe('cross', () => {
    it('should calculate 2D cross product (z-component)', () => {
      const v1 = new Vector2(2, 3);
      const v2 = new Vector2(4, 5);
      
      expect(v1.cross(v2)).toBe(-2); // 2*5 - 3*4 = -2
    });

    it('should return 0 for parallel vectors', () => {
      const v1 = new Vector2(2, 3);
      const v2 = new Vector2(4, 6); // Parallel to v1
      
      expect(v1.cross(v2)).toBe(0);
    });

    it('should be anti-commutative', () => {
      const v1 = new Vector2(2, 3);
      const v2 = new Vector2(4, 5);
      
      expect(v1.cross(v2)).toBe(-v2.cross(v1));
    });
  });

  describe('angle', () => {
    it('should calculate angle in radians', () => {
      const v = new Vector2(1, 0);
      expect(v.angle()).toBe(0);
      
      const v2 = new Vector2(0, 1);
      expect(v2.angle()).toBeCloseTo(Math.PI / 2, 5);
      
      const v3 = new Vector2(-1, 0);
      expect(v3.angle()).toBeCloseTo(Math.PI, 5);
    });

    it('should handle zero vector', () => {
      const v = new Vector2(0, 0);
      expect(v.angle()).toBe(0);
    });
  });

  describe('rotate', () => {
    it('should rotate vector by given angle', () => {
      const v = new Vector2(1, 0);
      const result = v.rotate(Math.PI / 2); // 90 degrees
      
      expect(result.x).toBeCloseTo(0, 5);
      expect(result.y).toBeCloseTo(1, 5);
      expect(result).toBe(v); // Should modify original vector
    });

    it('should preserve magnitude', () => {
      const v = new Vector2(3, 4);
      const originalMagnitude = v.magnitude();
      v.rotate(Math.PI / 4); // 45 degrees
      
      expect(v.magnitude()).toBeCloseTo(originalMagnitude, 5);
    });
  });

  describe('lerp', () => {
    it('should interpolate between vectors', () => {
      const v1 = new Vector2(0, 0);
      const v2 = new Vector2(10, 10);
      const result = v1.lerp(v2, 0.5);
      
      expect(result.x).toBe(5);
      expect(result.y).toBe(5);
      expect(result).toBe(v1); // Should modify original vector
    });

    it('should return start vector at t=0', () => {
      const v1 = new Vector2(2, 3);
      const v2 = new Vector2(8, 9);
      const result = v1.copy().lerp(v2, 0);
      
      expect(result.x).toBe(2);
      expect(result.y).toBe(3);
    });

    it('should return end vector at t=1', () => {
      const v1 = new Vector2(2, 3);
      const v2 = new Vector2(8, 9);
      const result = v1.copy().lerp(v2, 1);
      
      expect(result.x).toBe(8);
      expect(result.y).toBe(9);
    });
  });

  describe('equals', () => {
    it('should return true for equal vectors', () => {
      const v1 = new Vector2(3, 4);
      const v2 = new Vector2(3, 4);
      
      expect(v1.equals(v2)).toBe(true);
    });

    it('should return false for different vectors', () => {
      const v1 = new Vector2(3, 4);
      const v2 = new Vector2(3, 5);
      
      expect(v1.equals(v2)).toBe(false);
    });

    it('should handle floating point precision', () => {
      const v1 = new Vector2(0.1 + 0.2, 0.4);
      const v2 = new Vector2(0.3, 0.4);
      
      expect(v1.equals(v2, 1e-10)).toBe(true);
    });
  });

  describe('toString', () => {
    it('should return string representation', () => {
      const v = new Vector2(3, 4);
      expect(v.toString()).toBe('Vector2(3, 4)');
    });

    it('should handle negative numbers', () => {
      const v = new Vector2(-3, -4);
      expect(v.toString()).toBe('Vector2(-3, -4)');
    });

    it('should handle floating point numbers', () => {
      const v = new Vector2(3.14, 2.71);
      expect(v.toString()).toBe('Vector2(3.14, 2.71)');
    });
  });

  describe('static methods', () => {
    describe('zero', () => {
      it('should return zero vector', () => {
        const v = Vector2.zero();
        expect(v.x).toBe(0);
        expect(v.y).toBe(0);
      });
    });

    describe('one', () => {
      it('should return unit vector', () => {
        const v = Vector2.one();
        expect(v.x).toBe(1);
        expect(v.y).toBe(1);
      });
    });

    describe('up', () => {
      it('should return up vector', () => {
        const v = Vector2.up();
        expect(v.x).toBe(0);
        expect(v.y).toBe(1);
      });
    });

    describe('down', () => {
      it('should return down vector', () => {
        const v = Vector2.down();
        expect(v.x).toBe(0);
        expect(v.y).toBe(-1);
      });
    });

    describe('left', () => {
      it('should return left vector', () => {
        const v = Vector2.left();
        expect(v.x).toBe(-1);
        expect(v.y).toBe(0);
      });
    });

    describe('right', () => {
      it('should return right vector', () => {
        const v = Vector2.right();
        expect(v.x).toBe(1);
        expect(v.y).toBe(0);
      });
    });

    describe('fromAngle', () => {
      it('should create vector from angle', () => {
        const v = Vector2.fromAngle(0);
        expect(v.x).toBeCloseTo(1, 5);
        expect(v.y).toBeCloseTo(0, 5);
        
        const v2 = Vector2.fromAngle(Math.PI / 2);
        expect(v2.x).toBeCloseTo(0, 5);
        expect(v2.y).toBeCloseTo(1, 5);
      });

      it('should create vector with specified magnitude', () => {
        const v = Vector2.fromAngle(0, 5);
        expect(v.x).toBeCloseTo(5, 5);
        expect(v.y).toBeCloseTo(0, 5);
        expect(v.magnitude()).toBeCloseTo(5, 5);
      });
    });

    describe('random', () => {
      it('should create random unit vector', () => {
        const v = Vector2.random();
        expect(v.magnitude()).toBeCloseTo(1, 5);
      });

      it('should create random vector with specified magnitude', () => {
        const v = Vector2.random(5);
        expect(v.magnitude()).toBeCloseTo(5, 5);
      });
    });
  });
});
