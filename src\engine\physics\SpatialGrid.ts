/**
 * Spatial partitioning system for efficient collision detection
 * Uses a grid-based approach to reduce O(n²) collision checks
 */

export interface SpatialObject {
  position: { x: number; y: number };
  collisionRadius: number;
  id?: string | number;
}

export class SpatialGrid {
  private cellSize: number;
  private grid: Map<string, SpatialObject[]> = new Map();
  private objectCells: Map<SpatialObject, string[]> = new Map();

  constructor(cellSize: number = 64) {
    this.cellSize = cellSize;
  }

  clear(): void {
    this.grid.clear();
    this.objectCells.clear();
  }

  private getCellKey(x: number, y: number): string {
    const cellX = Math.floor(x / this.cellSize);
    const cellY = Math.floor(y / this.cellSize);
    return `${cellX},${cellY}`;
  }

  private getCellsForObject(obj: SpatialObject): string[] {
    const cells: string[] = [];
    const radius = obj.collisionRadius;
    
    // Calculate the range of cells this object spans
    const minX = Math.floor((obj.position.x - radius) / this.cellSize);
    const maxX = Math.floor((obj.position.x + radius) / this.cellSize);
    const minY = Math.floor((obj.position.y - radius) / this.cellSize);
    const maxY = Math.floor((obj.position.y + radius) / this.cellSize);

    for (let x = minX; x <= maxX; x++) {
      for (let y = minY; y <= maxY; y++) {
        cells.push(`${x},${y}`);
      }
    }

    return cells;
  }

  insert(obj: SpatialObject): void {
    const cells = this.getCellsForObject(obj);
    this.objectCells.set(obj, cells);

    for (const cellKey of cells) {
      if (!this.grid.has(cellKey)) {
        this.grid.set(cellKey, []);
      }
      this.grid.get(cellKey)!.push(obj);
    }
  }

  remove(obj: SpatialObject): void {
    const cells = this.objectCells.get(obj);
    if (!cells) return;

    for (const cellKey of cells) {
      const cellObjects = this.grid.get(cellKey);
      if (cellObjects) {
        const index = cellObjects.indexOf(obj);
        if (index !== -1) {
          cellObjects.splice(index, 1);
        }
        
        // Remove empty cells
        if (cellObjects.length === 0) {
          this.grid.delete(cellKey);
        }
      }
    }

    this.objectCells.delete(obj);
  }

  update(obj: SpatialObject): void {
    this.remove(obj);
    this.insert(obj);
  }

  getNearbyObjects(obj: SpatialObject): SpatialObject[] {
    const nearby: Set<SpatialObject> = new Set();
    const cells = this.getCellsForObject(obj);

    for (const cellKey of cells) {
      const cellObjects = this.grid.get(cellKey);
      if (cellObjects) {
        for (const other of cellObjects) {
          if (other !== obj) {
            nearby.add(other);
          }
        }
      }
    }

    return Array.from(nearby);
  }

  getObjectsInRadius(center: { x: number; y: number }, radius: number): SpatialObject[] {
    const tempObj: SpatialObject = {
      position: center,
      collisionRadius: radius
    };

    return this.getNearbyObjects(tempObj);
  }

  getStats(): {
    totalCells: number;
    totalObjects: number;
    averageObjectsPerCell: number;
    maxObjectsInCell: number;
  } {
    const totalCells = this.grid.size;
    const totalObjects = this.objectCells.size;
    
    let maxObjectsInCell = 0;
    let totalObjectsInCells = 0;

    for (const cellObjects of this.grid.values()) {
      const count = cellObjects.length;
      maxObjectsInCell = Math.max(maxObjectsInCell, count);
      totalObjectsInCells += count;
    }

    return {
      totalCells,
      totalObjects,
      averageObjectsPerCell: totalCells > 0 ? totalObjectsInCells / totalCells : 0,
      maxObjectsInCell
    };
  }

  // Debug visualization
  getCellBounds(cellKey: string): { x: number; y: number; width: number; height: number } {
    const [cellX, cellY] = cellKey.split(',').map(Number);
    return {
      x: cellX * this.cellSize,
      y: cellY * this.cellSize,
      width: this.cellSize,
      height: this.cellSize
    };
  }

  getAllCells(): string[] {
    return Array.from(this.grid.keys());
  }
}

/**
 * Optimized collision manager using spatial partitioning
 */
export class OptimizedCollisionManager {
  private spatialGrid: SpatialGrid;
  private collisionChecks: number = 0;

  constructor(cellSize: number = 64) {
    this.spatialGrid = new SpatialGrid(cellSize);
  }

  clear(): void {
    this.spatialGrid.clear();
    this.collisionChecks = 0;
  }

  checkCollisions<T extends SpatialObject, U extends SpatialObject>(
    group1: T[],
    group2: U[],
    collisionCallback: (obj1: T, obj2: U) => void
  ): void {
    // Clear and rebuild spatial grid
    this.spatialGrid.clear();
    
    // Insert all objects from group2 into spatial grid
    for (const obj of group2) {
      this.spatialGrid.insert(obj);
    }

    // Check each object in group1 against nearby objects in group2
    for (const obj1 of group1) {
      const nearby = this.spatialGrid.getNearbyObjects(obj1) as U[];
      
      for (const obj2 of nearby) {
        this.collisionChecks++;
        
        // Simple circle collision check
        const dx = obj1.position.x - obj2.position.x;
        const dy = obj1.position.y - obj2.position.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const minDistance = obj1.collisionRadius + obj2.collisionRadius;

        if (distance < minDistance) {
          collisionCallback(obj1, obj2);
        }
      }
    }
  }

  getCollisionChecks(): number {
    return this.collisionChecks;
  }

  resetCollisionChecks(): void {
    this.collisionChecks = 0;
  }

  getStats() {
    return {
      ...this.spatialGrid.getStats(),
      collisionChecks: this.collisionChecks
    };
  }
}
