/**
 * 2D Vector utility class for mathematical operations
 */

export class Vector2 {
  public x: number;
  public y: number;

  constructor(x: number = 0, y: number = 0) {
    this.x = x;
    this.y = y;
  }

  // Static factory methods
  static zero(): Vector2 {
    return new Vector2(0, 0);
  }

  static one(): Vector2 {
    return new Vector2(1, 1);
  }

  static up(): Vector2 {
    return new Vector2(0, -1);
  }

  static down(): Vector2 {
    return new Vector2(0, 1);
  }

  static left(): Vector2 {
    return new Vector2(-1, 0);
  }

  static right(): Vector2 {
    return new Vector2(1, 0);
  }

  static fromAngle(angle: number, magnitude: number = 1): Vector2 {
    return new Vector2(
      Math.cos(angle) * magnitude,
      Math.sin(angle) * magnitude
    );
  }

  static random(): Vector2 {
    const angle = Math.random() * Math.PI * 2;
    return Vector2.fromAngle(angle);
  }

  // Basic operations
  add(other: Vector2): Vector2 {
    return new Vector2(this.x + other.x, this.y + other.y);
  }

  subtract(other: Vector2): Vector2 {
    return new Vector2(this.x - other.x, this.y - other.y);
  }

  multiply(scalar: number): Vector2 {
    return new Vector2(this.x * scalar, this.y * scalar);
  }

  divide(scalar: number): Vector2 {
    if (scalar === 0) {
      console.warn('Division by zero in Vector2.divide');
      return new Vector2(0, 0);
    }
    return new Vector2(this.x / scalar, this.y / scalar);
  }

  // Vector operations
  dot(other: Vector2): number {
    return this.x * other.x + this.y * other.y;
  }

  cross(other: Vector2): number {
    return this.x * other.y - this.y * other.x;
  }

  // Magnitude operations
  magnitude(): number {
    return Math.sqrt(this.x * this.x + this.y * this.y);
  }

  magnitudeSquared(): number {
    return this.x * this.x + this.y * this.y;
  }

  normalize(): Vector2 {
    const mag = this.magnitude();
    if (mag === 0) {
      return new Vector2(0, 0);
    }
    return this.divide(mag);
  }

  setMagnitude(magnitude: number): Vector2 {
    return this.normalize().multiply(magnitude);
  }

  limit(maxMagnitude: number): Vector2 {
    const mag = this.magnitude();
    if (mag > maxMagnitude) {
      return this.setMagnitude(maxMagnitude);
    }
    return this.copy();
  }

  // Distance operations
  distance(other: Vector2): number {
    const dx = this.x - other.x;
    const dy = this.y - other.y;
    return Math.sqrt(dx * dx + dy * dy);
  }

  distanceSquared(other: Vector2): number {
    const dx = this.x - other.x;
    const dy = this.y - other.y;
    return dx * dx + dy * dy;
  }

  // Angle operations
  angle(): number {
    return Math.atan2(this.y, this.x);
  }

  angleTo(other: Vector2): number {
    return Math.atan2(other.y - this.y, other.x - this.x);
  }

  rotate(angle: number): Vector2 {
    const cos = Math.cos(angle);
    const sin = Math.sin(angle);
    return new Vector2(
      this.x * cos - this.y * sin,
      this.x * sin + this.y * cos
    );
  }

  // Interpolation
  lerp(other: Vector2, t: number): Vector2 {
    t = Math.max(0, Math.min(1, t)); // Clamp t between 0 and 1
    return new Vector2(
      this.x + (other.x - this.x) * t,
      this.y + (other.y - this.y) * t
    );
  }

  slerp(other: Vector2, t: number): Vector2 {
    // Spherical linear interpolation
    const dot = this.normalize().dot(other.normalize());
    const theta = Math.acos(Math.max(-1, Math.min(1, dot))) * t;
    
    const relative = other.subtract(this.multiply(dot)).normalize();
    return this.multiply(Math.cos(theta)).add(relative.multiply(Math.sin(theta)));
  }

  // Utility methods
  copy(): Vector2 {
    return new Vector2(this.x, this.y);
  }

  clone(): Vector2 {
    return this.copy();
  }

  set(x: number, y: number): Vector2 {
    this.x = x;
    this.y = y;
    return this;
  }

  setFrom(other: Vector2): Vector2 {
    this.x = other.x;
    this.y = other.y;
    return this;
  }

  equals(other: Vector2, tolerance: number = 0.0001): boolean {
    return Math.abs(this.x - other.x) < tolerance && 
           Math.abs(this.y - other.y) < tolerance;
  }

  isZero(tolerance: number = 0.0001): boolean {
    return this.magnitude() < tolerance;
  }

  // Reflection
  reflect(normal: Vector2): Vector2 {
    const normalizedNormal = normal.normalize();
    return this.subtract(normalizedNormal.multiply(2 * this.dot(normalizedNormal)));
  }

  // Projection
  project(onto: Vector2): Vector2 {
    const ontoNormalized = onto.normalize();
    return ontoNormalized.multiply(this.dot(ontoNormalized));
  }

  // String representation
  toString(): string {
    return `Vector2(${this.x}, ${this.y})`;
  }

  toArray(): [number, number] {
    return [this.x, this.y];
  }

  toObject(): { x: number; y: number } {
    return { x: this.x, y: this.y };
  }

  // Static utility methods
  static distance(a: Vector2, b: Vector2): number {
    return a.distance(b);
  }

  static lerp(a: Vector2, b: Vector2, t: number): Vector2 {
    return a.lerp(b, t);
  }

  static min(a: Vector2, b: Vector2): Vector2 {
    return new Vector2(Math.min(a.x, b.x), Math.min(a.y, b.y));
  }

  static max(a: Vector2, b: Vector2): Vector2 {
    return new Vector2(Math.max(a.x, b.x), Math.max(a.y, b.y));
  }
}
