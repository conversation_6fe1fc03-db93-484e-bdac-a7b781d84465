/**
 * Dedicated rendering component for game scenes
 * Handles all rendering logic to reduce complexity in main game scene
 */

import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';
import { Bullet } from '@/game/entities/Bullet';
import { Enemy } from '@/game/entities/Enemy';
import { Player } from '@/game/entities/Player';
import { Item } from '@/game/systems/LootSystem';
import { ProgressionData } from '@/game/systems/ProgressionSystem';

export interface RenderableEntities {
  player: Player;
  enemies: Enemy[];
  bullets: Bullet[];
  items: Item[];
}

export interface UIData {
  score: number;
  playerProgression: ProgressionData;
  showSkillTree: boolean;
  showInventory: boolean;
  gameTime: number;
}

export class GameRenderer {
  private static readonly GRID_SIZE = 32;
  private static readonly HEALTH_BAR_WIDTH = 200;
  private static readonly HEALTH_BAR_HEIGHT = 20;

  /**
   * Render the complete game scene
   */
  static renderScene(
    renderer: CanvasRenderer,
    entities: RenderableEntities,
    uiData: UIData,
    camera: { x: number; y: number }
  ): void {
    // Clear canvas
    renderer.clear();

    // Apply camera transform
    renderer.save();
    renderer.translate(-camera.x, -camera.y);

    // Render world elements
    this.renderBackground(renderer, camera);
    this.renderGrid(renderer, camera);
    this.renderEntities(renderer, entities);

    // Restore camera transform
    renderer.restore();

    // Render UI (not affected by camera)
    this.renderUI(renderer, entities.player, uiData);
  }

  /**
   * Render background
   */
  private static renderBackground(renderer: CanvasRenderer, camera: { x: number; y: number }): void {
    const { width, height } = renderer.getCanvasSize();

    // Simple gradient background
    const gradient = renderer.createLinearGradient(
      camera.x, camera.y,
      camera.x, camera.y + height
    );
    gradient.addColorStop(0, '#1a1a2e');
    gradient.addColorStop(1, '#16213e');

    renderer.fillRect(camera.x, camera.y, width, height, gradient);
  }

  /**
   * Render grid overlay
   */
  private static renderGrid(renderer: CanvasRenderer, camera: { x: number; y: number }): void {
    const { width, height } = renderer.getCanvasSize();
    const gridSize = this.GRID_SIZE;

    const startX = Math.floor(camera.x / gridSize) * gridSize;
    const startY = Math.floor(camera.y / gridSize) * gridSize;
    const endX = startX + width + gridSize;
    const endY = startY + height + gridSize;

    // Use utility function for grid rendering
    RenderUtils.drawGrid(
      renderer,
      startX,
      startY,
      endX,
      endY,
      gridSize,
      {
        color: '#666666',
        lineWidth: 1,
        alpha: 0.1
      }
    );
  }

  /**
   * Render all game entities
   */
  private static renderEntities(renderer: CanvasRenderer, entities: RenderableEntities): void {
    // Render in order: items, bullets, enemies, player (player on top)
    this.renderItems(renderer, entities.items);
    this.renderBullets(renderer, entities.bullets);
    this.renderEnemies(renderer, entities.enemies);
    this.renderPlayer(renderer, entities.player);
  }

  /**
   * Render player
   */
  private static renderPlayer(renderer: CanvasRenderer, player: Player): void {
    if (player.isAlive) {
      player.render(renderer);
    }
  }

  /**
   * Render enemies
   */
  private static renderEnemies(renderer: CanvasRenderer, enemies: Enemy[]): void {
    for (const enemy of enemies) {
      if (enemy.isAlive) {
        enemy.render(renderer);
      }
    }
  }

  /**
   * Render bullets/projectiles
   */
  private static renderBullets(renderer: CanvasRenderer, bullets: Bullet[]): void {
    for (const bullet of bullets) {
      if (bullet.isActive()) {
        bullet.render(renderer);
      }
    }
  }

  /**
   * Render items with rarity-based glow effects
   */
  private static renderItems(renderer: CanvasRenderer, items: Item[]): void {
    const glowColors = {
      'common': '#ffffff',
      'uncommon': '#00ff00',
      'rare': '#0080ff',
      'epic': '#8000ff',
      'legendary': '#ff8000'
    };

    for (const item of items) {
      const color = glowColors[item.rarity] || '#ffffff';

      // Draw glow effect
      renderer.setGlobalAlpha(0.5);
      renderer.drawCircle(item.position.x, item.position.y, 20, color);
      renderer.setGlobalAlpha(1);

      // Draw item
      renderer.drawRect(
        item.position.x - 8,
        item.position.y - 8,
        16,
        16,
        color
      );
    }
  }

  /**
   * Render UI elements
   */
  private static renderUI(renderer: CanvasRenderer, player: Player, uiData: UIData): void {
    this.renderHealthBar(renderer, player);
    this.renderGameStats(renderer, uiData);
    this.renderProgressionInfo(renderer, uiData.playerProgression);

    if (uiData.showSkillTree) {
      this.renderSkillTree(renderer, uiData.playerProgression);
    }

    if (uiData.showInventory) {
      this.renderInventory(renderer);
    }
  }

  /**
   * Render health bar
   */
  private static renderHealthBar(renderer: CanvasRenderer, player: Player): void {
    const x = 20;
    const y = 20;
    const width = this.HEALTH_BAR_WIDTH;
    const height = this.HEALTH_BAR_HEIGHT;

    // Use utility function for health bar
    RenderUtils.drawHealthBar(
      renderer,
      x,
      y,
      width,
      height,
      player.stats.health,
      player.stats.maxHealth,
      {
        backgroundColor: '#333333',
        healthColor: '#4CAF50',
        lowHealthColor: '#ff9800',
        criticalHealthColor: '#f44336',
        borderColor: '#000000',
        borderWidth: 1
      }
    );

    // Health text
    renderer.drawText(
      `Health: ${player.stats.health}/${player.stats.maxHealth}`,
      x,
      y + height + 15,
      '14px monospace',
      '#ffffff'
    );
  }

  /**
   * Render game statistics
   */
  private static renderGameStats(renderer: CanvasRenderer, uiData: UIData): void {
    const { width } = renderer.getCanvasSize();

    renderer.drawText(
      `Score: ${uiData.score}`,
      width - 200,
      30,
      '16px monospace',
      '#ffffff'
    );

    renderer.drawText(
      `Time: ${Math.floor(uiData.gameTime)}s`,
      width - 200,
      55,
      '14px monospace',
      '#cccccc'
    );
  }

  /**
   * Render progression information
   */
  private static renderProgressionInfo(renderer: CanvasRenderer, progression: ProgressionData): void {
    const x = 20;
    const y = 80;

    renderer.drawText(
      `Level: ${progression.level}`,
      x,
      y,
      '16px monospace',
      '#ffffff'
    );

    // Experience bar using utility
    const expPercent = progression.experience / progression.experienceToNext;
    const expBarWidth = 150;
    const expBarHeight = 8;

    RenderUtils.drawProgressBar(
      renderer,
      x,
      y + 10,
      expBarWidth,
      expBarHeight,
      expPercent,
      {
        backgroundColor: '#333333',
        progressColor: '#00ff00',
        borderColor: '#000000',
        borderWidth: 1
      }
    );

    renderer.drawText(
      `EXP: ${progression.experience}/${progression.experienceToNext}`,
      x,
      y + 30,
      '12px monospace',
      '#cccccc'
    );
  }

  /**
   * Render skill tree overlay
   */
  private static renderSkillTree(renderer: CanvasRenderer, progression: ProgressionData): void {
    const { width, height } = renderer.getCanvasSize();

    // Semi-transparent background
    renderer.setGlobalAlpha(0.8);
    renderer.drawRect(0, 0, width, height, '#000000');
    renderer.setGlobalAlpha(1);

    // Title
    renderer.drawText(
      'Skill Tree',
      width / 2 - 50,
      50,
      '24px monospace',
      '#ffffff'
    );

    // Available skill points
    renderer.drawText(
      `Skill Points: ${progression.skillPoints}`,
      width / 2 - 80,
      80,
      '16px monospace',
      '#00ff00'
    );

    // Instructions
    renderer.drawText(
      'Press T to close',
      width / 2 - 60,
      height - 30,
      '14px monospace',
      '#cccccc'
    );
  }

  /**
   * Render inventory overlay
   */
  private static renderInventory(renderer: CanvasRenderer): void {
    const { width, height } = renderer.getCanvasSize();

    // Semi-transparent background
    renderer.setGlobalAlpha(0.8);
    renderer.drawRect(0, 0, width, height, '#000000');
    renderer.setGlobalAlpha(1);

    // Title
    renderer.drawText(
      'Inventory',
      width / 2 - 50,
      50,
      '24px monospace',
      '#ffffff'
    );

    // Instructions
    renderer.drawText(
      'Press I to close',
      width / 2 - 60,
      height - 30,
      '14px monospace',
      '#cccccc'
    );

    // TODO: Render inventory grid and items
    // This would be implemented based on the inventory system
  }
}
