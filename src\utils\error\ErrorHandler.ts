/**
 * Comprehensive error handling and validation system
 */

export type ErrorLevel = 'info' | 'warning' | 'error' | 'critical';

export interface GameError {
  id: string;
  level: ErrorLevel;
  message: string;
  details?: any;
  timestamp: number;
  stack?: string;
  context?: string;
  recoverable: boolean;
}

export interface ValidationResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  warnings?: string[];
}

export interface ErrorRecoveryStrategy {
  name: string;
  canRecover: (error: GameError) => boolean;
  recover: (error: GameError) => Promise<boolean>;
}

export class ErrorHandler {
  private static instance: ErrorHandler;
  private errors: GameError[] = [];
  private maxErrorHistory: number = 100;
  private recoveryStrategies: ErrorRecoveryStrategy[] = [];
  private errorListeners: ((error: GameError) => void)[] = [];
  private isRecovering: boolean = false;

  private constructor() {
    this.setupGlobalErrorHandling();
    this.registerDefaultRecoveryStrategies();
  }

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  private setupGlobalErrorHandling(): void {
    // Handle uncaught JavaScript errors
    window.addEventListener('error', (event) => {
      this.handleError({
        id: this.generateErrorId(),
        level: 'error',
        message: event.message,
        details: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          error: event.error
        },
        timestamp: Date.now(),
        stack: event.error?.stack,
        context: 'global',
        recoverable: false
      });
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError({
        id: this.generateErrorId(),
        level: 'error',
        message: 'Unhandled Promise Rejection',
        details: event.reason,
        timestamp: Date.now(),
        stack: event.reason?.stack,
        context: 'promise',
        recoverable: false
      });
    });
  }

  private registerDefaultRecoveryStrategies(): void {
    // Audio context recovery
    this.addRecoveryStrategy({
      name: 'audio_context_recovery',
      canRecover: (error) => error.context === 'audio' && error.message.includes('suspended'),
      recover: async (error) => {
        try {
          // Attempt to resume audio context
          if ('audioContext' in window && (window as any).audioContext) {
            await (window as any).audioContext.resume();
            return true;
          }
          return false;
        } catch {
          return false;
        }
      }
    });

    // Canvas context recovery
    this.addRecoveryStrategy({
      name: 'canvas_context_recovery',
      canRecover: (error) => error.context === 'rendering' && error.message.includes('context'),
      recover: async (error) => {
        try {
          // Attempt to recreate canvas context
          const canvas = document.querySelector('canvas');
          if (canvas) {
            const newContext = canvas.getContext('2d');
            return newContext !== null;
          }
          return false;
        } catch {
          return false;
        }
      }
    });

    // Memory cleanup recovery
    this.addRecoveryStrategy({
      name: 'memory_cleanup',
      canRecover: (error) => error.level === 'warning' && error.message.includes('memory'),
      recover: async (error) => {
        try {
          // Force garbage collection if available
          if ('gc' in window) {
            (window as any).gc();
          }
          return true;
        } catch {
          return false;
        }
      }
    });
  }

  // Error handling methods
  handleError(error: GameError): void {
    // Add to error history
    this.errors.push(error);
    
    // Limit error history size
    if (this.errors.length > this.maxErrorHistory) {
      this.errors.shift();
    }

    // Log error
    this.logError(error);

    // Notify listeners
    this.notifyErrorListeners(error);

    // Attempt recovery for recoverable errors
    if (error.recoverable && !this.isRecovering) {
      this.attemptRecovery(error);
    }

    // Handle critical errors
    if (error.level === 'critical') {
      this.handleCriticalError(error);
    }
  }

  private logError(error: GameError): void {
    const logMessage = `[${error.level.toUpperCase()}] ${error.message}`;
    
    switch (error.level) {
      case 'info':
        console.info(logMessage, error.details);
        break;
      case 'warning':
        console.warn(logMessage, error.details);
        break;
      case 'error':
        console.error(logMessage, error.details);
        if (error.stack) console.error(error.stack);
        break;
      case 'critical':
        console.error(`🚨 CRITICAL: ${logMessage}`, error.details);
        if (error.stack) console.error(error.stack);
        break;
    }
  }

  private async attemptRecovery(error: GameError): Promise<void> {
    this.isRecovering = true;

    for (const strategy of this.recoveryStrategies) {
      if (strategy.canRecover(error)) {
        try {
          const recovered = await strategy.recover(error);
          if (recovered) {
            this.info(`Recovered from error using strategy: ${strategy.name}`, { originalError: error.id });
            this.isRecovering = false;
            return;
          }
        } catch (recoveryError) {
          this.error(`Recovery strategy ${strategy.name} failed`, recoveryError);
        }
      }
    }

    this.isRecovering = false;
    this.warning(`No recovery strategy found for error: ${error.message}`);
  }

  private handleCriticalError(error: GameError): void {
    // Show user-friendly error message
    const userMessage = this.getUserFriendlyMessage(error);
    
    // In a real game, this might show a modal or redirect to an error page
    alert(`Critical Error: ${userMessage}\n\nThe game will attempt to reload.`);
    
    // Attempt to save any critical data before reload
    this.saveErrorReport(error);
    
    // Reload the page as last resort
    setTimeout(() => {
      window.location.reload();
    }, 2000);
  }

  private getUserFriendlyMessage(error: GameError): string {
    // Convert technical errors to user-friendly messages
    if (error.context === 'audio') {
      return 'Audio system encountered an error. Please check your audio settings.';
    }
    if (error.context === 'rendering') {
      return 'Graphics system encountered an error. Please try refreshing the page.';
    }
    if (error.message.includes('memory')) {
      return 'The game is running low on memory. Please close other applications.';
    }
    if (error.message.includes('network')) {
      return 'Network connection error. Please check your internet connection.';
    }
    
    return 'An unexpected error occurred. Please try refreshing the page.';
  }

  // Convenience methods for different error levels
  info(message: string, details?: any, context?: string): void {
    this.handleError({
      id: this.generateErrorId(),
      level: 'info',
      message,
      details,
      timestamp: Date.now(),
      context,
      recoverable: false
    });
  }

  warning(message: string, details?: any, context?: string): void {
    this.handleError({
      id: this.generateErrorId(),
      level: 'warning',
      message,
      details,
      timestamp: Date.now(),
      context,
      recoverable: true
    });
  }

  error(message: string, details?: any, context?: string): void {
    this.handleError({
      id: this.generateErrorId(),
      level: 'error',
      message,
      details,
      timestamp: Date.now(),
      stack: new Error().stack,
      context,
      recoverable: true
    });
  }

  critical(message: string, details?: any, context?: string): void {
    this.handleError({
      id: this.generateErrorId(),
      level: 'critical',
      message,
      details,
      timestamp: Date.now(),
      stack: new Error().stack,
      context,
      recoverable: false
    });
  }

  // Recovery strategy management
  addRecoveryStrategy(strategy: ErrorRecoveryStrategy): void {
    this.recoveryStrategies.push(strategy);
  }

  removeRecoveryStrategy(name: string): void {
    this.recoveryStrategies = this.recoveryStrategies.filter(s => s.name !== name);
  }

  // Error listener management
  addErrorListener(listener: (error: GameError) => void): void {
    this.errorListeners.push(listener);
  }

  removeErrorListener(listener: (error: GameError) => void): void {
    const index = this.errorListeners.indexOf(listener);
    if (index > -1) {
      this.errorListeners.splice(index, 1);
    }
  }

  private notifyErrorListeners(error: GameError): void {
    for (const listener of this.errorListeners) {
      try {
        listener(error);
      } catch (listenerError) {
        console.error('Error in error listener:', listenerError);
      }
    }
  }

  // Error reporting and analysis
  getErrors(level?: ErrorLevel): GameError[] {
    if (level) {
      return this.errors.filter(error => error.level === level);
    }
    return [...this.errors];
  }

  getErrorStats(): { [level in ErrorLevel]: number } {
    const stats = { info: 0, warning: 0, error: 0, critical: 0 };
    
    for (const error of this.errors) {
      stats[error.level]++;
    }
    
    return stats;
  }

  clearErrors(): void {
    this.errors = [];
  }

  saveErrorReport(error?: GameError): void {
    const report = {
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      errors: error ? [error] : this.errors,
      stats: this.getErrorStats()
    };

    try {
      localStorage.setItem('bulletrpg_error_report', JSON.stringify(report));
    } catch (e) {
      console.error('Failed to save error report:', e);
    }
  }

  loadErrorReport(): any {
    try {
      const report = localStorage.getItem('bulletrpg_error_report');
      return report ? JSON.parse(report) : null;
    } catch (e) {
      console.error('Failed to load error report:', e);
      return null;
    }
  }

  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
