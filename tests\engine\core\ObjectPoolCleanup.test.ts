import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ObjectPool, PoolManager, Poolable } from '@/engine/core/ObjectPool';

// Mock poolable object for testing
class MockPoolableObject implements Poolable {
  private active: boolean = false;
  public destroyed: boolean = false;
  public resetCalled: boolean = false;

  reset(): void {
    this.resetCalled = true;
    this.active = false;
  }

  isActive(): boolean {
    return this.active;
  }

  setActive(active: boolean): void {
    this.active = active;
  }

  destroy(): void {
    this.destroyed = true;
  }
}

describe('ObjectPool Cleanup', () => {
  let pool: ObjectPool<MockPoolableObject>;

  beforeEach(() => {
    pool = new ObjectPool(() => new MockPoolableObject(), 2, 5);
  });

  describe('release with pool overflow', () => {
    it('should destroy objects when pool is full', () => {
      // Fill the pool to capacity
      const objects: MockPoolableObject[] = [];
      for (let i = 0; i < 6; i++) {
        objects.push(pool.acquire());
      }

      // Release all objects - some should be destroyed
      for (const obj of objects) {
        pool.release(obj);
      }

      // Check that excess objects were destroyed
      const destroyedCount = objects.filter(obj => obj.destroyed).length;
      expect(destroyedCount).toBe(1); // 6 objects - 5 max pool size = 1 destroyed
    });

    it('should not destroy objects when pool has space', () => {
      const obj1 = pool.acquire();
      const obj2 = pool.acquire();

      pool.release(obj1);
      pool.release(obj2);

      expect(obj1.destroyed).toBe(false);
      expect(obj2.destroyed).toBe(false);
    });
  });

  describe('cleanup method', () => {
    it('should remove inactive objects from active list', () => {
      const obj1 = pool.acquire();
      const obj2 = pool.acquire();
      const obj3 = pool.acquire();

      // Make some objects inactive
      obj1.setActive(false);
      obj3.setActive(false);

      expect(pool.getActiveCount()).toBe(3);

      pool.cleanup();

      expect(pool.getActiveCount()).toBe(1); // Only obj2 should remain active
      expect(pool.getPoolSize()).toBe(4); // 2 initial + 2 returned from cleanup
    });

    it('should destroy excess objects during cleanup', () => {
      // Create more objects than pool can hold
      const objects: MockPoolableObject[] = [];
      for (let i = 0; i < 6; i++) {
        objects.push(pool.acquire());
      }

      // Make all objects inactive
      objects.forEach(obj => obj.setActive(false));

      pool.cleanup();

      // Check that excess objects were destroyed
      const destroyedCount = objects.filter(obj => obj.destroyed).length;
      expect(destroyedCount).toBe(1); // 6 objects - 5 max pool size = 1 destroyed
    });
  });

  describe('trim method', () => {
    it('should reduce pool size to target', () => {
      // Fill pool
      const objects: MockPoolableObject[] = [];
      for (let i = 0; i < 5; i++) {
        const obj = pool.acquire();
        objects.push(obj);
        pool.release(obj);
      }

      expect(pool.getPoolSize()).toBe(5);

      // Trim to 2 objects
      pool.trim(2);

      expect(pool.getPoolSize()).toBe(2);

      // Check that trimmed objects were destroyed
      const destroyedCount = objects.filter(obj => obj.destroyed).length;
      expect(destroyedCount).toBe(3);
    });

    it('should use default target size when not specified', () => {
      // Fill pool
      for (let i = 0; i < 5; i++) {
        const obj = pool.acquire();
        pool.release(obj);
      }

      pool.trim(); // Should trim to 50% of max size (2.5 -> 2)

      expect(pool.getPoolSize()).toBe(2);
    });
  });

  describe('clear method', () => {
    it('should destroy all objects when clearing', () => {
      const obj1 = pool.acquire();
      const obj2 = pool.acquire();
      const obj3 = pool.acquire();

      pool.release(obj2); // One object in pool
      // obj1 and obj3 still active

      pool.clear();

      expect(obj1.destroyed).toBe(true);
      expect(obj2.destroyed).toBe(true);
      expect(obj3.destroyed).toBe(true);
      expect(pool.getActiveCount()).toBe(0);
      expect(pool.getPoolSize()).toBe(0);
    });
  });

  describe('update method', () => {
    it('should automatically release inactive objects', () => {
      const obj1 = pool.acquire();
      const obj2 = pool.acquire();

      obj1.setActive(false); // This should be auto-released

      expect(pool.getActiveCount()).toBe(2);

      pool.update();

      expect(pool.getActiveCount()).toBe(1);
      expect(pool.getPoolSize()).toBe(3); // 2 initial + 1 auto-released
    });
  });
});

describe('PoolManager Cleanup', () => {
  let poolManager: PoolManager;

  beforeEach(() => {
    poolManager = new PoolManager();
    poolManager.registerPool('test1', () => new MockPoolableObject(), 2, 5);
    poolManager.registerPool('test2', () => new MockPoolableObject(), 3, 6);
  });

  describe('cleanupAll method', () => {
    it('should cleanup all pools', () => {
      // Create and release objects in both pools
      const obj1 = poolManager.acquire('test1');
      const obj2 = poolManager.acquire('test2');

      obj1?.setActive(false);
      obj2?.setActive(false);

      poolManager.cleanupAll();

      const stats1 = poolManager.getPool('test1')?.getStats();
      const stats2 = poolManager.getPool('test2')?.getStats();

      expect(stats1?.active).toBe(0);
      expect(stats2?.active).toBe(0);
    });
  });

  describe('trimAll method', () => {
    it('should trim all pools to target ratio', () => {
      // Fill both pools
      for (let i = 0; i < 5; i++) {
        const obj1 = poolManager.acquire('test1');
        const obj2 = poolManager.acquire('test2');
        poolManager.release('test1', obj1!);
        poolManager.release('test2', obj2!);
      }

      poolManager.trimAll(0.4); // 40% of max size

      const stats1 = poolManager.getPool('test1')?.getStats();
      const stats2 = poolManager.getPool('test2')?.getStats();

      expect(stats1?.pooled).toBe(2); // 40% of 5 = 2
      expect(stats2?.pooled).toBe(2); // 40% of 6 = 2.4 -> 2
    });
  });

  describe('getMemoryStats method', () => {
    it('should return comprehensive memory statistics', () => {
      // Create some objects
      const obj1 = poolManager.acquire('test1');
      const obj2 = poolManager.acquire('test2');
      const obj3 = poolManager.acquire('test1');

      poolManager.release('test1', obj3!);

      const memStats = poolManager.getMemoryStats();

      expect(memStats.totalPools).toBe(2);
      expect(memStats.totalActive).toBe(2); // obj1 and obj2 still active
      expect(memStats.totalPooled).toBe(6); // 2 + 3 initial + 1 released
      expect(memStats.totalObjects).toBe(8);
      expect(memStats.averageUtilization).toBeGreaterThan(0);
    });

    it('should handle empty pool manager', () => {
      const emptyManager = new PoolManager();
      const memStats = emptyManager.getMemoryStats();

      expect(memStats.totalPools).toBe(0);
      expect(memStats.totalObjects).toBe(0);
      expect(memStats.averageUtilization).toBe(0);
    });
  });

  describe('destroy method', () => {
    it('should properly destroy all pools and objects', () => {
      const obj1 = poolManager.acquire('test1') as MockPoolableObject;
      const obj2 = poolManager.acquire('test2') as MockPoolableObject;

      poolManager.destroy();

      expect(obj1.destroyed).toBe(true);
      expect(obj2.destroyed).toBe(true);
      expect(poolManager.getStats().size).toBe(0);
    });
  });
});
