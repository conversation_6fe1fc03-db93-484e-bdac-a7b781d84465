<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bullet Hell Rogue-like RPG - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #1a1a1a;
            color: #ffffff;
            font-family: 'Courier New', monospace;
            overflow: hidden;
            user-select: none;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        
        #gameCanvas {
            border: 2px solid #444;
            background: #000;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }
        
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <canvas id="gameCanvas" width="1024" height="768"></canvas>
    <div id="info">
        <div>🎮 Bullet Hell Rogue-like RPG Demo</div>
        <div>WASD: Move Player</div>
        <div>Mouse: Aim</div>
        <div>Click: Shoot</div>
        <div>Space: Auto-attack toggle</div>
        <div>1-6: Switch weapons</div>
        <div id="fps">FPS: --</div>
        <div id="weapon">Weapon: Basic Bow</div>
        <div id="stats">Level: 1 | XP: 0</div>
    </div>

    <script>
        // Simple game demo without TypeScript compilation
        class Vector2 {
            constructor(x = 0, y = 0) {
                this.x = x;
                this.y = y;
            }
            
            add(other) {
                return new Vector2(this.x + other.x, this.y + other.y);
            }
            
            multiply(scalar) {
                return new Vector2(this.x * scalar, this.y * scalar);
            }
            
            normalize() {
                const length = Math.sqrt(this.x * this.x + this.y * this.y);
                if (length === 0) return new Vector2(0, 0);
                return new Vector2(this.x / length, this.y / length);
            }
            
            distance(other) {
                const dx = this.x - other.x;
                const dy = this.y - other.y;
                return Math.sqrt(dx * dx + dy * dy);
            }

            rotate(angle) {
                const cos = Math.cos(angle);
                const sin = Math.sin(angle);
                return new Vector2(
                    this.x * cos - this.y * sin,
                    this.x * sin + this.y * cos
                );
            }

            magnitude() {
                return Math.sqrt(this.x * this.x + this.y * this.y);
            }
        }

        class GameObject {
            constructor(x, y) {
                this.position = new Vector2(x, y);
                this.velocity = new Vector2(0, 0);
                this.size = 16;
                this.active = true;
            }
            
            update(deltaTime) {
                this.position = this.position.add(this.velocity.multiply(deltaTime));
            }
            
            render(ctx) {
                // Override in subclasses
            }
            
            checkCollision(other) {
                return this.position.distance(other.position) < (this.size + other.size) / 2;
            }
        }

        class Player extends GameObject {
            constructor(x, y) {
                super(x, y);
                this.size = 32;
                this.health = 100;
                this.maxHealth = 100;
                this.speed = 300;
                this.shootCooldown = 0;
                this.autoAttack = false;
                this.level = 1;
                this.experience = 0;
                this.currentWeapon = 0;
                this.weapons = [
                    { name: 'Basic Bow', damage: 25, rate: 10, color: '#8B4513', speed: 600, size: 8 },
                    { name: 'Magic Missile', damage: 20, rate: 8, color: '#4A90E2', speed: 400, size: 10, homing: true },
                    { name: 'Piercing Bow', damage: 35, rate: 6, color: '#FFD700', speed: 700, size: 10, piercing: true },
                    { name: 'Triple Shot', damage: 30, rate: 4, color: '#8B0000', speed: 650, size: 9, multishot: 3 },
                    { name: 'Fireball', damage: 50, rate: 3, color: '#FF4500', speed: 300, size: 16, explosive: true },
                    { name: 'Lightning', damage: 40, rate: 6, color: '#FFFF00', speed: 1200, size: 6, instant: true }
                ];
            }
            
            update(deltaTime, input, mousePos) {
                // Movement
                const movement = new Vector2(0, 0);
                if (input.keys.has('KeyW') || input.keys.has('ArrowUp')) movement.y -= 1;
                if (input.keys.has('KeyS') || input.keys.has('ArrowDown')) movement.y += 1;
                if (input.keys.has('KeyA') || input.keys.has('ArrowLeft')) movement.x -= 1;
                if (input.keys.has('KeyD') || input.keys.has('ArrowRight')) movement.x += 1;

                // Normalize diagonal movement
                if (movement.x !== 0 && movement.y !== 0) {
                    movement.x *= 0.707;
                    movement.y *= 0.707;
                }

                this.velocity = movement.multiply(this.speed);
                super.update(deltaTime);

                // Keep player in bounds
                this.position.x = Math.max(this.size/2, Math.min(1024 - this.size/2, this.position.x));
                this.position.y = Math.max(this.size/2, Math.min(768 - this.size/2, this.position.y));

                // Weapon switching
                for (let i = 1; i <= 6; i++) {
                    if (input.keysPressed.has(`Digit${i}`) && i <= this.weapons.length) {
                        this.currentWeapon = i - 1;
                        document.getElementById('weapon').textContent = `Weapon: ${this.weapons[this.currentWeapon].name}`;
                    }
                }

                // Shooting
                this.shootCooldown = Math.max(0, this.shootCooldown - deltaTime);

                if (input.keysPressed.has('Space')) {
                    this.autoAttack = !this.autoAttack;
                }

                const weapon = this.weapons[this.currentWeapon];
                if ((input.mouseDown || this.autoAttack) && this.shootCooldown <= 0) {
                    this.shoot(mousePos);
                    this.shootCooldown = 1 / weapon.rate;
                }
            }
            
            shoot(targetPos) {
                const weapon = this.weapons[this.currentWeapon];
                const direction = new Vector2(
                    targetPos.x - this.position.x,
                    targetPos.y - this.position.y
                ).normalize();

                const shotCount = weapon.multishot || 1;
                for (let i = 0; i < shotCount; i++) {
                    let shootDirection = direction;

                    // Apply spread for multishot
                    if (shotCount > 1) {
                        const spread = Math.PI / 8; // 22.5 degrees
                        const angle = (i - (shotCount - 1) / 2) * (spread / (shotCount - 1));
                        shootDirection = direction.rotate(angle);
                    }

                    const bullet = new Bullet(
                        this.position.x,
                        this.position.y,
                        shootDirection.multiply(weapon.speed),
                        weapon.color,
                        true
                    );

                    // Apply weapon properties
                    bullet.damage = weapon.damage;
                    bullet.size = weapon.size;
                    bullet.piercing = weapon.piercing || false;
                    bullet.homing = weapon.homing || false;
                    bullet.explosive = weapon.explosive || false;
                    bullet.instant = weapon.instant || false;

                    game.addBullet(bullet);
                }
            }
            
            render(ctx) {
                // Draw player
                ctx.fillStyle = '#4CAF50';
                ctx.fillRect(
                    this.position.x - this.size/2,
                    this.position.y - this.size/2,
                    this.size,
                    this.size
                );
                
                // Draw health bar
                const barWidth = this.size + 10;
                const barHeight = 4;
                const barX = this.position.x - barWidth/2;
                const barY = this.position.y - this.size/2 - 10;
                
                ctx.fillStyle = '#333';
                ctx.fillRect(barX, barY, barWidth, barHeight);
                
                const healthPercent = this.health / this.maxHealth;
                ctx.fillStyle = healthPercent > 0.5 ? '#4CAF50' : healthPercent > 0.25 ? '#ff9800' : '#f44336';
                ctx.fillRect(barX, barY, barWidth * healthPercent, barHeight);
            }
        }

        class Enemy extends GameObject {
            constructor(x, y, type = 'basic') {
                super(x, y);
                this.type = type;
                this.shootCooldown = 0;
                this.aiTimer = 0;
                this.patternPhase = 0;
                this.burstCount = 0;
                this.targetPosition = new Vector2(x, y);

                this.initializeByType(type);
            }

            initializeByType(type) {
                switch (type) {
                    case 'basic':
                        this.size = 24;
                        this.health = 30;
                        this.maxHealth = 30;
                        this.speed = 100;
                        this.damage = 15;
                        this.shootInterval = 1.5;
                        this.color = '#ff6b6b';
                        this.aiType = 'chaser';
                        this.bulletPattern = 'single';
                        break;
                    case 'shooter':
                        this.size = 20;
                        this.health = 20;
                        this.maxHealth = 20;
                        this.speed = 80;
                        this.damage = 12;
                        this.shootInterval = 0.8;
                        this.color = '#ff9800';
                        this.aiType = 'shooter';
                        this.bulletPattern = 'spread';
                        break;
                    case 'heavy':
                        this.size = 36;
                        this.health = 80;
                        this.maxHealth = 80;
                        this.speed = 60;
                        this.damage = 25;
                        this.shootInterval = 2;
                        this.color = '#9c27b0';
                        this.aiType = 'circler';
                        this.bulletPattern = 'circle';
                        break;
                    case 'bomber':
                        this.size = 28;
                        this.health = 40;
                        this.maxHealth = 40;
                        this.speed = 120;
                        this.damage = 30;
                        this.shootInterval = 3;
                        this.color = '#e91e63';
                        this.aiType = 'bomber';
                        this.bulletPattern = 'explosion';
                        break;
                }
            }
            
            update(deltaTime, player) {
                this.aiTimer += deltaTime;
                const distanceToPlayer = this.position.distance(player.position);

                // Update AI behavior
                this.updateAI(deltaTime, player, distanceToPlayer);

                super.update(deltaTime);

                // Shooting
                this.shootCooldown -= deltaTime;
                if (this.shootCooldown <= 0 && distanceToPlayer < 400) {
                    this.performAttack(player);
                    this.shootCooldown = this.shootInterval;
                }
            }

            updateAI(deltaTime, player, distanceToPlayer) {
                switch (this.aiType) {
                    case 'chaser':
                        this.updateChaserAI(player);
                        break;
                    case 'shooter':
                        this.updateShooterAI(player, distanceToPlayer);
                        break;
                    case 'circler':
                        this.updateCirclerAI(player, distanceToPlayer);
                        break;
                    case 'bomber':
                        this.updateBomberAI(player, distanceToPlayer);
                        break;
                }
            }

            updateChaserAI(player) {
                const direction = new Vector2(
                    player.position.x - this.position.x,
                    player.position.y - this.position.y
                ).normalize();

                this.velocity = direction.multiply(this.speed);
            }

            updateShooterAI(player, distanceToPlayer) {
                const direction = new Vector2(
                    player.position.x - this.position.x,
                    player.position.y - this.position.y
                ).normalize();

                if (distanceToPlayer < 150) {
                    // Too close, back away
                    this.velocity = direction.multiply(-this.speed * 0.7);
                } else if (distanceToPlayer > 250) {
                    // Too far, move closer
                    this.velocity = direction.multiply(this.speed * 0.5);
                } else {
                    // Good distance, strafe
                    const perpendicular = new Vector2(-direction.y, direction.x);
                    this.velocity = perpendicular.multiply(this.speed * 0.8);
                }
            }

            updateCirclerAI(player, distanceToPlayer) {
                const direction = new Vector2(
                    player.position.x - this.position.x,
                    player.position.y - this.position.y
                ).normalize();

                const perpendicular = new Vector2(-direction.y, direction.x);
                const circleRadius = 120;

                if (distanceToPlayer < circleRadius) {
                    this.velocity = direction.multiply(-this.speed * 0.3)
                        .add(perpendicular.multiply(this.speed * 0.9));
                } else if (distanceToPlayer > circleRadius * 1.5) {
                    this.velocity = direction.multiply(this.speed * 0.6);
                } else {
                    this.velocity = perpendicular.multiply(this.speed);
                }
            }

            updateBomberAI(player, distanceToPlayer) {
                if (distanceToPlayer < 80) {
                    // Rush towards player when very close
                    const direction = new Vector2(
                        player.position.x - this.position.x,
                        player.position.y - this.position.y
                    ).normalize();

                    this.velocity = direction.multiply(this.speed * 1.5);
                } else {
                    this.updateChaserAI(player);
                }
            }
            
            performAttack(player) {
                switch (this.bulletPattern) {
                    case 'single':
                        this.shootSingle(player);
                        break;
                    case 'spread':
                        this.shootSpread(player);
                        break;
                    case 'circle':
                        this.shootCircle();
                        break;
                    case 'explosion':
                        this.shootExplosion(player);
                        break;
                }
            }

            shootSingle(player) {
                const direction = new Vector2(
                    player.position.x - this.position.x,
                    player.position.y - this.position.y
                ).normalize();

                const bullet = new Bullet(
                    this.position.x,
                    this.position.y,
                    direction.multiply(200),
                    this.color,
                    false
                );
                bullet.damage = this.damage;
                bullet.size = 6;
                game.addBullet(bullet);
            }

            shootSpread(player) {
                const baseDirection = new Vector2(
                    player.position.x - this.position.x,
                    player.position.y - this.position.y
                ).normalize();

                const spreadAngle = Math.PI / 6; // 30 degrees
                const bulletCount = 3;

                for (let i = 0; i < bulletCount; i++) {
                    const angle = (i - (bulletCount - 1) / 2) * (spreadAngle / (bulletCount - 1));
                    const direction = baseDirection.rotate(angle);

                    const bullet = new Bullet(
                        this.position.x,
                        this.position.y,
                        direction.multiply(180),
                        this.color,
                        false
                    );
                    bullet.damage = this.damage * 0.8;
                    bullet.size = 5;
                    game.addBullet(bullet);
                }
            }

            shootCircle() {
                const bulletCount = 8;
                this.patternPhase += 0.3;

                for (let i = 0; i < bulletCount; i++) {
                    const angle = (Math.PI * 2 * i) / bulletCount + this.patternPhase;
                    const direction = new Vector2(Math.cos(angle), Math.sin(angle));

                    const bullet = new Bullet(
                        this.position.x,
                        this.position.y,
                        direction.multiply(150),
                        this.color,
                        false
                    );
                    bullet.damage = this.damage * 0.6;
                    bullet.size = 4;
                    game.addBullet(bullet);
                }
            }

            shootExplosion(player) {
                // Bomber shoots a slow projectile that explodes
                const direction = new Vector2(
                    player.position.x - this.position.x,
                    player.position.y - this.position.y
                ).normalize();

                const bullet = new Bullet(
                    this.position.x,
                    this.position.y,
                    direction.multiply(120),
                    this.color,
                    false
                );
                bullet.damage = this.damage;
                bullet.size = 12;
                bullet.explosive = true;
                bullet.lifetime = 2; // Explodes after 2 seconds
                game.addBullet(bullet);
            }
            
            takeDamage(damage) {
                this.health -= damage;
                if (this.health <= 0) {
                    this.active = false;
                }
            }
            
            render(ctx) {
                // Draw enemy based on type
                ctx.fillStyle = this.color;

                switch (this.type) {
                    case 'basic':
                        ctx.beginPath();
                        ctx.arc(this.position.x, this.position.y, this.size/2, 0, Math.PI * 2);
                        ctx.fill();
                        break;
                    case 'shooter':
                        ctx.fillRect(this.position.x - this.size/2, this.position.y - this.size/2, this.size, this.size);
                        break;
                    case 'heavy':
                        // Draw hexagon
                        ctx.beginPath();
                        for (let i = 0; i < 6; i++) {
                            const angle = (Math.PI * 2 * i) / 6;
                            const x = this.position.x + Math.cos(angle) * this.size/2;
                            const y = this.position.y + Math.sin(angle) * this.size/2;
                            if (i === 0) ctx.moveTo(x, y);
                            else ctx.lineTo(x, y);
                        }
                        ctx.closePath();
                        ctx.fill();
                        break;
                    case 'bomber':
                        // Draw diamond
                        ctx.beginPath();
                        ctx.moveTo(this.position.x, this.position.y - this.size/2);
                        ctx.lineTo(this.position.x + this.size/2, this.position.y);
                        ctx.lineTo(this.position.x, this.position.y + this.size/2);
                        ctx.lineTo(this.position.x - this.size/2, this.position.y);
                        ctx.closePath();
                        ctx.fill();
                        break;
                }

                // Health bar
                const barWidth = this.size + 5;
                const barHeight = 3;
                const barX = this.position.x - barWidth/2;
                const barY = this.position.y - this.size/2 - 8;

                ctx.fillStyle = '#333';
                ctx.fillRect(barX, barY, barWidth, barHeight);

                const healthPercent = this.health / this.maxHealth;
                ctx.fillStyle = this.color;
                ctx.fillRect(barX, barY, barWidth * healthPercent, barHeight);
            }
        }

        class Bullet extends GameObject {
            constructor(x, y, velocity, color, isPlayerBullet) {
                super(x, y);
                this.velocity = velocity;
                this.size = 6;
                this.color = color;
                this.isPlayerBullet = isPlayerBullet;
                this.lifetime = 3; // 3 seconds
                this.damage = 10;
                this.piercing = false;
                this.homing = false;
                this.explosive = false;
                this.instant = false;
                this.hitTargets = new Set();
            }
            
            update(deltaTime) {
                // Homing behavior
                if (this.homing && this.isPlayerBullet) {
                    const closestEnemy = this.findClosestEnemy();
                    if (closestEnemy && this.position.distance(closestEnemy.position) < 200) {
                        const targetDirection = new Vector2(
                            closestEnemy.position.x - this.position.x,
                            closestEnemy.position.y - this.position.y
                        ).normalize();

                        const currentDirection = this.velocity.normalize();
                        const homingForce = targetDirection.subtract(currentDirection).multiply(3 * deltaTime);
                        this.velocity = this.velocity.add(homingForce).normalize().multiply(this.velocity.magnitude());
                    }
                }

                super.update(deltaTime);
                this.lifetime -= deltaTime;

                if (this.lifetime <= 0) {
                    if (this.explosive) {
                        game.createExplosion(this.position.x, this.position.y);
                    }
                    this.active = false;
                } else if (this.position.x < -50 || this.position.x > 1074 ||
                          this.position.y < -50 || this.position.y > 818) {
                    this.active = false;
                }
            }

            findClosestEnemy() {
                let closest = null;
                let closestDistance = Infinity;

                for (const enemy of game.enemies) {
                    if (!enemy.active || this.hitTargets.has(enemy)) continue;
                    const distance = this.position.distance(enemy.position);
                    if (distance < closestDistance) {
                        closest = enemy;
                        closestDistance = distance;
                    }
                }

                return closest;
            }
            
            render(ctx) {
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.position.x, this.position.y, this.size/2, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        class Game {
            constructor() {
                this.canvas = document.getElementById('gameCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.player = new Player(512, 384);
                this.enemies = [];
                this.bullets = [];
                this.particles = [];
                this.input = {
                    keys: new Set(),
                    keysPressed: new Set(),
                    mousePos: new Vector2(512, 384),
                    mouseDown: false
                };
                this.lastTime = 0;
                this.fps = 0;
                this.frameCount = 0;
                this.fpsTimer = 0;

                this.setupInput();
                this.spawnEnemies();
            }
            
            setupInput() {
                window.addEventListener('keydown', (e) => {
                    if (!this.input.keys.has(e.code)) {
                        this.input.keysPressed.add(e.code);
                    }
                    this.input.keys.add(e.code);
                    e.preventDefault();
                });
                
                window.addEventListener('keyup', (e) => {
                    this.input.keys.delete(e.code);
                    e.preventDefault();
                });
                
                this.canvas.addEventListener('mousemove', (e) => {
                    const rect = this.canvas.getBoundingClientRect();
                    this.input.mousePos.x = e.clientX - rect.left;
                    this.input.mousePos.y = e.clientY - rect.top;
                });
                
                this.canvas.addEventListener('mousedown', (e) => {
                    this.input.mouseDown = true;
                    e.preventDefault();
                });
                
                this.canvas.addEventListener('mouseup', (e) => {
                    this.input.mouseDown = false;
                    e.preventDefault();
                });
            }
            
            spawnEnemies() {
                const enemyTypes = ['basic', 'shooter', 'heavy', 'bomber'];
                for (let i = 0; i < 5; i++) {
                    const x = Math.random() * 1024;
                    const y = Math.random() * 768;
                    const type = enemyTypes[Math.floor(Math.random() * enemyTypes.length)];
                    this.enemies.push(new Enemy(x, y, type));
                }
            }
            
            addBullet(bullet) {
                this.bullets.push(bullet);
            }
            
            update(deltaTime) {
                // Clear frame-specific input
                this.input.keysPressed.clear();
                
                // Update player
                this.player.update(deltaTime, this.input, this.input.mousePos);
                
                // Update enemies
                this.enemies.forEach(enemy => {
                    if (enemy.active) {
                        enemy.update(deltaTime, this.player);
                    }
                });
                
                // Update bullets
                this.bullets.forEach(bullet => {
                    if (bullet.active) {
                        bullet.update(deltaTime);
                    }
                });

                // Update particles
                for (let i = this.particles.length - 1; i >= 0; i--) {
                    const particle = this.particles[i];
                    particle.life -= deltaTime;
                    particle.x += particle.vx * deltaTime;
                    particle.y += particle.vy * deltaTime;
                    particle.vy += 50 * deltaTime; // gravity

                    if (particle.life <= 0) {
                        this.particles.splice(i, 1);
                    }
                }
                
                // Check collisions
                this.checkCollisions();
                
                // Remove inactive objects
                this.enemies = this.enemies.filter(enemy => enemy.active);
                this.bullets = this.bullets.filter(bullet => bullet.active);

                // Update UI
                document.getElementById('stats').textContent = `Level: ${this.player.level} | XP: ${this.player.experience}`;
                
                // Spawn new enemies if needed
                const maxEnemies = Math.min(8, 3 + Math.floor(this.player.level / 2));
                if (this.enemies.length < maxEnemies) {
                    const edge = Math.floor(Math.random() * 4);
                    let x, y;
                    switch (edge) {
                        case 0: x = Math.random() * 1024; y = -50; break;
                        case 1: x = 1074; y = Math.random() * 768; break;
                        case 2: x = Math.random() * 1024; y = 818; break;
                        case 3: x = -50; y = Math.random() * 768; break;
                    }

                    // Higher level = more dangerous enemies
                    const enemyTypes = ['basic', 'shooter', 'heavy', 'bomber'];
                    let typeIndex = Math.floor(Math.random() * Math.min(enemyTypes.length, 1 + this.player.level / 2));
                    const type = enemyTypes[typeIndex];

                    this.enemies.push(new Enemy(x, y, type));
                }
            }
            
            checkCollisions() {
                // Player bullets vs enemies
                this.bullets.forEach(bullet => {
                    if (bullet.isPlayerBullet && bullet.active) {
                        this.enemies.forEach(enemy => {
                            if (enemy.active && !bullet.hitTargets.has(enemy) && bullet.checkCollision(enemy)) {
                                enemy.takeDamage(bullet.damage);
                                bullet.hitTargets.add(enemy);

                                // Handle special effects
                                if (bullet.explosive) {
                                    this.createExplosion(bullet.position.x, bullet.position.y);
                                }

                                // Only destroy bullet if not piercing
                                if (!bullet.piercing) {
                                    bullet.active = false;
                                }

                                // Give experience when enemy dies
                                if (enemy.health <= 0) {
                                    this.player.experience += 10;
                                    if (this.player.experience >= this.player.level * 100) {
                                        this.player.level++;
                                        this.player.experience = 0;
                                        this.player.maxHealth += 20;
                                        this.player.health = this.player.maxHealth;
                                        document.getElementById('stats').textContent = `Level: ${this.player.level} | XP: ${this.player.experience}`;
                                    }
                                }
                            }
                        });
                    }
                });

                // Enemy bullets vs player
                this.bullets.forEach(bullet => {
                    if (!bullet.isPlayerBullet && bullet.active) {
                        if (bullet.checkCollision(this.player)) {
                            this.player.health -= bullet.damage;
                            bullet.active = false;

                            if (this.player.health <= 0) {
                                this.gameOver();
                            }
                        }
                    }
                });
            }
            
            createExplosion(x, y) {
                // Create explosion particles
                for (let i = 0; i < 15; i++) {
                    const angle = Math.random() * Math.PI * 2;
                    const speed = 50 + Math.random() * 100;
                    const particle = {
                        x: x,
                        y: y,
                        vx: Math.cos(angle) * speed,
                        vy: Math.sin(angle) * speed,
                        life: 0.5 + Math.random() * 0.5,
                        maxLife: 0.5 + Math.random() * 0.5,
                        size: 2 + Math.random() * 4,
                        color: Math.random() > 0.5 ? '#FF4500' : '#FFD700'
                    };
                    this.particles.push(particle);
                }

                // Damage enemies in explosion radius
                const explosionRadius = 60;
                this.enemies.forEach(enemy => {
                    if (enemy.active) {
                        const distance = Math.sqrt((enemy.position.x - x) ** 2 + (enemy.position.y - y) ** 2);
                        if (distance <= explosionRadius) {
                            enemy.takeDamage(30);
                        }
                    }
                });
            }

            gameOver() {
                alert(`Game Over! Final Level: ${this.player.level}\nReloading...`);
                location.reload();
            }
            
            render() {
                // Clear canvas
                this.ctx.fillStyle = '#1a1a1a';
                this.ctx.fillRect(0, 0, 1024, 768);
                
                // Draw grid
                this.ctx.strokeStyle = '#333';
                this.ctx.lineWidth = 1;
                for (let x = 0; x < 1024; x += 64) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(x, 0);
                    this.ctx.lineTo(x, 768);
                    this.ctx.stroke();
                }
                for (let y = 0; y < 768; y += 64) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(0, y);
                    this.ctx.lineTo(1024, y);
                    this.ctx.stroke();
                }
                
                // Render particles (behind everything)
                this.particles.forEach(particle => {
                    const alpha = particle.life / particle.maxLife;
                    this.ctx.globalAlpha = alpha;
                    this.ctx.fillStyle = particle.color;
                    this.ctx.beginPath();
                    this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                    this.ctx.fill();
                    this.ctx.globalAlpha = 1;
                });

                // Render game objects
                this.bullets.forEach(bullet => bullet.render(this.ctx));
                this.enemies.forEach(enemy => enemy.render(this.ctx));
                this.player.render(this.ctx);
                
                // Draw crosshair
                const crosshairSize = 10;
                this.ctx.strokeStyle = '#ffffff';
                this.ctx.lineWidth = 2;
                this.ctx.beginPath();
                this.ctx.moveTo(this.input.mousePos.x - crosshairSize, this.input.mousePos.y);
                this.ctx.lineTo(this.input.mousePos.x + crosshairSize, this.input.mousePos.y);
                this.ctx.moveTo(this.input.mousePos.x, this.input.mousePos.y - crosshairSize);
                this.ctx.lineTo(this.input.mousePos.x, this.input.mousePos.y + crosshairSize);
                this.ctx.stroke();
                
                // UI
                this.ctx.fillStyle = '#ffffff';
                this.ctx.font = '16px monospace';
                this.ctx.fillText(`Health: ${this.player.health}`, 20, 40);
                this.ctx.fillText(`Enemies: ${this.enemies.length}`, 20, 60);
                this.ctx.fillText(`Auto-attack: ${this.player.autoAttack ? 'ON' : 'OFF'}`, 20, 80);
            }
            
            gameLoop(currentTime) {
                const deltaTime = (currentTime - this.lastTime) / 1000;
                this.lastTime = currentTime;
                
                // Calculate FPS
                this.frameCount++;
                this.fpsTimer += deltaTime;
                if (this.fpsTimer >= 1) {
                    this.fps = this.frameCount;
                    this.frameCount = 0;
                    this.fpsTimer = 0;
                    document.getElementById('fps').textContent = `FPS: ${this.fps}`;
                }
                
                this.update(deltaTime);
                this.render();
                
                requestAnimationFrame((time) => this.gameLoop(time));
            }
            
            start() {
                this.lastTime = performance.now();
                this.gameLoop(this.lastTime);
            }
        }

        // Start the game
        const game = new Game();
        game.start();
    </script>
</body>
</html>
