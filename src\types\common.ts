/**
 * Common type definitions to improve type safety across the codebase
 */

// Utility types for better type safety
export type NonNullable<T> = T extends null | undefined ? never : T;
export type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

// Branded types for better type safety
export type Brand<T, B> = T & { __brand: B };
export type EntityId = Brand<string, 'EntityId'>;
export type AssetId = Brand<string, 'AssetId'>;
export type SceneId = Brand<string, 'SceneId'>;
export type Timestamp = Brand<number, 'Timestamp'>;
export type Milliseconds = Brand<number, 'Milliseconds'>;

// Common data structures
export interface Point2D {
  readonly x: number;
  readonly y: number;
}

export interface Size2D {
  readonly width: number;
  readonly height: number;
}

export interface Rectangle extends Point2D, Size2D {}

export interface Circle extends Point2D {
  readonly radius: number;
}

// Color types
export type HexColor = Brand<string, 'HexColor'>;
export type RGBColor = {
  readonly r: number; // 0-255
  readonly g: number; // 0-255
  readonly b: number; // 0-255
};
export type RGBAColor = RGBColor & {
  readonly a: number; // 0-1
};
export type HSLColor = {
  readonly h: number; // 0-360
  readonly s: number; // 0-100
  readonly l: number; // 0-100
};

// Event system types
export interface GameEvent<T = unknown> {
  readonly type: string;
  readonly timestamp: Timestamp;
  readonly data: T;
}

export type EventHandler<T = unknown> = (event: GameEvent<T>) => void;
export type EventUnsubscribe = () => void;

// Configuration types
export interface BaseConfig {
  readonly enabled: boolean;
  readonly debug?: boolean;
}

export interface PerformanceConfig extends BaseConfig {
  readonly maxFPS: number;
  readonly targetFrameTime: Milliseconds;
  readonly enableProfiling: boolean;
}

export interface RenderConfig extends BaseConfig {
  readonly width: number;
  readonly height: number;
  readonly pixelRatio: number;
  readonly antialias: boolean;
}

// Asset types
export type AssetType = 'image' | 'audio' | 'json' | 'text' | 'binary';

export interface AssetMetadata {
  readonly id: AssetId;
  readonly type: AssetType;
  readonly url: string;
  readonly size: number;
  readonly lastModified: Timestamp;
}

export interface LoadedAsset<T = unknown> extends AssetMetadata {
  readonly data: T;
  readonly loadTime: Milliseconds;
}

// Input types
export type KeyCode = string;
export type MouseButton = 0 | 1 | 2; // left, middle, right

export interface InputState {
  readonly keys: ReadonlySet<KeyCode>;
  readonly mouseButtons: ReadonlySet<MouseButton>;
  readonly mousePosition: Point2D;
  readonly mouseDelta: Point2D;
}

// Game state types
export type GameStateType = 'loading' | 'menu' | 'playing' | 'paused' | 'gameOver';

export interface GameStateData {
  readonly type: GameStateType;
  readonly timestamp: Timestamp;
  readonly metadata?: Record<string, unknown>;
}

// Entity types
export interface EntityTransform {
  readonly position: Point2D;
  readonly rotation: number; // radians
  readonly scale: Point2D;
}

export interface EntityBounds {
  readonly min: Point2D;
  readonly max: Point2D;
}

export interface EntityStats {
  readonly health: number;
  readonly maxHealth: number;
  readonly mana?: number;
  readonly maxMana?: number;
  readonly damage: number;
  readonly speed: number;
  readonly defense?: number;
  readonly criticalChance?: number;
  readonly criticalMultiplier?: number;
}

// Component types for ECS
export interface Component {
  readonly type: string;
}

export interface TransformComponent extends Component {
  readonly type: 'transform';
  transform: EntityTransform;
}

export interface RenderComponent extends Component {
  readonly type: 'render';
  readonly visible: boolean;
  readonly layer: number;
  readonly color: RGBAColor;
}

export interface PhysicsComponent extends Component {
  readonly type: 'physics';
  readonly velocity: Point2D;
  readonly acceleration: Point2D;
  readonly mass: number;
  readonly friction: number;
}

export interface CollisionComponent extends Component {
  readonly type: 'collision';
  readonly bounds: Circle | Rectangle;
  readonly solid: boolean;
  readonly trigger: boolean;
}

// System types
export interface System {
  readonly name: string;
  readonly priority: number;
  readonly enabled: boolean;
  update(deltaTime: Milliseconds): void;
  destroy(): void;
}

// Error types
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

export interface GameError {
  readonly id: string;
  readonly message: string;
  readonly severity: ErrorSeverity;
  readonly timestamp: Timestamp;
  readonly stack?: string;
  readonly context?: Record<string, unknown>;
}

// Validation types
export interface ValidationRule<T> {
  readonly name: string;
  readonly validate: (value: T) => boolean;
  readonly message: string;
}

export interface ValidationResult<T> {
  readonly isValid: boolean;
  readonly value?: T;
  readonly errors: readonly string[];
}

// Utility function types
export type Predicate<T> = (value: T) => boolean;
export type Mapper<T, U> = (value: T) => U;
export type Reducer<T, U> = (accumulator: U, current: T) => U;
export type Comparator<T> = (a: T, b: T) => number;

// Promise types
export type AsyncResult<T, E = Error> = Promise<{ success: true; data: T } | { success: false; error: E }>;
export type MaybePromise<T> = T | Promise<T>;

// Collection types
export type ReadonlyRecord<K extends string | number | symbol, V> = {
  readonly [P in K]: V;
};

export interface ReadonlyMap<K, V> {
  readonly size: number;
  has(key: K): boolean;
  get(key: K): V | undefined;
  keys(): IterableIterator<K>;
  values(): IterableIterator<V>;
  entries(): IterableIterator<[K, V]>;
  forEach(callbackfn: (value: V, key: K, map: ReadonlyMap<K, V>) => void): void;
}

export interface ReadonlySet<T> {
  readonly size: number;
  has(value: T): boolean;
  keys(): IterableIterator<T>;
  values(): IterableIterator<T>;
  entries(): IterableIterator<[T, T]>;
  forEach(callbackfn: (value: T, value2: T, set: ReadonlySet<T>) => void): void;
}

// Type guards
export function isString(value: unknown): value is string {
  return typeof value === 'string';
}

export function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value) && isFinite(value);
}

export function isBoolean(value: unknown): value is boolean {
  return typeof value === 'boolean';
}

export function isObject(value: unknown): value is Record<string, unknown> {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}

export function isArray<T>(value: unknown): value is T[] {
  return Array.isArray(value);
}

export function isFunction(value: unknown): value is Function {
  return typeof value === 'function';
}

export function isDefined<T>(value: T | undefined | null): value is T {
  return value !== undefined && value !== null;
}

export function hasProperty<K extends string>(
  obj: unknown,
  key: K
): obj is Record<K, unknown> {
  return isObject(obj) && key in obj;
}

// Type assertion helpers
export function assertString(value: unknown, message?: string): asserts value is string {
  if (!isString(value)) {
    throw new TypeError(message || `Expected string, got ${typeof value}`);
  }
}

export function assertNumber(value: unknown, message?: string): asserts value is number {
  if (!isNumber(value)) {
    throw new TypeError(message || `Expected number, got ${typeof value}`);
  }
}

export function assertObject(value: unknown, message?: string): asserts value is Record<string, unknown> {
  if (!isObject(value)) {
    throw new TypeError(message || `Expected object, got ${typeof value}`);
  }
}

export function assertDefined<T>(value: T | undefined | null, message?: string): asserts value is T {
  if (!isDefined(value)) {
    throw new Error(message || 'Value is undefined or null');
  }
}

// Branded type helpers
export function createEntityId(id: string): EntityId {
  return id as EntityId;
}

export function createAssetId(id: string): AssetId {
  return id as AssetId;
}

export function createSceneId(id: string): SceneId {
  return id as SceneId;
}

export function createTimestamp(time: number = Date.now()): Timestamp {
  return time as Timestamp;
}

export function createMilliseconds(ms: number): Milliseconds {
  return ms as Milliseconds;
}

export function createHexColor(color: string): HexColor {
  if (!/^#[0-9A-Fa-f]{6}$/.test(color)) {
    throw new Error(`Invalid hex color: ${color}`);
  }
  return color as HexColor;
}

// Utility type for exhaustive switch statements
export function assertNever(value: never): never {
  throw new Error(`Unexpected value: ${value}`);
}
