# 🚀 Deployment Guide - Ultimate Bullet Hell RPG

This guide provides step-by-step instructions for deploying the game to various platforms and environments.

## 📋 Prerequisites

- Node.js (for build script)
- Web server (Apache, Nginx, or static hosting)
- Modern web browser with ES6+ support

## 🔧 Build Process

### 1. Production Build

Run the build script to create an optimized production version:

```bash
# Using Node.js
node build.js

# Or if you have npm
npm run build
```

This will:
- ✅ Minify HTML, CSS, and JavaScript
- ✅ Optimize TypeScript compilation
- ✅ Create service worker for caching
- ✅ Generate production manifest
- ✅ Create deployment-ready `/dist` folder

### 2. Build Output

The build process creates a `dist/` folder with:

```
dist/
├── index.html              # Landing page with demo links
├── demo.html               # Basic gameplay demo
├── dungeon-demo.html       # Procedural generation demo
├── complete-demo.html      # Full game experience
├── ultimate-demo.html      # Complete with mobile support
├── map-editor.html         # Level editor tool
├── src/                    # Optimized source files
├── docs/                   # Documentation
├── styles.min.css          # Combined and minified CSS
├── module-loader.js        # Production module system
├── sw.js                   # Service worker
├── manifest.json           # Build manifest
└── package.json            # Package information
```

## 🌐 Deployment Options

### Option 1: Static Web Hosting

**Recommended for:** GitHub Pages, Netlify, Vercel, AWS S3

1. **Build the project:**
   ```bash
   node build.js
   ```

2. **Upload the `dist/` folder** to your hosting provider

3. **Configure the web server** (if needed):
   - Set `index.html` as the default document
   - Enable HTTPS for service worker functionality
   - Configure MIME types for `.js` files

**Example for GitHub Pages:**
```bash
# After building
git add dist/
git commit -m "Deploy production build"
git subtree push --prefix dist origin gh-pages
```

### Option 2: Traditional Web Server

**Recommended for:** Apache, Nginx, IIS

1. **Build and copy files:**
   ```bash
   node build.js
   cp -r dist/* /var/www/html/
   ```

2. **Configure Apache (.htaccess):**
   ```apache
   # Enable compression
   <IfModule mod_deflate.c>
       AddOutputFilterByType DEFLATE text/html text/css application/javascript
   </IfModule>
   
   # Set cache headers
   <IfModule mod_expires.c>
       ExpiresActive On
       ExpiresByType text/css "access plus 1 month"
       ExpiresByType application/javascript "access plus 1 month"
       ExpiresByType text/html "access plus 1 hour"
   </IfModule>
   
   # Enable HTTPS redirect
   RewriteEngine On
   RewriteCond %{HTTPS} off
   RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
   ```

3. **Configure Nginx:**
   ```nginx
   server {
       listen 80;
       server_name yourdomain.com;
       root /var/www/html;
       index index.html;
       
       # Enable gzip compression
       gzip on;
       gzip_types text/css application/javascript text/html;
       
       # Cache static assets
       location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
           expires 1M;
           add_header Cache-Control "public, immutable";
       }
       
       # Security headers
       add_header X-Frame-Options "SAMEORIGIN";
       add_header X-Content-Type-Options "nosniff";
   }
   ```

### Option 3: Content Delivery Network (CDN)

**Recommended for:** Global distribution, high performance

1. **Build the project**
2. **Upload to CDN** (CloudFlare, AWS CloudFront, etc.)
3. **Configure caching rules:**
   - HTML files: 1 hour cache
   - CSS/JS files: 1 month cache
   - Images: 1 year cache

### Option 4: Docker Container

**Recommended for:** Containerized deployments

1. **Create Dockerfile:**
   ```dockerfile
   FROM nginx:alpine
   
   # Copy built files
   COPY dist/ /usr/share/nginx/html/
   
   # Copy nginx configuration
   COPY nginx.conf /etc/nginx/nginx.conf
   
   EXPOSE 80
   
   CMD ["nginx", "-g", "daemon off;"]
   ```

2. **Build and run:**
   ```bash
   docker build -t bullet-hell-rpg .
   docker run -p 8080:80 bullet-hell-rpg
   ```

## 🔧 Configuration Options

### Environment Variables

Set these before building for different environments:

```bash
# Development
export NODE_ENV=development
export DEBUG=true

# Production
export NODE_ENV=production
export DEBUG=false
```

### Custom Build Configuration

Modify `build.js` for custom optimization:

```javascript
const config = {
  minifyHTML: true,        // Minify HTML files
  minifyJS: true,          // Minify JavaScript
  minifyCSS: true,         // Minify CSS
  optimizeImages: true,    // Optimize images
  generateSourceMaps: false, // Generate source maps
  compressionLevel: 9      // Compression level (1-9)
};
```

## 📊 Performance Optimization

### 1. Enable Compression

**Gzip/Brotli compression** can reduce file sizes by 60-80%:

```bash
# Check if compression is working
curl -H "Accept-Encoding: gzip" -I https://yourdomain.com/
```

### 2. Configure Caching

**Optimal cache headers:**
- HTML: `Cache-Control: max-age=3600` (1 hour)
- CSS/JS: `Cache-Control: max-age=2592000` (30 days)
- Images: `Cache-Control: max-age=31536000` (1 year)

### 3. Use HTTP/2

Enable HTTP/2 for better performance:
- Multiplexing reduces latency
- Server push for critical resources
- Header compression

### 4. Content Security Policy

Add CSP headers for security:

```html
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; 
               script-src 'self' 'unsafe-inline'; 
               style-src 'self' 'unsafe-inline';">
```

## 🔍 Testing Deployment

### 1. Local Testing

Test the production build locally:

```bash
# Using Python
cd dist && python -m http.server 8000

# Using Node.js
cd dist && npx serve .

# Using PHP
cd dist && php -S localhost:8000
```

### 2. Performance Testing

Use these tools to verify performance:

- **Lighthouse**: Audit performance, accessibility, SEO
- **WebPageTest**: Detailed performance analysis
- **GTmetrix**: Page speed insights
- **Chrome DevTools**: Network and performance profiling

### 3. Cross-Browser Testing

Test on multiple browsers:
- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

### 4. Mobile Testing

Verify mobile functionality:
- Touch controls work properly
- Responsive design adapts correctly
- Performance is acceptable on mobile devices
- Virtual controls are properly positioned

## 🚨 Troubleshooting

### Common Issues

**1. Module Loading Errors**
```
Solution: Ensure all .ts extensions are changed to .js in production
Check: Module paths are correctly updated in HTML files
```

**2. Service Worker Not Registering**
```
Solution: Serve over HTTPS (required for service workers)
Check: Service worker file is accessible at /sw.js
```

**3. Performance Issues**
```
Solution: Enable compression and caching
Check: Large files are properly optimized
Monitor: Network tab in browser dev tools
```

**4. Mobile Controls Not Working**
```
Solution: Verify touch event listeners are properly bound
Check: Virtual controls are visible and positioned correctly
Test: On actual mobile devices, not just browser emulation
```

### Debug Mode

Enable debug mode for troubleshooting:

```javascript
// Add to HTML files for debugging
window.DEBUG = true;
window.PERFORMANCE_MONITORING = true;
```

## 📈 Monitoring

### 1. Analytics

Add analytics to track usage:

```html
<!-- Google Analytics example -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_TRACKING_ID');
</script>
```

### 2. Error Tracking

Monitor errors in production:

```javascript
// Add to main game files
window.addEventListener('error', (event) => {
  // Send error to monitoring service
  console.error('Game Error:', event.error);
});
```

### 3. Performance Monitoring

Track performance metrics:

```javascript
// Monitor FPS and performance
setInterval(() => {
  const metrics = performanceMonitor.getCurrentMetrics();
  if (metrics.fps < 30) {
    // Alert or log performance issue
  }
}, 5000);
```

## ✅ Deployment Checklist

Before going live:

- [ ] Run production build successfully
- [ ] Test all demo pages work correctly
- [ ] Verify mobile controls function properly
- [ ] Check performance meets targets (60 FPS)
- [ ] Confirm service worker registers correctly
- [ ] Test on multiple browsers and devices
- [ ] Enable compression and caching
- [ ] Configure security headers
- [ ] Set up monitoring and analytics
- [ ] Test error handling and recovery
- [ ] Verify all documentation is accessible

## 🎉 Go Live!

Once everything is tested and configured:

1. **Deploy to production environment**
2. **Monitor initial performance and errors**
3. **Gather user feedback**
4. **Iterate and improve based on real usage**

Your Ultimate Bullet Hell RPG is now ready for the world! 🚀
