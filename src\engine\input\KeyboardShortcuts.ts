/**
 * Keyboard shortcuts and hotkey management system
 * Provides a centralized way to handle keyboard shortcuts for common game actions
 */

import { InputManager } from './InputManager';

export interface ShortcutAction {
  readonly name: string;
  readonly description: string;
  readonly keys: readonly string[];
  readonly callback: () => void;
  readonly enabled: boolean;
  readonly category: ShortcutCategory;
}

export type ShortcutCategory = 'game' | 'debug' | 'ui' | 'system';

export interface ShortcutConfig {
  readonly [actionName: string]: {
    readonly keys: readonly string[];
    readonly description: string;
    readonly category: ShortcutCategory;
    readonly enabled?: boolean;
  };
}

/**
 * Manages keyboard shortcuts and hotkeys for the game
 */
export class KeyboardShortcuts {
  private inputManager: InputManager;
  private shortcuts: Map<string, ShortcutAction> = new Map();
  private keyToActions: Map<string, string[]> = new Map();
  private enabled: boolean = true;

  // Default shortcut configuration
  private static readonly DEFAULT_SHORTCUTS: ShortcutConfig = {
    // Game controls
    pause: {
      keys: ['Escape', 'KeyP'],
      description: 'Pause/Resume game',
      category: 'game'
    },
    restart: {
      keys: ['KeyR'],
      description: 'Restart current level',
      category: 'game'
    },
    fullscreen: {
      keys: ['F11', 'KeyF'],
      description: 'Toggle fullscreen',
      category: 'game'
    },
    screenshot: {
      keys: ['F12', 'PrintScreen'],
      description: 'Take screenshot',
      category: 'game'
    },

    // Debug controls
    toggleDebug: {
      keys: ['F1'],
      description: 'Toggle debug mode',
      category: 'debug'
    },
    toggleFPS: {
      keys: ['F2'],
      description: 'Toggle FPS display',
      category: 'debug'
    },
    toggleColliders: {
      keys: ['F3'],
      description: 'Toggle collision visualization',
      category: 'debug'
    },
    toggleGrid: {
      keys: ['F4'],
      description: 'Toggle spatial grid visualization',
      category: 'debug'
    },
    toggleProfiling: {
      keys: ['F5'],
      description: 'Toggle performance profiling',
      category: 'debug'
    },
    slowMotion: {
      keys: ['KeyT'],
      description: 'Toggle slow motion',
      category: 'debug'
    },
    godMode: {
      keys: ['KeyG'],
      description: 'Toggle god mode',
      category: 'debug'
    },

    // UI controls
    toggleInventory: {
      keys: ['KeyI', 'Tab'],
      description: 'Toggle inventory',
      category: 'ui'
    },
    toggleSkillTree: {
      keys: ['KeyK'],
      description: 'Toggle skill tree',
      category: 'ui'
    },
    toggleMap: {
      keys: ['KeyM'],
      description: 'Toggle map',
      category: 'ui'
    },
    toggleHelp: {
      keys: ['F1', 'KeyH'],
      description: 'Show help/controls',
      category: 'ui'
    },
    toggleSettings: {
      keys: ['KeyO'],
      description: 'Open settings',
      category: 'ui'
    },

    // System controls
    quickSave: {
      keys: ['F5'],
      description: 'Quick save',
      category: 'system'
    },
    quickLoad: {
      keys: ['F9'],
      description: 'Quick load',
      category: 'system'
    },
    exitGame: {
      keys: ['Alt+F4', 'Ctrl+KeyQ'],
      description: 'Exit game',
      category: 'system'
    }
  };

  constructor(inputManager: InputManager) {
    this.inputManager = inputManager;
    this.initializeDefaultShortcuts();
  }

  /**
   * Initialize default shortcuts
   */
  private initializeDefaultShortcuts(): void {
    for (const [actionName, config] of Object.entries(KeyboardShortcuts.DEFAULT_SHORTCUTS)) {
      this.registerShortcut(actionName, {
        name: actionName,
        description: config.description,
        keys: config.keys,
        callback: () => this.handleDefaultAction(actionName),
        enabled: config.enabled ?? true,
        category: config.category
      });
    }
  }

  /**
   * Register a new keyboard shortcut
   */
  registerShortcut(actionName: string, action: ShortcutAction): void {
    // Remove existing shortcut if it exists
    this.unregisterShortcut(actionName);

    // Register new shortcut
    this.shortcuts.set(actionName, action);

    // Map keys to actions
    for (const key of action.keys) {
      if (!this.keyToActions.has(key)) {
        this.keyToActions.set(key, []);
      }
      this.keyToActions.get(key)!.push(actionName);
    }
  }

  /**
   * Unregister a keyboard shortcut
   */
  unregisterShortcut(actionName: string): void {
    const action = this.shortcuts.get(actionName);
    if (!action) return;

    // Remove from key mappings
    for (const key of action.keys) {
      const actions = this.keyToActions.get(key);
      if (actions) {
        const index = actions.indexOf(actionName);
        if (index !== -1) {
          actions.splice(index, 1);
        }
        if (actions.length === 0) {
          this.keyToActions.delete(key);
        }
      }
    }

    // Remove shortcut
    this.shortcuts.delete(actionName);
  }

  /**
   * Update shortcuts (call this in the game loop)
   */
  update(): void {
    if (!this.enabled) return;

    // Check for pressed keys
    for (const [key, actionNames] of this.keyToActions.entries()) {
      if (this.inputManager.isKeyPressed(key)) {
        for (const actionName of actionNames) {
          const action = this.shortcuts.get(actionName);
          if (action && action.enabled) {
            try {
              action.callback();
            } catch (error) {
              console.error(`Error executing shortcut ${actionName}:`, error);
            }
          }
        }
      }
    }
  }

  /**
   * Handle default actions
   */
  private handleDefaultAction(actionName: string): void {
    switch (actionName) {
      case 'pause':
        this.dispatchGameEvent('pause');
        break;
      case 'restart':
        this.dispatchGameEvent('restart');
        break;
      case 'fullscreen':
        this.toggleFullscreen();
        break;
      case 'screenshot':
        this.takeScreenshot();
        break;
      case 'toggleDebug':
        this.dispatchGameEvent('toggleDebug');
        break;
      case 'toggleFPS':
        this.dispatchGameEvent('toggleFPS');
        break;
      case 'toggleColliders':
        this.dispatchGameEvent('toggleColliders');
        break;
      case 'toggleGrid':
        this.dispatchGameEvent('toggleGrid');
        break;
      case 'toggleProfiling':
        this.dispatchGameEvent('toggleProfiling');
        break;
      case 'slowMotion':
        this.dispatchGameEvent('toggleSlowMotion');
        break;
      case 'godMode':
        this.dispatchGameEvent('toggleGodMode');
        break;
      case 'toggleInventory':
        this.dispatchGameEvent('toggleInventory');
        break;
      case 'toggleSkillTree':
        this.dispatchGameEvent('toggleSkillTree');
        break;
      case 'toggleMap':
        this.dispatchGameEvent('toggleMap');
        break;
      case 'toggleHelp':
        this.dispatchGameEvent('toggleHelp');
        break;
      case 'toggleSettings':
        this.dispatchGameEvent('toggleSettings');
        break;
      case 'quickSave':
        this.dispatchGameEvent('quickSave');
        break;
      case 'quickLoad':
        this.dispatchGameEvent('quickLoad');
        break;
      case 'exitGame':
        this.dispatchGameEvent('exitGame');
        break;
      default:
        console.warn(`Unknown shortcut action: ${actionName}`);
    }
  }

  /**
   * Dispatch a game event
   */
  private dispatchGameEvent(eventType: string): void {
    const event = new CustomEvent(`game:${eventType}`, {
      detail: { timestamp: Date.now() }
    });
    window.dispatchEvent(event);
  }

  /**
   * Toggle fullscreen mode
   */
  private toggleFullscreen(): void {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch(err => {
        console.warn('Could not enter fullscreen:', err);
      });
    } else {
      document.exitFullscreen().catch(err => {
        console.warn('Could not exit fullscreen:', err);
      });
    }
  }

  /**
   * Take a screenshot of the game canvas
   */
  private takeScreenshot(): void {
    const canvas = document.querySelector('canvas');
    if (canvas) {
      const link = document.createElement('a');
      link.download = `screenshot_${Date.now()}.png`;
      link.href = canvas.toDataURL();
      link.click();
    }
  }

  /**
   * Enable or disable shortcuts
   */
  setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  /**
   * Check if shortcuts are enabled
   */
  isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * Enable or disable a specific shortcut
   */
  setShortcutEnabled(actionName: string, enabled: boolean): void {
    const action = this.shortcuts.get(actionName);
    if (action) {
      this.shortcuts.set(actionName, { ...action, enabled });
    }
  }

  /**
   * Get all registered shortcuts
   */
  getShortcuts(): ReadonlyMap<string, ShortcutAction> {
    return this.shortcuts;
  }

  /**
   * Get shortcuts by category
   */
  getShortcutsByCategory(category: ShortcutCategory): ShortcutAction[] {
    return Array.from(this.shortcuts.values()).filter(action => action.category === category);
  }

  /**
   * Get help text for all shortcuts
   */
  getHelpText(): string {
    const categories: Record<ShortcutCategory, ShortcutAction[]> = {
      game: [],
      debug: [],
      ui: [],
      system: []
    };

    // Group shortcuts by category
    for (const action of this.shortcuts.values()) {
      if (action.enabled) {
        categories[action.category].push(action);
      }
    }

    let helpText = 'Keyboard Shortcuts:\n\n';

    for (const [category, actions] of Object.entries(categories)) {
      if (actions.length === 0) continue;

      helpText += `${category.toUpperCase()}:\n`;
      for (const action of actions) {
        const keys = action.keys.join(' or ');
        helpText += `  ${keys}: ${action.description}\n`;
      }
      helpText += '\n';
    }

    return helpText;
  }

  /**
   * Load shortcuts from configuration
   */
  loadFromConfig(config: ShortcutConfig): void {
    for (const [actionName, shortcutConfig] of Object.entries(config)) {
      this.registerShortcut(actionName, {
        name: actionName,
        description: shortcutConfig.description,
        keys: shortcutConfig.keys,
        callback: () => this.handleDefaultAction(actionName),
        enabled: shortcutConfig.enabled ?? true,
        category: shortcutConfig.category
      });
    }
  }

  /**
   * Export current shortcuts configuration
   */
  exportConfig(): ShortcutConfig {
    const config: ShortcutConfig = {};
    
    for (const [actionName, action] of this.shortcuts.entries()) {
      config[actionName] = {
        keys: action.keys,
        description: action.description,
        category: action.category,
        enabled: action.enabled
      };
    }

    return config;
  }
}
