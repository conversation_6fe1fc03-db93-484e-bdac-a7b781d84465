/**
 * Skill tree user interface for character progression
 */

import { CanvasRenderer } from '@/engine/rendering/CanvasRenderer';
import { InputManager } from '@/engine/input/InputManager';
import { Vector2 } from '@/utils/math/Vector2';
import { ProgressionSystem, ProgressionData, SkillTree, Skill } from '@/game/systems/ProgressionSystem';

export interface SkillTreeUIConfig {
  nodeSize: number;
  nodeSpacing: number;
  treeSpacing: number;
  colors: {
    background: string;
    available: string;
    learned: string;
    locked: string;
    canLearn: string;
    connection: string;
    text: string;
    highlight: string;
  };
}

export class SkillTreeUI {
  private config: SkillTreeUIConfig;
  private progressionSystem: ProgressionSystem;
  private isVisible: boolean = false;
  private selectedTree: string = 'combat';
  private hoveredSkill: string | null = null;
  private camera: Vector2 = new Vector2(0, 0);
  private skillPositions: Map<string, Vector2> = new Map();

  constructor(progressionSystem: ProgressionSystem, config: Partial<SkillTreeUIConfig> = {}) {
    this.progressionSystem = progressionSystem;
    this.config = {
      nodeSize: 60,
      nodeSpacing: 100,
      treeSpacing: 150,
      colors: {
        background: 'rgba(0, 0, 0, 0.9)',
        available: '#666666',
        learned: '#4CAF50',
        locked: '#333333',
        canLearn: '#FFD700',
        connection: '#888888',
        text: '#ffffff',
        highlight: '#ffffff'
      },
      ...config
    };
  }

  show(): void {
    this.isVisible = true;
    this.calculateSkillPositions();
  }

  hide(): void {
    this.isVisible = false;
  }

  toggle(): void {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }

  update(deltaTime: number, inputManager: InputManager, character: ProgressionData): void {
    if (!this.isVisible) return;

    // Handle input
    this.handleInput(inputManager, character);
  }

  private handleInput(inputManager: InputManager, character: ProgressionData): void {
    // Tab switching
    if (inputManager.isKeyPressed('Digit1')) {
      this.selectedTree = 'combat';
      this.calculateSkillPositions();
    } else if (inputManager.isKeyPressed('Digit2')) {
      this.selectedTree = 'magic';
      this.calculateSkillPositions();
    } else if (inputManager.isKeyPressed('Digit3')) {
      this.selectedTree = 'utility';
      this.calculateSkillPositions();
    }

    // Mouse interaction
    const mousePos = inputManager.getMousePosition();
    this.hoveredSkill = this.getSkillAtPosition(mousePos);

    // Skill learning
    if (inputManager.isMouseButtonPressed(0) && this.hoveredSkill) {
      if (this.progressionSystem.canLearnSkill(character, this.hoveredSkill)) {
        this.progressionSystem.learnSkill(character, this.hoveredSkill);
      }
    }

    // Camera movement with WASD
    const cameraSpeed = 300;
    const movement = inputManager.getMovementVector();
    this.camera = this.camera.add(movement.multiply(cameraSpeed / 60)); // Assuming 60 FPS
  }

  private getSkillAtPosition(mousePos: Vector2): string | null {
    for (const [skillId, position] of this.skillPositions) {
      const screenPos = this.worldToScreen(position);
      const distance = mousePos.distance(screenPos);
      
      if (distance <= this.config.nodeSize / 2) {
        return skillId;
      }
    }
    return null;
  }

  private worldToScreen(worldPos: Vector2): Vector2 {
    return new Vector2(
      worldPos.x - this.camera.x,
      worldPos.y - this.camera.y
    );
  }

  private calculateSkillPositions(): void {
    this.skillPositions.clear();
    
    const tree = this.progressionSystem.getSkillTree(this.selectedTree);
    if (!tree) return;

    const centerX = 512; // Center of screen
    const centerY = 384;

    for (const skill of tree.skills) {
      const x = centerX + (skill.position.x - 1) * this.config.nodeSpacing;
      const y = centerY + (skill.position.y - 1) * this.config.nodeSpacing;
      this.skillPositions.set(skill.id, new Vector2(x, y));
    }
  }

  render(renderer: CanvasRenderer, character: ProgressionData): void {
    if (!this.isVisible) return;

    const { width, height } = renderer.getCanvasSize();

    // Draw background
    renderer.setGlobalAlpha(0.95);
    renderer.drawRect(0, 0, width, height, this.config.colors.background);
    renderer.setGlobalAlpha(1);

    // Draw header
    this.renderHeader(renderer, character);

    // Draw tree tabs
    this.renderTreeTabs(renderer);

    // Draw current tree
    this.renderSkillTree(renderer, character);

    // Draw skill tooltip
    if (this.hoveredSkill) {
      this.renderSkillTooltip(renderer, this.hoveredSkill, character);
    }
  }

  private renderHeader(renderer: CanvasRenderer, character: ProgressionData): void {
    const { width } = renderer.getCanvasSize();
    
    // Title
    renderer.drawText(
      'Character Progression',
      width / 2 - 100,
      30,
      'bold 24px monospace',
      this.config.colors.text
    );

    // Character info
    const level = `Level: ${character.level}`;
    const xp = `XP: ${character.experience}/${this.progressionSystem.getExperienceForLevel(character.level + 1)}`;
    const skillPoints = `Skill Points: ${character.skillPoints}`;

    renderer.drawText(level, 20, 60, '16px monospace', this.config.colors.text);
    renderer.drawText(xp, 20, 80, '16px monospace', this.config.colors.text);
    renderer.drawText(skillPoints, 20, 100, '16px monospace', this.config.colors.text);

    // XP bar
    const barWidth = 200;
    const barHeight = 10;
    const barX = 20;
    const barY = 110;
    
    renderer.drawRect(barX, barY, barWidth, barHeight, '#333333');
    
    const progress = this.progressionSystem.getProgressToNextLevel(character);
    renderer.drawRect(barX, barY, barWidth * progress, barHeight, '#4CAF50');
  }

  private renderTreeTabs(renderer: CanvasRenderer): void {
    const trees = this.progressionSystem.getSkillTrees();
    const tabWidth = 120;
    const tabHeight = 40;
    const startX = 50;
    const y = 150;

    for (let i = 0; i < trees.length; i++) {
      const tree = trees[i];
      const x = startX + i * (tabWidth + 10);
      const isSelected = tree.id === this.selectedTree;

      // Tab background
      const bgColor = isSelected ? tree.color : '#444444';
      renderer.drawRect(x, y, tabWidth, tabHeight, bgColor);

      // Tab border
      renderer.drawRectOutline(x, y, tabWidth, tabHeight, this.config.colors.text, 2);

      // Tab text
      const textColor = isSelected ? '#000000' : this.config.colors.text;
      renderer.drawText(
        tree.name,
        x + 10,
        y + 15,
        '16px monospace',
        textColor
      );

      // Hotkey hint
      renderer.drawText(
        `(${i + 1})`,
        x + 10,
        y + 30,
        '12px monospace',
        textColor
      );
    }
  }

  private renderSkillTree(renderer: CanvasRenderer, character: ProgressionData): void {
    const tree = this.progressionSystem.getSkillTree(this.selectedTree);
    if (!tree) return;

    // Draw connections first
    this.renderSkillConnections(renderer, tree, character);

    // Draw skill nodes
    for (const skill of tree.skills) {
      this.renderSkillNode(renderer, skill, character);
    }
  }

  private renderSkillConnections(renderer: CanvasRenderer, tree: SkillTree, character: ProgressionData): void {
    for (const skill of tree.skills) {
      const skillPos = this.skillPositions.get(skill.id);
      if (!skillPos) continue;

      const screenPos = this.worldToScreen(skillPos);

      for (const prereqId of skill.prerequisites) {
        const prereqPos = this.skillPositions.get(prereqId);
        if (!prereqPos) continue;

        const prereqScreenPos = this.worldToScreen(prereqPos);

        // Connection color based on prerequisite status
        const prereqLevel = this.progressionSystem.getSkillLevel(character, prereqId);
        const connectionColor = prereqLevel > 0 ? this.config.colors.learned : this.config.colors.locked;

        renderer.drawLine(
          prereqScreenPos.x,
          prereqScreenPos.y,
          screenPos.x,
          screenPos.y,
          connectionColor,
          3
        );
      }
    }
  }

  private renderSkillNode(renderer: CanvasRenderer, skill: Skill, character: ProgressionData): void {
    const position = this.skillPositions.get(skill.id);
    if (!position) return;

    const screenPos = this.worldToScreen(position);
    const currentLevel = this.progressionSystem.getSkillLevel(character, skill.id);
    const canLearn = this.progressionSystem.canLearnSkill(character, skill.id);
    const isHovered = this.hoveredSkill === skill.id;

    // Determine node color
    let nodeColor = this.config.colors.locked;
    if (currentLevel > 0) {
      nodeColor = this.config.colors.learned;
    } else if (canLearn) {
      nodeColor = this.config.colors.canLearn;
    } else {
      // Check if prerequisites are met
      const prereqsMet = skill.prerequisites.every(prereqId => 
        this.progressionSystem.getSkillLevel(character, prereqId) > 0
      );
      if (prereqsMet) {
        nodeColor = this.config.colors.available;
      }
    }

    // Draw node
    const nodeSize = this.config.nodeSize;
    renderer.drawCircle(screenPos.x, screenPos.y, nodeSize / 2, nodeColor);

    // Draw border
    const borderColor = isHovered ? this.config.colors.highlight : this.config.colors.text;
    const borderWidth = isHovered ? 3 : 2;
    renderer.drawCircleOutline(screenPos.x, screenPos.y, nodeSize / 2, borderColor, borderWidth);

    // Draw icon
    renderer.drawText(
      skill.icon,
      screenPos.x - 12,
      screenPos.y - 12,
      '24px monospace',
      '#000000'
    );

    // Draw level indicator
    if (currentLevel > 0) {
      const levelText = `${currentLevel}/${skill.maxLevel}`;
      renderer.drawText(
        levelText,
        screenPos.x - 15,
        screenPos.y + nodeSize / 2 + 15,
        '12px monospace',
        this.config.colors.text
      );
    }

    // Draw skill name
    renderer.drawText(
      skill.name,
      screenPos.x - skill.name.length * 3,
      screenPos.y - nodeSize / 2 - 10,
      '12px monospace',
      this.config.colors.text
    );
  }

  private renderSkillTooltip(renderer: CanvasRenderer, skillId: string, character: ProgressionData): void {
    const skill = this.progressionSystem.getSkill(skillId);
    if (!skill) return;

    const mousePos = new Vector2(500, 300); // Fixed position for now
    const tooltipWidth = 300;
    const tooltipHeight = 150;

    // Background
    renderer.setGlobalAlpha(0.95);
    renderer.drawRect(mousePos.x, mousePos.y, tooltipWidth, tooltipHeight, '#222222');
    renderer.setGlobalAlpha(1);

    // Border
    renderer.drawRectOutline(mousePos.x, mousePos.y, tooltipWidth, tooltipHeight, this.config.colors.text, 2);

    let y = mousePos.y + 20;
    const x = mousePos.x + 10;

    // Skill name
    renderer.drawText(skill.name, x, y, 'bold 16px monospace', this.config.colors.text);
    y += 25;

    // Current level
    const currentLevel = this.progressionSystem.getSkillLevel(character, skillId);
    renderer.drawText(`Level: ${currentLevel}/${skill.maxLevel}`, x, y, '14px monospace', this.config.colors.text);
    y += 20;

    // Description
    renderer.drawText(skill.description, x, y, '12px monospace', this.config.colors.text);
    y += 20;

    // Cost
    if (currentLevel < skill.maxLevel) {
      const cost = skill.cost(currentLevel + 1);
      const costText = `Cost: ${cost} skill points`;
      const costColor = character.skillPoints >= cost ? this.config.colors.canLearn : '#ff6b6b';
      renderer.drawText(costText, x, y, '12px monospace', costColor);
      y += 20;
    }

    // Prerequisites
    if (skill.prerequisites.length > 0) {
      renderer.drawText('Prerequisites:', x, y, '12px monospace', this.config.colors.text);
      y += 15;
      
      for (const prereqId of skill.prerequisites) {
        const prereqSkill = this.progressionSystem.getSkill(prereqId);
        if (prereqSkill) {
          const prereqLevel = this.progressionSystem.getSkillLevel(character, prereqId);
          const prereqColor = prereqLevel > 0 ? this.config.colors.learned : '#ff6b6b';
          renderer.drawText(`• ${prereqSkill.name}`, x + 10, y, '11px monospace', prereqColor);
          y += 15;
        }
      }
    }
  }

  // Getters
  isOpen(): boolean {
    return this.isVisible;
  }

  getSelectedTree(): string {
    return this.selectedTree;
  }
}
