{"typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": true, "source.organizeImports": true}, "files.associations": {"*.ts": "typescript", "*.js": "javascript"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/coverage": true, "**/.git": true}, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/coverage": true}, "emmet.includeLanguages": {"typescript": "html"}, "debug.javascript.autoAttachFilter": "onlyWithFlag", "eslint.workingDirectories": ["."], "eslint.validate": ["javascript", "typescript"], "vitest.enable": true, "vitest.commandLine": "npm run test", "git.ignoreLimitWarning": true, "html.format.indentInnerHtml": true, "html.format.wrapLineLength": 120, "css.validate": true, "less.validate": true, "scss.validate": true, "javascript.preferences.quoteStyle": "single", "typescript.preferences.quoteStyle": "single", "editor.rulers": [80, 120], "editor.wordWrap": "wordWrapColumn", "editor.wordWrapColumn": 120, "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "files.trimFinalNewlines": true}